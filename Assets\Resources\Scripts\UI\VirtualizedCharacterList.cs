using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using System.Collections;

public class VirtualizedCharacterList : MonoBehaviour
{
    [Header("Viewport Configuration")]
    public ScrollRect scrollRect;
    public RectTransform content;
    public RectTransform viewport;
    
    [Header("Virtualization Settings")]
    public int visibleItemCount = 12;
    public float itemHeight = 0.5f; // matches CharConfUI ySpacing
    public int bufferItems = 2; // extra items above/below viewport
    
    [Header("References")]
    public GameObject characterPrefab; // CharacterValues prefab
    public Transform poolParent; // parent for pooled items
    
    // Character data
    private List<BattleCharacter> characters;
    private ConfigsHandler configsHandler;
    
    // Object pooling
    private Queue<VirtualizedCharacterItem> pooledItems;
    private List<VirtualizedCharacterItem> activeItems;
    private Dictionary<int, VirtualizedCharacterItem> indexToItemMap;
    
    // Viewport state
    private int firstVisibleIndex = 0;
    private int lastVisibleIndex = 0;
    private float contentHeight;
    private bool isInitialized = false;
    
    // State preservation
    private BattleCharacter selectedCharacter;
    private Dictionary<string, object> characterStates; // preserve UI states
    
    void Awake()
    {
        pooledItems = new Queue<VirtualizedCharacterItem>();
        activeItems = new List<VirtualizedCharacterItem>();
        indexToItemMap = new Dictionary<int, VirtualizedCharacterItem>();
        characterStates = new Dictionary<string, object>();
        
        // Initialize pool parent if not set
        if (poolParent == null)
        {
            GameObject poolObj = new GameObject("CharacterUIPool");
            poolObj.transform.SetParent(transform);
            poolObj.SetActive(false);
            poolParent = poolObj.transform;
        }
    }
    
    void Start()
    {
        // Find required components if not assigned
        if (scrollRect == null)
            scrollRect = GetComponent<ScrollRect>();
        if (content == null)
            content = scrollRect.content;
        if (viewport == null)
            viewport = scrollRect.viewport;
        if (characterPrefab == null)
            characterPrefab = Resources.Load<GameObject>("Prefabs/CharacterValues");
            
        // Setup scroll rect callback
        scrollRect.onValueChanged.AddListener(OnScrollValueChanged);
        
        // Initialize the system
        StartCoroutine(InitializeAfterFrame());
    }
    
    IEnumerator InitializeAfterFrame()
    {
        yield return null; // wait one frame for all components to initialize
        
        // Find ConfigsHandler
        configsHandler = FindObjectOfType<ConfigsHandler>();
        if (configsHandler != null)
        {
            characters = configsHandler.characters;
            InitializeVirtualizedList();
        }
        else
        {
            Debug.LogError("VirtualizedCharacterList: ConfigsHandler not found!");
        }
    }
    
    public void InitializeVirtualizedList()
    {
        if (characters == null || characters.Count == 0)
        {
            Debug.LogWarning("VirtualizedCharacterList: No characters to display");
            return;
        }
        
        // Calculate content height based on character count
        contentHeight = characters.Count * itemHeight;
        content.sizeDelta = new Vector2(content.sizeDelta.x, contentHeight);
        
        // Prewarm the object pool
        PrewarmPool();
        
        // Initial viewport update
        UpdateViewport();
        
        isInitialized = true;
        
        Debug.Log($"VirtualizedCharacterList initialized with {characters.Count} characters");
    }
    
    void PrewarmPool()
    {
        int poolSize = visibleItemCount + bufferItems * 2;
        
        for (int i = 0; i < poolSize; i++)
        {
            GameObject itemObj = Instantiate(characterPrefab, poolParent);
            VirtualizedCharacterItem item = itemObj.GetComponent<VirtualizedCharacterItem>();
            
            if (item == null)
            {
                item = itemObj.AddComponent<VirtualizedCharacterItem>();
            }
            
            item.Initialize(this);
            item.gameObject.SetActive(false);
            pooledItems.Enqueue(item);
        }
        
        Debug.Log($"Character UI pool prewarmed with {poolSize} items");
    }
    
    void OnScrollValueChanged(Vector2 scrollPosition)
    {
        if (!isInitialized) return;
        UpdateViewport();
    }
    
    void UpdateViewport()
    {
        if (characters == null || characters.Count == 0) return;
        
        // Calculate which items should be visible
        float viewportTop = content.anchoredPosition.y;
        float viewportBottom = viewportTop + viewport.rect.height;
        
        int newFirstIndex = Mathf.Max(0, Mathf.FloorToInt(viewportTop / itemHeight) - bufferItems);
        int newLastIndex = Mathf.Min(characters.Count - 1, 
            Mathf.CeilToInt(viewportBottom / itemHeight) + bufferItems);
        
        // Only update if the visible range changed
        if (newFirstIndex != firstVisibleIndex || newLastIndex != lastVisibleIndex)
        {
            firstVisibleIndex = newFirstIndex;
            lastVisibleIndex = newLastIndex;
            RefreshVisibleItems();
        }
    }
    
    void RefreshVisibleItems()
    {
        // Return items that are no longer visible to pool
        for (int i = activeItems.Count - 1; i >= 0; i--)
        {
            VirtualizedCharacterItem item = activeItems[i];
            if (item.characterIndex < firstVisibleIndex || item.characterIndex > lastVisibleIndex)
            {
                ReturnItemToPool(item);
            }
        }
        
        // Create or reuse items for newly visible characters
        for (int index = firstVisibleIndex; index <= lastVisibleIndex; index++)
        {
            if (!indexToItemMap.ContainsKey(index))
            {
                VirtualizedCharacterItem item = GetPooledItem();
                if (item != null)
                {
                    BindItemToCharacter(item, index);
                }
            }
        }
    }
    
    VirtualizedCharacterItem GetPooledItem()
    {
        if (pooledItems.Count > 0)
        {
            VirtualizedCharacterItem item = pooledItems.Dequeue();
            item.gameObject.SetActive(true);
            item.transform.SetParent(content);
            activeItems.Add(item);
            return item;
        }
        
        Debug.LogWarning("VirtualizedCharacterList: Pool exhausted, creating new item");
        GameObject itemObj = Instantiate(characterPrefab, content);
        VirtualizedCharacterItem newItem = itemObj.GetComponent<VirtualizedCharacterItem>();
        if (newItem == null)
        {
            newItem = itemObj.AddComponent<VirtualizedCharacterItem>();
        }
        newItem.Initialize(this);
        activeItems.Add(newItem);
        return newItem;
    }
    
    void ReturnItemToPool(VirtualizedCharacterItem item)
    {
        if (item == null) return;
        
        // Preserve character state before unbinding
        if (item.characterIndex >= 0 && item.characterIndex < characters.Count)
        {
            PreserveCharacterState(item);
        }
        
        // Remove from active tracking
        activeItems.Remove(item);
        if (indexToItemMap.ContainsKey(item.characterIndex))
        {
            indexToItemMap.Remove(item.characterIndex);
        }
        
        // Unbind and return to pool
        item.UnbindCharacter();
        item.gameObject.SetActive(false);
        item.transform.SetParent(poolParent);
        pooledItems.Enqueue(item);
    }
    
    void BindItemToCharacter(VirtualizedCharacterItem item, int characterIndex)
    {
        if (characterIndex < 0 || characterIndex >= characters.Count) return;
        
        BattleCharacter character = characters[characterIndex];
        item.BindCharacter(character, characterIndex, configsHandler);
        
        // Set position based on character index
        float yPosition = -characterIndex * itemHeight;
        item.GetComponent<RectTransform>().anchoredPosition = new Vector2(0, yPosition);
        
        // Track the item
        indexToItemMap[characterIndex] = item;
        
        // Restore character state if available
        RestoreCharacterState(item);
    }
    
    void PreserveCharacterState(VirtualizedCharacterItem item)
    {
        // Preserve any UI state that needs to persist when scrolling
        string characterId = item.GetCharacter()?.id;
        if (!string.IsNullOrEmpty(characterId))
        {
            CharacterUIStateManager.Instance.SaveCharacterState(characterId, item);
        }
    }
    
    void RestoreCharacterState(VirtualizedCharacterItem item)
    {
        // Restore preserved UI state
        string characterId = item.GetCharacter()?.id;
        if (!string.IsNullOrEmpty(characterId))
        {
            CharacterUIStateManager.Instance.RestoreCharacterState(characterId, item);
        }
    }
    
    // Public interface for external systems
    public void RefreshCharacterList()
    {
        if (configsHandler != null)
        {
            characters = configsHandler.characters;
            contentHeight = characters.Count * itemHeight;
            content.sizeDelta = new Vector2(content.sizeDelta.x, contentHeight);
            UpdateViewport();
        }
    }
    
    public void ScrollToCharacter(int characterIndex)
    {
        if (characterIndex < 0 || characterIndex >= characters.Count) return;
        
        float targetY = characterIndex * itemHeight;
        content.anchoredPosition = new Vector2(content.anchoredPosition.x, targetY);
        UpdateViewport();
    }
    
    public void ScrollToCharacter(BattleCharacter character)
    {
        int index = characters.IndexOf(character);
        if (index >= 0)
        {
            ScrollToCharacter(index);
        }
    }

    // Performance monitoring
    public int GetActiveItemCount()
    {
        return activeItems?.Count ?? 0;
    }

    public int GetPooledItemCount()
    {
        return pooledItems?.Count ?? 0;
    }

    public int GetTotalItemCount()
    {
        return GetActiveItemCount() + GetPooledItemCount();
    }
}
