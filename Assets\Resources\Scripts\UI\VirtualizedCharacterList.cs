using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// High-performance virtualized character list that renders only visible elements
/// Replaces the traditional approach of instantiating all 618+ characters simultaneously
/// </summary>
public class VirtualizedCharacterList : MonoBehaviour
{
    [Header("Virtualization Settings")]
    [SerializeField] private int visibleElementCount = 10;
    [SerializeField] private int bufferElementCount = 2;
    [SerializeField] private float elementHeight = 0.5f;
    [SerializeField] private bool enableDebugLogging = false;
    
    [Header("UI References")]
    [SerializeField] private Transform contentParent;
    [SerializeField] private ScrollRect scrollRect;
    [SerializeField] private RectTransform viewport;
    
    // Core Components
    private CharacterUIPool uiPool;
    private CachedCharacterFilter characterFilter;
    private ViewportCalculator viewportCalculator;
    
    // State Management
    private List<BattleCharacter> filteredCharacters = new List<BattleCharacter>();
    private List<VirtualCharacterElement> virtualElements = new List<VirtualCharacterElement>();
    private int firstVisibleIndex = 0;
    private int lastVisibleIndex = 0;
    
    // Configuration
    private ConfigsHandler configsHandler;
    private Camera configsCamera;
    
    // Performance Tracking
    private int framesSinceLastUpdate = 0;
    private const int UPDATE_FREQUENCY = 3; // Update every 3 frames for 20 FPS update rate
    
    /// <summary>
    /// Total number of elements in the virtualized list
    /// </summary>
    public int TotalElementCount => filteredCharacters.Count;
    
    /// <summary>
    /// Number of currently visible UI elements
    /// </summary>
    public int VisibleElementCount => Mathf.Min(visibleElementCount + bufferElementCount, TotalElementCount);
    
    private void Awake()
    {
        InitializeComponents();
    }
    
    private void Start()
    {
        SetupVirtualization();
        RefreshCharacterList();
    }
    
    private void Update()
    {
        // Throttle updates for performance
        framesSinceLastUpdate++;
        if (framesSinceLastUpdate < UPDATE_FREQUENCY) return;
        framesSinceLastUpdate = 0;
        
        UpdateVirtualization();
    }
    
    /// <summary>
    /// Initialize core virtualization components
    /// </summary>
    private void InitializeComponents()
    {
        // Get required references
        configsHandler = GameObject.Find("GameConfigsHandler")?.GetComponent<ConfigsHandler>();
        configsCamera = GameObject.Find("Configs Camera")?.GetComponent<Camera>();
        
        if (configsHandler == null)
        {
            Debug.LogError("[VIRTUALIZED_LIST] ❌ ConfigsHandler not found!");
            return;
        }
        
        // Initialize components
        uiPool = new CharacterUIPool(visibleElementCount + bufferElementCount, contentParent);
        characterFilter = new CachedCharacterFilter(configsHandler);
        viewportCalculator = new ViewportCalculator(elementHeight, viewport);
        
        // Setup scroll rect if not assigned
        if (scrollRect == null)
            scrollRect = GetComponentInParent<ScrollRect>();
            
        if (viewport == null && scrollRect != null)
            viewport = scrollRect.viewport;
            
        if (contentParent == null)
            contentParent = transform;
            
        if (enableDebugLogging)
            Debug.Log($"[VIRTUALIZED_LIST] ✅ Initialized with {VisibleElementCount} visible elements");
    }
    
    /// <summary>
    /// Setup the virtualization system
    /// </summary>
    private void SetupVirtualization()
    {
        // Configure scroll rect for virtualization
        if (scrollRect != null)
        {
            scrollRect.onValueChanged.AddListener(OnScrollValueChanged);
            scrollRect.vertical = true;
            scrollRect.horizontal = false;
            scrollRect.movementType = ScrollRect.MovementType.Clamped;
        }
        
        // Initialize virtual elements
        virtualElements.Clear();
        for (int i = 0; i < VisibleElementCount; i++)
        {
            virtualElements.Add(new VirtualCharacterElement());
        }
    }
    
    /// <summary>
    /// Refresh the character list from ConfigsHandler
    /// </summary>
    public void RefreshCharacterList()
    {
        if (configsHandler == null) return;
        
        // Update filtered characters using cached filter
        var newFilteredCharacters = characterFilter.GetFilteredCharacters();
        
        // Check if list changed
        bool listChanged = !filteredCharacters.SequenceEqual(newFilteredCharacters);
        
        if (listChanged)
        {
            filteredCharacters = newFilteredCharacters;
            UpdateContentSize();
            UpdateVisibleElements(true); // Force update on list change
            
            if (enableDebugLogging)
                Debug.Log($"[VIRTUALIZED_LIST] 🔄 Character list updated: {filteredCharacters.Count} characters");
        }
    }
    
    /// <summary>
    /// Update the scroll content size based on total elements
    /// </summary>
    private void UpdateContentSize()
    {
        if (scrollRect?.content != null)
        {
            float totalHeight = TotalElementCount * elementHeight;
            scrollRect.content.sizeDelta = new Vector2(scrollRect.content.sizeDelta.x, totalHeight);
        }
    }
    
    /// <summary>
    /// Handle scroll value changes
    /// </summary>
    private void OnScrollValueChanged(Vector2 scrollPosition)
    {
        UpdateVirtualization();
    }
    
    /// <summary>
    /// Update virtualization based on current scroll position
    /// </summary>
    private void UpdateVirtualization()
    {
        if (TotalElementCount == 0) return;
        
        // Calculate visible range
        var visibleRange = viewportCalculator.CalculateVisibleRange(
            scrollRect?.content?.anchoredPosition.y ?? 0f, 
            TotalElementCount
        );
        
        // Check if visible range changed
        if (visibleRange.firstIndex != firstVisibleIndex || visibleRange.lastIndex != lastVisibleIndex)
        {
            firstVisibleIndex = visibleRange.firstIndex;
            lastVisibleIndex = visibleRange.lastIndex;
            UpdateVisibleElements(false);
        }
    }
    
    /// <summary>
    /// Update visible UI elements based on current viewport
    /// </summary>
    private void UpdateVisibleElements(bool forceUpdate)
    {
        int elementsToShow = Mathf.Min(VisibleElementCount, lastVisibleIndex - firstVisibleIndex + 1);
        
        for (int i = 0; i < VisibleElementCount; i++)
        {
            var virtualElement = virtualElements[i];
            int characterIndex = firstVisibleIndex + i;
            
            if (i < elementsToShow && characterIndex < TotalElementCount)
            {
                // Show element
                var character = filteredCharacters[characterIndex];
                var uiElement = uiPool.GetElement();
                
                if (uiElement != null)
                {
                    // Update virtual element
                    virtualElement.characterIndex = characterIndex;
                    virtualElement.character = character;
                    virtualElement.uiElement = uiElement;
                    virtualElement.isActive = true;
                    
                    // Position element
                    float yPosition = -characterIndex * elementHeight;
                    uiElement.transform.localPosition = new Vector3(0, yPosition, 0);
                    
                    // Update UI content
                    UpdateCharacterUI(uiElement, character);
                    uiElement.SetActive(true);
                }
            }
            else
            {
                // Hide element
                if (virtualElement.isActive && virtualElement.uiElement != null)
                {
                    uiPool.ReturnElement(virtualElement.uiElement);
                    virtualElement.Reset();
                }
            }
        }
        
        if (enableDebugLogging && forceUpdate)
        {
            Debug.Log($"[VIRTUALIZED_LIST] 📊 Showing elements {firstVisibleIndex}-{lastVisibleIndex} " +
                     $"({elementsToShow} visible of {TotalElementCount} total)");
        }
    }
    
    /// <summary>
    /// Update character UI element with character data
    /// </summary>
    private void UpdateCharacterUI(GameObject uiElement, BattleCharacter character)
    {
        var charConfUI = uiElement.GetComponent<CharConfUI>();
        if (charConfUI != null)
        {
            charConfUI.character = character;
            // Disable the Update method positioning since we handle it here
            charConfUI.enabled = false;
        }
    }
    
    /// <summary>
    /// Get character at specific index for external access
    /// </summary>
    public BattleCharacter GetCharacterAtIndex(int index)
    {
        if (index >= 0 && index < filteredCharacters.Count)
            return filteredCharacters[index];
        return null;
    }
    
    /// <summary>
    /// Force refresh of the entire list
    /// </summary>
    public void ForceRefresh()
    {
        characterFilter.InvalidateCache();
        RefreshCharacterList();
    }
    
    /// <summary>
    /// Get current scroll position as percentage
    /// </summary>
    public float GetScrollPercentage()
    {
        if (scrollRect?.content == null || TotalElementCount <= VisibleElementCount)
            return 0f;
            
        float maxScroll = (TotalElementCount - VisibleElementCount) * elementHeight;
        float currentScroll = scrollRect.content.anchoredPosition.y;
        return Mathf.Clamp01(currentScroll / maxScroll);
    }
    
    /// <summary>
    /// Scroll to specific character index
    /// </summary>
    public void ScrollToCharacter(int characterIndex)
    {
        if (scrollRect?.content == null || characterIndex < 0 || characterIndex >= TotalElementCount)
            return;
            
        float targetY = characterIndex * elementHeight;
        scrollRect.content.anchoredPosition = new Vector2(
            scrollRect.content.anchoredPosition.x, 
            targetY
        );
    }
    
    private void OnDestroy()
    {
        if (scrollRect != null)
            scrollRect.onValueChanged.RemoveListener(OnScrollValueChanged);
            
        uiPool?.Dispose();
    }
}

/// <summary>
/// Represents a virtual character element in the list
/// </summary>
public class VirtualCharacterElement
{
    public int characterIndex = -1;
    public BattleCharacter character;
    public GameObject uiElement;
    public bool isActive = false;
    
    public void Reset()
    {
        characterIndex = -1;
        character = null;
        uiElement = null;
        isActive = false;
    }
}
