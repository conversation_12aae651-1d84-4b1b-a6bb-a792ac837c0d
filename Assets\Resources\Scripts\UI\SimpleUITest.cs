using UnityEngine;

/// <summary>
/// Simple UI test script to verify virtualized character system is working
/// This is a lightweight alternative to UIPerformanceTest.cs
/// </summary>
public class SimpleUITest : MonoBehaviour
{
    [Header("Simple Test Settings")]
    [SerializeField] private bool runTestOnStart = true;
    [SerializeField] private bool showContinuousStats = true;
    
    // Component references
    private ConfigsHandler configsHandler;
    private VirtualizedCharacterList virtualizedList;
    private VirtualizedAddCharacter virtualizedAddChar;
    
    void Start()
    {
        if (runTestOnStart)
        {
            RunSimpleTest();
        }
        
        if (showContinuousStats)
        {
            InvokeRepeating(nameof(LogSimpleStats), 2f, 3f);
        }
    }
    
    /// <summary>
    /// Runs a simple test to verify the virtualized system is working
    /// </summary>
    public void RunSimpleTest()
    {
        Debug.Log("=== SIMPLE UI VIRTUALIZATION TEST ===");
        
        // Find components
        FindComponents();
        
        // Test basic functionality
        TestBasicFunctionality();
        
        // Show results
        ShowResults();
        
        Debug.Log("=== SIMPLE TEST COMPLETED ===");
    }
    
    /// <summary>
    /// Finds all required components
    /// </summary>
    private void FindComponents()
    {
        Debug.Log("[SIMPLE_TEST] 🔍 Finding components...");
        
        // Find ConfigsHandler
        GameObject configsObj = GameObject.Find("GameConfigsHandler");
        if (configsObj != null)
        {
            configsHandler = configsObj.GetComponent<ConfigsHandler>();
            Debug.Log("[SIMPLE_TEST] ✅ ConfigsHandler found");
        }
        else
        {
            Debug.LogWarning("[SIMPLE_TEST] ⚠️ ConfigsHandler not found");
        }
        
        // Find VirtualizedCharacterList
        virtualizedList = FindFirstObjectByType<VirtualizedCharacterList>();
        if (virtualizedList != null)
        {
            Debug.Log("[SIMPLE_TEST] ✅ VirtualizedCharacterList found");
        }
        else
        {
            Debug.LogWarning("[SIMPLE_TEST] ⚠️ VirtualizedCharacterList not found");
        }
        
        // Find VirtualizedAddCharacter
        virtualizedAddChar = FindFirstObjectByType<VirtualizedAddCharacter>();
        if (virtualizedAddChar != null)
        {
            Debug.Log("[SIMPLE_TEST] ✅ VirtualizedAddCharacter found");
        }
        else
        {
            Debug.LogWarning("[SIMPLE_TEST] ⚠️ VirtualizedAddCharacter not found");
        }
    }
    
    /// <summary>
    /// Tests basic functionality
    /// </summary>
    private void TestBasicFunctionality()
    {
        Debug.Log("[SIMPLE_TEST] 🧪 Testing basic functionality...");
        
        if (configsHandler == null)
        {
            Debug.LogError("[SIMPLE_TEST] ❌ Cannot test without ConfigsHandler");
            return;
        }
        
        // Get character count
        var characters = configsHandler.GetCharacters();
        Debug.Log($"[SIMPLE_TEST] 📊 Total characters loaded: {characters.Count}");
        
        // Test virtualized list
        if (virtualizedList != null)
        {
            int activeElements = virtualizedList.GetActiveUIElementCount();
            int filteredCount = virtualizedList.GetFilteredCharacterCount();
            
            Debug.Log($"[SIMPLE_TEST] 📊 Active UI elements: {activeElements}");
            Debug.Log($"[SIMPLE_TEST] 📊 Filtered characters: {filteredCount}");
            
            // Calculate memory reduction
            if (characters.Count > 0)
            {
                float memoryReduction = (1f - (float)activeElements / characters.Count) * 100f;
                Debug.Log($"[SIMPLE_TEST] 📊 Memory reduction: {memoryReduction:F1}%");
                
                if (memoryReduction >= 90f)
                {
                    Debug.Log("[SIMPLE_TEST] ✅ EXCELLENT memory optimization achieved!");
                }
                else if (memoryReduction >= 70f)
                {
                    Debug.Log("[SIMPLE_TEST] ✅ GOOD memory optimization achieved!");
                }
                else
                {
                    Debug.LogWarning("[SIMPLE_TEST] ⚠️ Memory optimization may need improvement");
                }
            }
            
            // Test scrolling
            if (filteredCount > 10)
            {
                Debug.Log("[SIMPLE_TEST] 📜 Testing scroll functionality...");
                virtualizedList.ScrollToIndex(filteredCount / 2);
                Debug.Log("[SIMPLE_TEST] ✅ Scroll test completed");
            }
        }
        
        // Test virtualized add character
        if (virtualizedAddChar != null)
        {
            int totalCharacters = virtualizedAddChar.GetTotalCharacterCount();
            int activeUIElements = virtualizedAddChar.GetActiveUIElementCount();
            
            Debug.Log($"[SIMPLE_TEST] 📊 VirtualizedAddCharacter managing {totalCharacters} characters");
            Debug.Log($"[SIMPLE_TEST] 📊 VirtualizedAddCharacter using {activeUIElements} UI elements");
        }
    }
    
    /// <summary>
    /// Shows test results
    /// </summary>
    private void ShowResults()
    {
        Debug.Log("[SIMPLE_TEST] 📋 TEST RESULTS:");
        
        // Component status
        Debug.Log($"  ConfigsHandler: {(configsHandler != null ? "✅ WORKING" : "❌ MISSING")}");
        Debug.Log($"  VirtualizedCharacterList: {(virtualizedList != null ? "✅ WORKING" : "❌ MISSING")}");
        Debug.Log($"  VirtualizedAddCharacter: {(virtualizedAddChar != null ? "✅ WORKING" : "❌ MISSING")}");
        
        // Performance status
        if (configsHandler != null && virtualizedList != null)
        {
            int totalChars = configsHandler.GetCharacters().Count;
            int activeUI = virtualizedList.GetActiveUIElementCount();
            
            Debug.Log($"  Character Count: {totalChars}");
            Debug.Log($"  Active UI Objects: {activeUI}");
            
            if (totalChars > 500 && activeUI < 20)
            {
                Debug.Log("  Performance Status: ✅ EXCELLENT - Virtualization working perfectly!");
            }
            else if (totalChars > 100 && activeUI < totalChars / 10)
            {
                Debug.Log("  Performance Status: ✅ GOOD - Virtualization working well!");
            }
            else if (activeUI < totalChars)
            {
                Debug.Log("  Performance Status: ⚠️ PARTIAL - Some optimization achieved");
            }
            else
            {
                Debug.LogWarning("  Performance Status: ❌ POOR - Virtualization may not be working");
            }
        }
        
        // Frame rate check
        float currentFPS = 1f / Time.unscaledDeltaTime;
        Debug.Log($"  Current FPS: {currentFPS:F1}");
        
        if (currentFPS >= 60f)
        {
            Debug.Log("  FPS Status: ✅ EXCELLENT");
        }
        else if (currentFPS >= 30f)
        {
            Debug.Log("  FPS Status: ✅ GOOD");
        }
        else
        {
            Debug.LogWarning("  FPS Status: ⚠️ NEEDS IMPROVEMENT");
        }
    }
    
    /// <summary>
    /// Logs simple continuous statistics
    /// </summary>
    private void LogSimpleStats()
    {
        if (!showContinuousStats) return;
        
        float fps = 1f / Time.unscaledDeltaTime;
        int activeUI = virtualizedList != null ? virtualizedList.GetActiveUIElementCount() : 0;
        int totalChars = configsHandler != null ? configsHandler.GetCharacters().Count : 0;
        
        Debug.Log($"[UI_STATS] FPS: {fps:F1} | Active UI: {activeUI} | Total Characters: {totalChars}");
    }
    
    /// <summary>
    /// Manual test trigger - can be called from inspector
    /// </summary>
    [ContextMenu("Run Simple Test")]
    public void RunTestManually()
    {
        RunSimpleTest();
    }
    
    /// <summary>
    /// Toggle continuous stats
    /// </summary>
    [ContextMenu("Toggle Continuous Stats")]
    public void ToggleContinuousStats()
    {
        showContinuousStats = !showContinuousStats;
        Debug.Log($"[SIMPLE_TEST] Continuous stats: {(showContinuousStats ? "ENABLED" : "DISABLED")}");
        
        if (showContinuousStats)
        {
            InvokeRepeating(nameof(LogSimpleStats), 1f, 3f);
        }
        else
        {
            CancelInvoke(nameof(LogSimpleStats));
        }
    }
    
    /// <summary>
    /// Test scrolling functionality
    /// </summary>
    [ContextMenu("Test Scrolling")]
    public void TestScrolling()
    {
        if (virtualizedList == null)
        {
            Debug.LogWarning("[SIMPLE_TEST] Cannot test scrolling - VirtualizedCharacterList not found");
            return;
        }
        
        int totalCount = virtualizedList.GetFilteredCharacterCount();
        if (totalCount == 0)
        {
            Debug.LogWarning("[SIMPLE_TEST] Cannot test scrolling - No characters found");
            return;
        }
        
        Debug.Log("[SIMPLE_TEST] 📜 Testing scrolling...");
        
        // Scroll to different positions
        virtualizedList.ScrollToIndex(0);
        Debug.Log("[SIMPLE_TEST] Scrolled to top");
        
        if (totalCount > 10)
        {
            virtualizedList.ScrollToIndex(totalCount / 2);
            Debug.Log("[SIMPLE_TEST] Scrolled to middle");
            
            virtualizedList.ScrollToIndex(totalCount - 1);
            Debug.Log("[SIMPLE_TEST] Scrolled to bottom");
        }
        
        Debug.Log("[SIMPLE_TEST] ✅ Scrolling test completed");
    }
}
