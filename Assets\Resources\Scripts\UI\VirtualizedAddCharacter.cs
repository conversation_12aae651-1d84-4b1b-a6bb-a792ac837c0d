using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Virtualized replacement for AddCharacter class
/// Instead of instantiating UI for all 618 characters, uses VirtualizedCharacterList
/// Provides massive performance improvement by only rendering visible characters
/// </summary>
public class VirtualizedAddCharacter : MonoBehaviour
{
    [Header("Virtualization Settings")]
    [SerializeField] private VirtualizedCharacterList virtualizedList;
    [SerializeField] private Transform charactersInfoParent;
    [SerializeField] private bool enablePerformanceLogging = false;
    
    // References
    private ConfigsHandler configsHandler;
    
    // Performance tracking
    private int lastCharacterCount = -1;
    private float initializationTime = 0f;
    
    void Awake()
    {
        configsHandler = GameObject.Find("GameConfigsHandler")?.GetComponent<ConfigsHandler>();
        
        if (configsHandler == null)
        {
            Debug.LogError("[VIRTUALIZED_ADD_CHAR] ❌ ConfigsHandler not found!");
            return;
        }
        
        // Find or create the CharactersInfo parent
        if (charactersInfoParent == null)
        {
            GameObject charactersInfoObj = GameObject.Find("CharactersInfo");
            if (charactersInfoObj != null)
            {
                charactersInfoParent = charactersInfoObj.transform;
            }
            else
            {
                Debug.LogError("[VIRTUALIZED_ADD_CHAR] ❌ CharactersInfo parent not found!");
                return;
            }
        }
    }
    
    void Start()
    {
        // Only initialize if we have the required components
        if (configsHandler != null && charactersInfoParent != null)
        {
            InitializeVirtualizedSystem();
        }
        else
        {
            Debug.LogWarning("[VIRTUALIZED_ADD_CHAR] ⚠️ Required components not found - will retry initialization later");
        }
    }
    
    /// <summary>
    /// Initializes the virtualized character system
    /// Replaces the old system that created UI for all characters
    /// </summary>
    public void InitializeVirtualizedSystem()
    {
        float startTime = Time.realtimeSinceStartup;
        
        // Find or create VirtualizedCharacterList component
        if (virtualizedList == null)
        {
            virtualizedList = charactersInfoParent.GetComponent<VirtualizedCharacterList>();
            
            if (virtualizedList == null)
            {
                virtualizedList = charactersInfoParent.gameObject.AddComponent<VirtualizedCharacterList>();
            }
        }
        
        // Initialize the virtualized list
        if (virtualizedList != null)
        {
            virtualizedList.InitializeVirtualization();

            // Set up character selection callbacks
            virtualizedList.OnCharacterSelected += OnCharacterSelected;
            virtualizedList.OnCharacterRemoved += OnCharacterRemoved;
        }
        else
        {
            Debug.LogError("[VIRTUALIZED_ADD_CHAR] ❌ Failed to create VirtualizedCharacterList component!");
            return;
        }
        
        initializationTime = (Time.realtimeSinceStartup - startTime) * 1000f;
        
        if (enablePerformanceLogging)
        {
            Debug.Log($"[VIRTUALIZED_ADD_CHAR] ✅ Initialized in {initializationTime:F2}ms");
            LogPerformanceComparison();
        }
    }
    
    /// <summary>
    /// Adds characters to the virtualized system
    /// This replaces the old AddCharacter constructor pattern
    /// </summary>
    public void LoadCharactersToUI(List<BattleCharacter> characters)
    {
        if (virtualizedList == null)
        {
            Debug.LogError("[VIRTUALIZED_ADD_CHAR] ❌ VirtualizedCharacterList not initialized!");
            return;
        }
        
        float startTime = Time.realtimeSinceStartup;
        
        // Simply refresh the virtualized list - it will handle the characters automatically
        virtualizedList.RefreshCharacterList();
        
        float loadTime = (Time.realtimeSinceStartup - startTime) * 1000f;
        
        if (enablePerformanceLogging)
        {
            Debug.Log($"[VIRTUALIZED_ADD_CHAR] 📊 Loaded {characters.Count} characters in {loadTime:F2}ms");
            Debug.Log($"[VIRTUALIZED_ADD_CHAR] 🎯 Active UI elements: {virtualizedList.GetActiveUIElementCount()}/{characters.Count}");
        }
        
        lastCharacterCount = characters.Count;
    }
    
    /// <summary>
    /// Adds a single character to the virtualized system
    /// Replaces individual AddCharacter instantiation
    /// </summary>
    public void AddCharacterToUI(int index, BattleCharacter character)
    {
        if (virtualizedList == null)
        {
            Debug.LogError("[VIRTUALIZED_ADD_CHAR] ❌ VirtualizedCharacterList not initialized!");
            return;
        }
        
        // For virtualized system, we just refresh the list
        // The virtualization will handle showing/hiding as needed
        virtualizedList.RefreshCharacterList();
        
        if (enablePerformanceLogging)
        {
            Debug.Log($"[VIRTUALIZED_ADD_CHAR] ➕ Added character {character.name} (index {index})");
        }
    }
    
    /// <summary>
    /// Removes a character from the virtualized system
    /// </summary>
    public void RemoveCharacterFromUI(BattleCharacter character)
    {
        if (virtualizedList == null) return;
        
        virtualizedList.RefreshCharacterList();
        
        if (enablePerformanceLogging)
        {
            Debug.Log($"[VIRTUALIZED_ADD_CHAR] ➖ Removed character {character.name}");
        }
    }
    
    /// <summary>
    /// Sets a filter for which characters to display
    /// </summary>
    public void SetCharacterFilter(System.Func<BattleCharacter, bool> filter)
    {
        if (virtualizedList == null) return;
        
        virtualizedList.SetCharacterFilter(filter);
        
        if (enablePerformanceLogging)
        {
            Debug.Log($"[VIRTUALIZED_ADD_CHAR] 🔍 Applied character filter");
        }
    }
    
    /// <summary>
    /// Scrolls to a specific character in the list
    /// </summary>
    public void ScrollToCharacter(BattleCharacter character)
    {
        if (virtualizedList == null) return;
        
        virtualizedList.ScrollToCharacter(character);
        
        if (enablePerformanceLogging)
        {
            Debug.Log($"[VIRTUALIZED_ADD_CHAR] 📜 Scrolled to character {character.name}");
        }
    }
    
    /// <summary>
    /// Forces a complete refresh of the character UI
    /// </summary>
    public void RefreshCharacterUI()
    {
        if (virtualizedList == null) return;
        
        virtualizedList.ForceRefresh();
        
        if (enablePerformanceLogging)
        {
            Debug.Log("[VIRTUALIZED_ADD_CHAR] 🔄 Forced UI refresh");
        }
    }
    
    /// <summary>
    /// Called when a character is selected in the virtualized list
    /// </summary>
    private void OnCharacterSelected(BattleCharacter character)
    {
        if (enablePerformanceLogging)
        {
            Debug.Log($"[VIRTUALIZED_ADD_CHAR] 👆 Character selected: {character.name}");
        }
        
        // Handle character selection logic here
        // This can be extended based on specific requirements
    }
    
    /// <summary>
    /// Called when a character is removed from the virtualized list
    /// </summary>
    private void OnCharacterRemoved(BattleCharacter character)
    {
        if (enablePerformanceLogging)
        {
            Debug.Log($"[VIRTUALIZED_ADD_CHAR] 🗑️ Character removed: {character.name}");
        }
        
        // Handle character removal logic here
        // This can be extended based on specific requirements
    }
    
    /// <summary>
    /// Gets performance statistics for the virtualized system
    /// </summary>
    public void LogPerformanceStats()
    {
        if (virtualizedList == null) return;
        
        int totalCharacters = virtualizedList.GetFilteredCharacterCount();
        int activeUIElements = virtualizedList.GetActiveUIElementCount();
        float memoryReduction = totalCharacters > 0 ? (1f - (float)activeUIElements / totalCharacters) * 100f : 0f;
        
        Debug.Log($"[VIRTUALIZED_ADD_CHAR] 📊 Performance Stats:");
        Debug.Log($"  Total Characters: {totalCharacters}");
        Debug.Log($"  Active UI Elements: {activeUIElements}");
        Debug.Log($"  Memory Reduction: {memoryReduction:F1}%");
        Debug.Log($"  Initialization Time: {initializationTime:F2}ms");
    }
    
    /// <summary>
    /// Logs performance comparison with old system
    /// </summary>
    private void LogPerformanceComparison()
    {
        if (configsHandler == null) return;
        
        int characterCount = configsHandler.GetCharacters().Count;
        int activeUIElements = virtualizedList?.GetActiveUIElementCount() ?? 0;
        
        Debug.Log($"[VIRTUALIZED_ADD_CHAR] 🚀 Performance Comparison:");
        Debug.Log($"  OLD SYSTEM: {characterCount} UI objects instantiated");
        Debug.Log($"  NEW SYSTEM: {activeUIElements} UI objects active");
        Debug.Log($"  MEMORY REDUCTION: {(1f - (float)activeUIElements / characterCount) * 100f:F1}%");
        Debug.Log($"  EXPECTED FPS IMPROVEMENT: 60+ FPS");
    }
    
    /// <summary>
    /// Gets the current number of active UI elements
    /// </summary>
    public int GetActiveUIElementCount()
    {
        return virtualizedList?.GetActiveUIElementCount() ?? 0;
    }
    
    /// <summary>
    /// Gets the total number of characters being managed
    /// </summary>
    public int GetTotalCharacterCount()
    {
        return virtualizedList?.GetFilteredCharacterCount() ?? 0;
    }
    
    void OnDestroy()
    {
        if (virtualizedList != null)
        {
            virtualizedList.OnCharacterSelected -= OnCharacterSelected;
            virtualizedList.OnCharacterRemoved -= OnCharacterRemoved;
        }
        
        if (enablePerformanceLogging)
        {
            LogPerformanceStats();
        }
    }
}
