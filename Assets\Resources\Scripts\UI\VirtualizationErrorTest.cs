using UnityEngine;

/// <summary>
/// Test script to verify virtualization components handle null references properly
/// </summary>
public class VirtualizationErrorTest : MonoBehaviour
{
    [Header("Error Test Settings")]
    [SerializeField] private bool runTestOnStart = true;
    [SerializeField] private bool testNullReferences = true;
    [SerializeField] private bool testMissingComponents = true;
    
    void Start()
    {
        if (runTestOnStart)
        {
            StartCoroutine(RunErrorTests());
        }
    }
    
    /// <summary>
    /// Runs error handling tests
    /// </summary>
    private System.Collections.IEnumerator RunErrorTests()
    {
        Debug.Log("=== VIRTUALIZATION ERROR HANDLING TEST ===");
        
        yield return new WaitForSeconds(1f);
        
        if (testNullReferences)
        {
            TestNullReferenceHandling();
        }
        
        yield return new WaitForSeconds(1f);
        
        if (testMissingComponents)
        {
            TestMissingComponentHandling();
        }
        
        yield return new WaitForSeconds(1f);
        
        TestComponentInitialization();
        
        Debug.Log("=== ERROR HANDLING TEST COMPLETED ===");
    }
    
    /// <summary>
    /// Tests null reference handling
    /// </summary>
    private void TestNullReferenceHandling()
    {
        Debug.Log("[ERROR_TEST] 🧪 Testing null reference handling...");
        
        try
        {
            // Create a VirtualizedCharacterList without required components
            GameObject testObj = new GameObject("TestVirtualizedList");
            VirtualizedCharacterList testList = testObj.AddComponent<VirtualizedCharacterList>();
            
            // This should not throw null reference exceptions
            testList.InitializeVirtualization();
            
            Debug.Log("[ERROR_TEST] ✅ VirtualizedCharacterList handled missing components gracefully");
            
            // Clean up
            DestroyImmediate(testObj);
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[ERROR_TEST] ❌ VirtualizedCharacterList failed null reference test: {e.Message}");
        }
        
        try
        {
            // Create a VirtualizedAddCharacter without required components
            GameObject testObj = new GameObject("TestVirtualizedAddChar");
            VirtualizedAddCharacter testAddChar = testObj.AddComponent<VirtualizedAddCharacter>();
            
            // This should not throw null reference exceptions
            testAddChar.InitializeVirtualizedSystem();
            
            Debug.Log("[ERROR_TEST] ✅ VirtualizedAddCharacter handled missing components gracefully");
            
            // Clean up
            DestroyImmediate(testObj);
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[ERROR_TEST] ❌ VirtualizedAddCharacter failed null reference test: {e.Message}");
        }
    }
    
    /// <summary>
    /// Tests missing component handling
    /// </summary>
    private void TestMissingComponentHandling()
    {
        Debug.Log("[ERROR_TEST] 🧪 Testing missing component handling...");
        
        // Test VirtualizedCharacterList without ScrollRect
        GameObject testObj1 = new GameObject("TestNoScrollRect");
        VirtualizedCharacterList testList = testObj1.AddComponent<VirtualizedCharacterList>();
        
        bool initSuccess = true;
        try
        {
            testList.InitializeVirtualization();
        }
        catch (System.Exception e)
        {
            Debug.LogWarning($"[ERROR_TEST] Expected error for missing ScrollRect: {e.Message}");
            initSuccess = false;
        }
        
        if (initSuccess)
        {
            Debug.LogWarning("[ERROR_TEST] ⚠️ VirtualizedCharacterList should have failed without ScrollRect");
        }
        else
        {
            Debug.Log("[ERROR_TEST] ✅ VirtualizedCharacterList properly detected missing ScrollRect");
        }
        
        DestroyImmediate(testObj1);
        
        // Test VirtualizedAddCharacter without ConfigsHandler
        GameObject testObj2 = new GameObject("TestNoConfigsHandler");
        VirtualizedAddCharacter testAddChar = testObj2.AddComponent<VirtualizedAddCharacter>();
        
        try
        {
            testAddChar.InitializeVirtualizedSystem();
            Debug.Log("[ERROR_TEST] ✅ VirtualizedAddCharacter handled missing ConfigsHandler gracefully");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[ERROR_TEST] ❌ VirtualizedAddCharacter failed with missing ConfigsHandler: {e.Message}");
        }
        
        DestroyImmediate(testObj2);
    }
    
    /// <summary>
    /// Tests component initialization order
    /// </summary>
    private void TestComponentInitialization()
    {
        Debug.Log("[ERROR_TEST] 🧪 Testing component initialization order...");
        
        // Find existing components
        VirtualizedCharacterList[] existingLists = FindObjectsByType<VirtualizedCharacterList>(FindObjectsSortMode.None);
        VirtualizedAddCharacter[] existingAddChars = FindObjectsByType<VirtualizedAddCharacter>(FindObjectsSortMode.None);
        
        Debug.Log($"[ERROR_TEST] 📊 Found {existingLists.Length} VirtualizedCharacterList components");
        Debug.Log($"[ERROR_TEST] 📊 Found {existingAddChars.Length} VirtualizedAddCharacter components");
        
        // Test safe initialization
        foreach (var list in existingLists)
        {
            try
            {
                list.InitializeWhenReady();
                Debug.Log("[ERROR_TEST] ✅ VirtualizedCharacterList.InitializeWhenReady() succeeded");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[ERROR_TEST] ❌ VirtualizedCharacterList.InitializeWhenReady() failed: {e.Message}");
            }
        }
        
        // Check ConfigsHandler integration
        ConfigsHandler configsHandler = FindFirstObjectByType<ConfigsHandler>();
        if (configsHandler != null)
        {
            Debug.Log("[ERROR_TEST] ✅ ConfigsHandler found - integration should work");
            
            var characters = configsHandler.GetCharacters();
            if (characters != null)
            {
                Debug.Log($"[ERROR_TEST] 📊 ConfigsHandler has {characters.Count} characters loaded");
            }
            else
            {
                Debug.LogWarning("[ERROR_TEST] ⚠️ ConfigsHandler.GetCharacters() returned null");
            }
        }
        else
        {
            Debug.LogWarning("[ERROR_TEST] ⚠️ ConfigsHandler not found - virtualization may not work properly");
        }
    }
    
    /// <summary>
    /// Manual test trigger
    /// </summary>
    [ContextMenu("Run Error Tests")]
    public void RunTestsManually()
    {
        StartCoroutine(RunErrorTests());
    }
    
    /// <summary>
    /// Test specific null reference scenario
    /// </summary>
    [ContextMenu("Test Null References Only")]
    public void TestNullReferencesOnly()
    {
        TestNullReferenceHandling();
    }
    
    /// <summary>
    /// Test missing components scenario
    /// </summary>
    [ContextMenu("Test Missing Components Only")]
    public void TestMissingComponentsOnly()
    {
        TestMissingComponentHandling();
    }
    
    /// <summary>
    /// Test initialization order
    /// </summary>
    [ContextMenu("Test Initialization Order")]
    public void TestInitializationOrderOnly()
    {
        TestComponentInitialization();
    }
}
