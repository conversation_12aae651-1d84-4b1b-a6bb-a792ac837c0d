using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using TMPro;
using UnityEngine;
using UnityEngine.UI;


public class ConfigsHandler : MonoBehaviour
{
    public int[] ComboChance = new int[7], GoldenStrike = new int[7]; // combo chance array

    [HideInInspector]
    public int currentCombo = -1, playerTurns = 0, enemyTurns = 0, turns = 0, playerActions = 1, enemyActions = 1, playerEmptyActions = 0, enemyEmptyActions = 0, lostActions = -1, money = 0, ModFraction = 10; // turn counters

    public int pIndex = 0; // attacked player index

    public int energyTimer = 7; // max energy
    public float energyLeft = 7, ppLeft = 100f, shakeStrength = 5.3f; // current energy/power left

    public bool canCountdown = false; // the bool that controls the energy countdown

    // the default values of B, C, D, E, F, G, Difficulty, StockWeight, MaxPP, ReductionPerPiece and FractionOfAttacksPerAction
    [HideInInspector] public float B = 25, C = 2.5f, D = 0.5f, E = 0.04f, F = 1f, G = 1.5f, Difficulty = 1, StockWeight = 0.5f, MaxPP = 100f, ReductionPerCombo = 1f, FractionOfAttacksPerAction = 2f, IDBOffset = 0f;

    // the gameobjects that will be used to display the values, chance configurations and gameplay UI
    [HideInInspector]
    public GameObject
        StatsValues, SkillsValues,
        ModsValues, ActionMenu,
        SkillSelection, ChangeWithStock,
        CharactersForParty, EscapePopup,
        EscapeFailPopup, WinPopup,
        FailPopup, LosePopup,
        infiniteToggle, ChangeOnlyNulls,
        ShowHP, Coins,
        GamePlayConfs;

    public AudioSource MusicPlayer;

    public int partySelected = -1;

    GridManager grid; // the grid creator

    private bool[] playerSelected = { true, false, false, false }; // a bool array to control the player selection
    private bool[] enemySelected = { true, false, false, false }; // a bool array to control the enemy selection

    int selectedEnemy;

    List<BattleCharacter> characters = new(), enemies = new(); // a list to store the characters
    public List<PartyCharacters> partyCharacters = new(); // a list to store the party characters

    public List<GameObject> playersInterface = new(); // a list to store the players interfaces
    public List<GameObject> enemiesInterface = new();

    List<BuffNDebuffs> buffs = new(); // a list to store the buffs

    public List<CharacterSkills> defaultSkills = new(); // list of default skills
    public List<CharacterStatus> defaultStats = new(); // list of default stats
    public List<CharacterMods> defaultMods = new(); // list of default mods

    public BattleCharacter[] pC = { null, null, null, null }; // an array to store the player characters positions
    public BattleCharacter[] eC = { null, null, null, null }; // an array to store the enemy characters positions

    public LoadValues LoadedValues; // the gameobject that handles loading and saving of values

    public bool canCheckWhoStarts = true, stopGetAllEnemies = false;

    public bool playerTurn = true;

    public long damageNumber;

    public bool physicalAttack;

    public bool enemyFirstStrike = false;
    

    private void Awake()
    {

        for (int i = 0; i < pC.Length; i++) playersInterface.Add(GameObject.Find("Player" + i)); // gets the player interfaces
        for (int i = 0; i < eC.Length; i++) enemiesInterface.Add(GameObject.Find("Enemy" + i)); // gets the enemy interfaces

        try
        {
            grid = GameObject.Find("GridManager").GetComponent<GridManager>(); // gets the GridManager component
        }
        catch (Exception ex)
        {
            Debug.LogError("An error occurred: " + ex.Message);
        }
        // gets the gameobjects that will be used
        StatsValues = GameObject.Find("StatsValues");
        SkillsValues = GameObject.Find("SkillsValues");
        ModsValues = GameObject.Find("ModsValues");
        ActionMenu = GameObject.Find("ActionMenu");
        SkillSelection = GameObject.Find("SkillSelection");
        ChangeWithStock = GameObject.Find("ChangeWithStock");
        CharactersForParty = GameObject.Find("CharactersForParty");
        EscapePopup = GameObject.Find("EscapePopup");
        EscapeFailPopup = GameObject.Find("EscapeFailPopup");
        WinPopup = GameObject.Find("WinPopup");
        FailPopup = GameObject.Find("FailPopup");
        LosePopup = GameObject.Find("LosePopup");
        infiniteToggle = GameObject.Find("InfiniteToggle");
        ChangeOnlyNulls = GameObject.Find("ChangeOnlyNulls");
        ShowHP = GameObject.Find("ShowHP");
        Coins = GameObject.Find("Coins");
        GamePlayConfs = GameObject.Find("GamePlayConfs");

        // sets the gameobjects to inactive
        StatsValues.SetActive(false);
        ActionMenu.SetActive(false);
        SkillSelection.SetActive(false);
        ChangeWithStock.SetActive(false);
        EscapePopup.SetActive(false);
        EscapeFailPopup.SetActive(false);
        WinPopup.SetActive(false);
        FailPopup.SetActive(false);
        LosePopup.SetActive(false);
        GamePlayConfs.SetActive(false);

        // gets the gameobject that handles loading and saving of values
        LoadedValues = GameObject.Find("LoadedValues").GetComponent<LoadValues>();

        // loads the values
        energyTimer = LoadedValues.energyTimer;
        energyLeft = energyTimer;

        ModFraction = LoadedValues.ModFraction;

        ComboChance = LoadedValues.ComboChance;

        GoldenStrike = LoadedValues.GoldenStrike;

        B = LoadedValues.B;
        C = LoadedValues.C;
        D = LoadedValues.D;
        E = LoadedValues.E;
        F = LoadedValues.F;
        G = LoadedValues.G;
        Difficulty = LoadedValues.Difficulty;
        StockWeight = LoadedValues.StockWeight;
        MaxPP = LoadedValues.MaxPP;
        ReductionPerCombo = LoadedValues.ReductionPerCombo;
        FractionOfAttacksPerAction = LoadedValues.FractionOfAttacksPerAction;
        IDBOffset = LoadedValues.IDBOffset;

        defaultMods = LoadedValues.defaultMods;
        defaultSkills = LoadedValues.defaultSkills;
        defaultStats = LoadedValues.defaultStats;

        partyCharacters = LoadedValues.partyCharacters;

        // Ensure we always have exactly 5 parties (safety check for JSON migration)
        while (partyCharacters.Count < 5)
        {
            partyCharacters.Add(new PartyCharacters($"Party{partyCharacters.Count}"));
        }

        characters = LoadedValues.characters;

        // Volume control is now handled entirely by ConfMenu

        if (LoadedValues.didLoad) StartCoroutine(LoadCharacterUI());

        buffs = LoadedValues.buffs; // loads the buffs

        for (int i = 0; i < buffs.Count; i++) // loops through the buffs to be loaded to the game and config UI
        {
            new AddBuffNDebuff(i, this);
        }

        GameObject.Find("BuffsConfs").SetActive(false); // sets the buffs UI to inactive
        GameObject.Find("PartyConfs").SetActive(false); // sets the party UI to inactive

        infiniteToggle.GetComponent<Toggle>().isOn = LoadedValues.infinity; // loads the infinity toggle, only works on reset

        infiniteToggle.GetComponent<Toggle>().onValueChanged.AddListener((bool b) => LoadedValues.SaveInfinity(b)); // when the toggle is changed, saves the infinity toggle value

        ChangeOnlyNulls.GetComponent<Toggle>().isOn = LoadedValues.changeOnlyNulls; // loads the ChangeOnlyNulls toggle, only works on reset

        ChangeOnlyNulls.GetComponent<Toggle>().onValueChanged.AddListener((bool b) => LoadedValues.SaveChangeOnlyNulls(b)); // when the toggle is changed, saves the ChangeOnlyNulls toggle value

        ShowHP.GetComponent<Toggle>().isOn = LoadedValues.showHP;

        ShowHP.GetComponent<Toggle>().onValueChanged.AddListener((bool b) => LoadedValues.SaveShowHP(b));

        canCountdown = false;

        ppLeft = MaxPP; // sets the power left to the max power

        SetEnergyTime();

        enemyActions = 1;
    }

    private void Update()
    {
        // Volume control is now handled entirely by ConfMenu

        Coins.transform.GetChild(1).GetComponent<TextMeshProUGUI>().text = money.ToString();

        for (int i = 0; i < pC.Length; i++) if (pC[i] != null && (pC[i].isEnemy || GetCharacter(pC[i]) == -1)) pC[i] = null; // removes the player if they stopped existing in the characters list or if they are an enemy

        for (int i = 0; i < eC.Length; i++) if (eC[i] != null && (!eC[i].isEnemy || GetCharacter(eC[i]) == -1)) eC[i] = null; // same thing to the loop above but for the enemies

        foreach (var enemy in eC) if (enemy != null && partySelected != -1) // sets the max hp according to the sum of the active characters hp plus the stock weight times the sum of the stock characters hp
            {

                float fLevel = Mathf.Min(1, (Mathf.Max(enemy.level, 0.5f) / Mathf.Max(CalculatePartyAverageLevel(), 1)));

                float sumOfActives = partyCharacters[partySelected].activeCharacters.Where(ac => ac != null).Sum(ac => ac.stats.GetHp(ac.level));
                float sumOfStock = partyCharacters[partySelected].stockCharacters.Where(sc => sc != null && partyCharacters[partySelected].activeCharacters.FirstOrDefault(ac => ac == sc) == null).Sum(sc => sc.stats.GetHp(sc.level));

                // max ( baseHP, ((ActiveSum + stockWeight * StockSum) / NumBosses) * fLevel * Difficulty)
                float calculatedHP = Mathf.Max(enemy.stats.GetHp(enemy.level),
                    ((sumOfActives + StockWeight * sumOfStock) / eC.Count(ec => ec != null)) * fLevel * Difficulty);

                if (enemy.maxHP != (long)calculatedHP && (!enemy.hpAddapted || (playerTurns == 0 && playerActions == 1 && playerEmptyActions == 0)) && !enemy.IsDead) // checks if it should update the hp for the enemy
                {
                    enemy.hpAddapted = true;
                    enemy.maxHP = (long)calculatedHP;
                    enemy.Heal(enemy.maxHP);
                }
            }

        if (GetNumberOfEnemies() == 1)
        {
            enemySelected = new bool[] { true, false, false, false };
        }

            //resets the player's hp, to prevent the player to have the hp of an enemy
            foreach (var player in pC) if (player != null && player.maxHP != player.stats.GetHp()[player.level]) { player.SetLevel(player.level); player.hpAddapted = false; }

        var notNullEnemies = eC.Where(e => e != null && !e.IsDead).ToArray();
        var nullEnemies = eC.Where(e => e == null || (e != null && e.IsDead)).ToArray();

        if (GetNumberOfEnemies() != 4) eC = notNullEnemies.Concat(nullEnemies).ToArray();

        partyCharacters.ForEach(party =>
        {
            party.activeCharacters = party.activeCharacters.Select(c => party.stockCharacters.FirstOrDefault(sC => sC == c) == null ? null : c).ToArray();
            party.stockCharacters = party.stockCharacters.Select(c => c != null && c.isEnemy ? null : c).ToArray();

            var hasSeen = new HashSet<BattleCharacter>();

            party.stockCharacters = party.stockCharacters.Select(c => c == null || !hasSeen.Add(c) ? null : c).ToArray();

        });

        if (pC[Array.IndexOf(playerSelected, true)] != null &&
            (pC[Array.IndexOf(playerSelected, true)].IsDead || pC[Array.IndexOf(playerSelected, true)].isStunned)
            && pC.Any(pc => pc == null || (pc != null && !pc.IsDead && !pc.isStunned && playersInterface[Array.IndexOf(pC, pc)].GetComponent<PlayerInterface>().AttackCooldown.fillAmount <= 0.001f)))
        {
            BattleCharacter character = pC.FirstOrDefault(pc => pc == null || (pc != null && !pc.IsDead && !pc.isStunned && playersInterface[Array.IndexOf(pC, pc)].GetComponent<PlayerInterface>().AttackCooldown.fillAmount <= 0.001f));
            int index = character != null || Array.IndexOf(pC, character) != -1 ? Array.IndexOf(pC, character) : 3;
            SetPlayerSelected(index);
        }

        if (IsThereCharacters() && canCheckWhoStarts && Camera.main != null)
        {
            float AGIparty = 0;
            float AGIenemies = 0;

            foreach (var ec in eC)
            {
                if (ec != null) AGIenemies += ec.mods.GetSpeed();
            }

            foreach (var pc in pC)
            {
                if (pc != null) AGIparty += pc.mods.GetSpeed();
            }

            AGIparty /= partyCharacters[partySelected].activeCharacters.Count(pc => pc != null);
            AGIenemies /= eC.Count(pc => pc != null);
            if (AGIparty < AGIenemies || (50 < UnityEngine.Random.Range(0, 100) && Mathf.Abs(AGIparty - AGIenemies) < 0.0001f))
            {
                playersInterface[GetSelectedPlayer()].GetComponent<PlayerInterface>().didCrit = true;
                playerTurn = false;
                enemyFirstStrike = true;
                GameObject.Find("FirstAdvantage").GetComponent<FirstAdvantageAnim>().PlayCenterAnimation();
                StartCoroutine(grid.SwitchTurns());
            }
            else
            {
                GameObject.Find("FirstAdvantage").GetComponent<FirstAdvantageAnim>().PlayCenterAnimation();
            }
            canCheckWhoStarts = false;
        }

        // if there are more enemies in the characters list than in the enemies array, it fills it up, it only does that if the enemy array is not full,
        // dead enemies are counted as not null, if you want to add new enemies to replace the dead ones, just call SetEnemyCharacter with false
        // default state will only replace ones that are null
        if ((Array.IndexOf(eC, null) != -1 || eC.FirstOrDefault(ec => ec.IsDead) != null) && enemies.Count >= eC.Length)
        {
            BattleCharacter enemy = enemies.Find(ec => ec != null && !ec.IsDead && Array.IndexOf(eC, ec) == -1); // gets the first enemy that isn't dead and isn't being used in the enemies array

            if (enemy != null) StartCoroutine(SetEnemyCharacter(enemy, ChangeOnlyNulls.GetComponent<Toggle>().isOn)); // if found switches the character
        }

        if (!enemies.Any(ec => ec != null && !ec.IsDead) || (!eC.Any(ec => ec != null && !ec.IsDead) && ChangeOnlyNulls.GetComponent<Toggle>().isOn)) // if there are enemies in the enemies array it checks if all of them are dead, if they are, it shows the win popup
        {
            WinPopup.SetActive(eC.All(ec => ec?.IsDead ?? true));
        }

        if (Array.FindLast(pC, pc => pc != null) != null) // same thing but for the players and it shows the fail popup
        {
            bool allDead = pC.All(pc => pc?.IsDead ?? true);
            FailPopup.SetActive(allDead);
        }

        LosePopup.SetActive(!IsPPLeft());
    }

    IEnumerator LoadCharacterUI()
    {
        GameObject loadingCameraOBJ = GameObject.Find("LoadingCamera");
        Camera loadingCamera = loadingCameraOBJ.GetComponent<Camera>();

        loadingCamera.enabled = true;

        Image loadingBar = GameObject.Find("LoadingBar").GetComponent<Image>();


        for (int i = 0; i < characters.Count; i++) // loops through the characters to be loaded to the game and config UI
        {
            characters[i].ResetBuffs(); // resets the buffs, just in case there was still buffs applied

            new AddCharacter(i, this); // adds the character to the configs UI
            if (characters[i].isEnemy) StartCoroutine(SetEnemyCharacter(characters[i])); // if the character is an enemy, sets it as an enemy in the enemy array
            characters[i].SetLevel(characters[i].level); // resets the hp by setting their level to the current level
            characters[i].configsHandler = this; // sets the reference of the configsHandler script
            characters[i].hpAddapted = false;

            StartCoroutine(characters[i].DidStunEnded());

            loadingBar.fillAmount = i / (float)characters.Count;

            LoadedValues.loadingInfoText = "Loading Character UI (" + (i + 1) + " of " + characters.Count + ")";

            if (i % 10 == 0) yield return new WaitForSeconds(0);
        }
        LoadedValues.loadingInfoText = "Loading Complete!";

        loadingBar.fillAmount = 1;

        yield return new WaitForSeconds(0.5f);

        loadingCamera.enabled = false;

        StartCoroutine(GetAllEnemies());
    }


    public IEnumerator EnemyAttack(int totalamount) // function that makes the enemies attack, they attack the same amount that the player attacked them
    {
        totalamount = Mathf.Max(1, totalamount); // makes sure the amount of hits is at least 1
        int amount = totalamount; // sets the amount of hits to the total amount of hits


        do
        {
            selectedEnemy = Array.IndexOf(enemySelected, true);

            if (selectedEnemy == -1 || (selectedEnemy != -1 && enemiesInterface[selectedEnemy].GetComponent<EnemyInterface>().AttackCooldown.fillAmount > 0.0001f))
            {
                do
                {
                    if (eC.FirstOrDefault(ec => ec != null && !ec.IsDead && !ec.isStunned) == null || !IsThereAnyEnemyNotInCooldown()) break;
                    yield return null;
                    selectedEnemy = UnityEngine.Random.Range(0, eC.Length);
                } while (eC[selectedEnemy] == null || eC[selectedEnemy].IsDead || eC[selectedEnemy].isStunned || enemiesInterface[selectedEnemy].GetComponent<EnemyInterface>().AttackCooldown.fillAmount > 0.0001f);
            }

            yield return new WaitForSeconds(1f);

            int hits = 0;

            for (int i = 0; i < amount; i++) // loops the amount of hits
            {
                if (Array.FindIndex(pC, pc => pc != null && !pc.IsDead) == -1) break; // if there are no players alive or usable, it breaks
                if (Array.FindIndex(eC, ec => ec != null && !ec.IsDead && !ec.isStunned) == -1) break; // same thing but for the enemies
                if (!IsThereAnyEnemyNotInCooldown() || selectedEnemy == -1) break;

                pIndex = 0; // index of the player that will attack
                do
                {
            yield return new WaitForSeconds(0);
                    pIndex = UnityEngine.Random.Range(0, 4); // picks a random player
                                                             // makes sure the player is alive, is usable and is not in cooldown, if all players are in cooldown, it ignores the cooldown case
                } while (pC[pIndex] == null || pC[pIndex].IsDead || (playersInterface[pIndex].GetComponent<PlayerInterface>().AttackCooldown.fillAmount > 0.0001f && IsThereAnyPlayerNotInCooldown()));


                if (eC[selectedEnemy] == null || eC[selectedEnemy].IsDead || eC[selectedEnemy].isStunned) // switches to the next enemy that isn't dead or null
                {
                    BattleCharacter nextEnemy = eC.FirstOrDefault(ec => ec != null && !ec.IsDead && !ec.isStunned); // gets the first enemy that isn't dead or null

                    if (nextEnemy != null) // if there is a next enemy, it sets it as the selected enemy
                    {
                        selectedEnemy = Array.IndexOf(eC, nextEnemy);
                    }
                }
                var animation = enemiesInterface[selectedEnemy].GetComponent<EnemyAnimation>().enemyAnimation.AnimationState.Data.SkeletonData.FindAnimation("Attack");

                string animationName = animation == null ? "Idle_Break" : "Attack";

                enemiesInterface[selectedEnemy].GetComponent<EnemyAnimation>().enemyAnimation.AnimationState.SetAnimation(0, animationName, false);

                yield return new WaitForSeconds(0.5f); // waits 0.5 seconds

                Types type = (Types)UnityEngine.Random.Range(0, Enum.GetValues(typeof(Types)).Length); // picks a random type

                // hits the player
                int enemySpAtk = 0;
                if (int.TryParse(eC[selectedEnemy].skills.GetValues(type).spAtk, out int spAtkValue))
                    enemySpAtk = spAtkValue;

                bool didHit = !pC[pIndex].Damage(
                    eC[selectedEnemy].stats.GetAtk(eC[selectedEnemy].level),
                    enemySpAtk,
                    type,
                    eC[selectedEnemy]
                    );
                if (didHit) hits++;

            }

            if (!IsThereAnyEnemyNotInCooldown() || Array.FindIndex(eC, ec => ec != null && !ec.IsDead && !ec.isStunned) == -1) lostActions = enemyEmptyActions;
            enemyActions--;
            enemyEmptyActions++;
            yield return null;
            try
            {

                // gets the selected enemy to check if it did crit, if it didn't crit, it applies the cooldown
                EnemyInterface enemy = enemiesInterface[selectedEnemy].GetComponent<EnemyInterface>();

                if (enemy.didCrit) enemy.didCrit = false;
                else if (enemy.AttackCooldown.fillAmount <= 0.0001f) enemy.AttackCooldown.fillAmount = 1;
                enemiesInterface[selectedEnemy].GetComponent<EnemyAnimation>().enemyAnimation.AnimationState.SetAnimation(0, "Idle", true);

            }
            catch (Exception ex)
            {
                Debug.LogError("An error occurred: " + "Index: " + selectedEnemy + " " + ex.Message);
            }

            amount = (int)Mathf.Max(1, totalamount / FractionOfAttacksPerAction); // sets the amount of hits to the total amount of hits divided by the fraction of attacks per action

        } while (enemyActions > 0); // loops until there are no more actions for the enemies

        try
        {
            enemiesInterface[selectedEnemy].GetComponent<EnemyAnimation>().enemyAnimation.AnimationState.SetAnimation(0, "Idle", true);
        }
        catch
        {
            yield break;
        }

        grid.attacks = 0; // after all the hits are finished, it resets the score

    }

    public void GuardCharacter() // sets the selected player as guarding
    {
        pC[Array.IndexOf(playerSelected, true)].isGuard = true; // sets the player as guarding, there's no need to check if the player is dead or not null because the Action Menu can't be opened if the player is dead or null

        GameObject selectedPlayer = playersInterface[Array.IndexOf(playerSelected, true)]; // gets the selected player GameObject

        Sprite icon = Resources.Load<Sprite>("Sprites/StatusEffects/Guard"); // loads the guard icon

        selectedPlayer.GetComponent<PlayerInterface>().didCrit = true;

        selectedPlayer.GetComponent<PlayerInterface>().AddStatusIcon(icon, "Guarding"); // adds the guard icon to that player interface

        playerTurn = false;
        StartCoroutine(grid.SwitchTurns()); // switches the turns
    }

    public bool IsGuarding() // returns if the selected player is guarding
    {
        if (pC[Array.IndexOf(playerSelected, true)] == null) return false; // if the player doesn't exist, it returns false
        return pC[Array.IndexOf(playerSelected, true)].statusIcons.TryGetValue("Guarding", out _); // otherwise, it checks if the player is guarding
    }

    public void RunesDamage(Types type) // damages the selected enemy with the rune type
    {

        if (!IsPPLeft()) return; // if there is no power left, it returns

        selectedEnemy = Array.IndexOf(enemySelected, true);


        if (selectedEnemy == -1)
        {
            if (eC.FirstOrDefault(ec => ec != null && !ec.IsDead) == null) return;
            do
            {
                selectedEnemy = UnityEngine.Random.Range(0, eC.Length);
            } while (eC[selectedEnemy] == null || eC[selectedEnemy].IsDead);
        }

        if (eC[selectedEnemy] == null || eC[selectedEnemy].IsDead) // switches to the next enemy if the selected one is dead or null
        {
            BattleCharacter nextEnemy = eC.FirstOrDefault(ec => ec != null && !ec.IsDead); // gets the first enemy that isn't dead or null

            if (nextEnemy != null) // if there is a next enemy, it selects it
            {
                int index = Array.IndexOf(eC, nextEnemy);

                SetEnemySelected(index);
            }
        }

        if (pC[Array.IndexOf(playerSelected, true)] == null || pC[Array.IndexOf(playerSelected, true)].IsDead || pC[Array.IndexOf(playerSelected, true)].isStunned) // same thing but for the players
        {
            BattleCharacter nextPlayer = pC.FirstOrDefault(pc => pc != null && !pc.IsDead && !pc.isStunned);

            if (nextPlayer != null)
            {
                int index = Array.IndexOf(pC, nextPlayer);

                SetPlayerSelected(index);
            }
        }

        // checks if the player and the enemy selected are alive, not null and that the player is not in cooldown
        if (eC[selectedEnemy] != null && !eC[selectedEnemy].IsDead && pC[Array.IndexOf(playerSelected, true)] != null &&
            !pC[Array.IndexOf(playerSelected, true)].IsDead && IsThereAnyPlayerNotInCooldown())
        {
            BattleCharacter atacker = pC[Array.IndexOf(playerSelected, true)]; // gets the player that is attacking

            int dmg = atacker.stats.GetAtk(atacker.level); // gets the atk of the player that is attacking at its current level

            int spAtk = 0;
            if (int.TryParse(atacker.skills.GetValues(type).spAtk, out int spAtkValue))
                spAtk = spAtkValue; // gets the spAtk of the player that is attacking for the selected type

            eC[selectedEnemy].Damage(dmg, spAtk, type, atacker); // and deals the damage to the enemy

            EnemyAnimation enemy = enemiesInterface[selectedEnemy].GetComponent<EnemyAnimation>();

            if (!enemy.gotHitted) StartCoroutine(enemy.HitAnimation(7)); // plays the hit animation

            StartCoroutine(ScreenShake(shakeStrength, 7)); // makes the background and the enemies shake
        }

    }


    public bool DamageLabel(BattleCharacter reciver, long amount, Types type, bool critical = false, bool weak = false, bool superEffective = false, bool notEffective = false, bool missed = false) // creates a damage label
    {
        GameObject dmgStarPrefab = Resources.Load<GameObject>("Prefabs/DamageStar"); // loads the damage star prefab
        GameObject reciverCharacter; // the character that received the damage
        GameObject attackerCharacter; // the character that did the damage
        GameObject dmgLabelPrefab;
      
        if (!reciver.isEnemy) // if the reciver is a player it gets the player GameObject
        {
            int index = Array.IndexOf(pC, reciver); // gets the index of the player that received the damage

            reciverCharacter = playersInterface[index]; // gets the player GameObject

            attackerCharacter = enemiesInterface[selectedEnemy]; // gets the enemy GameObject

            BattleCharacter attackerLabel = GetEnemyCharacter(selectedEnemy);

            switch (type)  // If any spkAtk is 0 the dmgLabel doesnt display the type icon
            {
                case Types.Strength:
                    if (int.TryParse(attackerLabel.skills.GetValues(Types.Strength).spAtk, out int strSpAtk) && strSpAtk <= 0)
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DamageLabelEnemyPhy");
                    }
                    else
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DamageLabelStrength"); // loads the damage label type prefab
                        dmgLabelPrefab.GetComponent<TextMeshPro>().fontMaterial = Resources.Load<Material>("Materials/LiberationSansSDFDropShadowEnemy");
                    }
                    break;
                case Types.Magic:
                    if (int.TryParse(attackerLabel.skills.GetValues(Types.Magic).spAtk, out int magSpAtk) && magSpAtk <= 0)
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DamageLabelEnemyPhy");
                    }
                    else
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DamageLabelMagic"); // loads the damage label type prefab
                        dmgLabelPrefab.GetComponent<TextMeshPro>().fontMaterial = Resources.Load<Material>("Materials/LiberationSansSDFDropShadowEnemy");
                    }
                    break;
                case Types.Fire:
                    if (int.TryParse(attackerLabel.skills.GetValues(Types.Fire).spAtk, out int firSpAtk) && firSpAtk <= 0)
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DamageLabelEnemyPhy");
                    }
                    else
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DamageLabelFire"); // loads the damage label type prefab
                        dmgLabelPrefab.GetComponent<TextMeshPro>().fontMaterial = Resources.Load<Material>("Materials/LiberationSansSDFDropShadowEnemy");
                    }
                    break;
                case Types.Venom:
                    if (int.TryParse(attackerLabel.skills.GetValues(Types.Venom).spAtk, out int venSpAtk) && venSpAtk <= 0)
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DamageLabelEnemyPhy");
                    }
                    else
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DamageLabelVenom"); // loads the damage label air type prefab
                        dmgLabelPrefab.GetComponent<TextMeshPro>().fontMaterial = Resources.Load<Material>("Materials/LiberationSansSDFDropShadowEnemy");
                    }
                    break;
                case Types.Possession:
                    if (int.TryParse(attackerLabel.skills.GetValues(Types.Possession).spAtk, out int posSpAtk) && posSpAtk <= 0)
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DamageLabelEnemyPhy");
                    }
                    else
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DamageLabelPossession"); // loads the damage label air type prefab
                        dmgLabelPrefab.GetComponent<TextMeshPro>().fontMaterial = Resources.Load<Material>("Materials/LiberationSansSDFDropShadowEnemy");
                    }
                    break;
                case Types.Electricity:
                    if (int.TryParse(attackerLabel.skills.GetValues(Types.Electricity).spAtk, out int eleSpAtk) && eleSpAtk <= 0)
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DamageLabelEnemyPhy");
                    }
                    else
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DamageLabelElectricity"); // loads the damage label air type prefab
                        dmgLabelPrefab.GetComponent<TextMeshPro>().fontMaterial = Resources.Load<Material>("Materials/LiberationSansSDFDropShadowEnemy");

                    }
                    break;
                case Types.Acid:
                    if (int.TryParse(attackerLabel.skills.GetValues(Types.Acid).spAtk, out int aciSpAtk) && aciSpAtk <= 0)
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DamageLabelEnemyPhy");
                    }
                    else
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DamageLabelAcid"); // loads the damage label air type prefab
                        dmgLabelPrefab.GetComponent<TextMeshPro>().fontMaterial = Resources.Load<Material>("Materials/LiberationSansSDFDropShadowEnemy");
                    }
                    break;
                case Types.Frost:
                    if (int.TryParse(attackerLabel.skills.GetValues(Types.Frost).spAtk, out int froSpAtk) && froSpAtk <= 0)
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DamageLabelEnemyPhy");
                    }
                    else
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DamageLabelFrost"); // loads the damage label air type prefab
                        dmgLabelPrefab.GetComponent<TextMeshPro>().fontMaterial = Resources.Load<Material>("Materials/LiberationSansSDFDropShadowEnemy");
                    }
                    break;
                default:
                    dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DamageLabelEnemy");
                    break;
            }

            StartCoroutine(reciverCharacter.GetComponent<PlayerInterface>().GotHitted()); // plays the hitted animation
        }
        else // otherwise it gets the enemy GameObject
        {
            int index = Array.IndexOf(eC, reciver); // gets the index of the enemy that received the damage

            reciverCharacter = enemiesInterface[index]; // gets the enemy GameObject

            index = Array.IndexOf(playerSelected, true); // gets the index of the selected player

            attackerCharacter = playersInterface[index]; // gets the player GameObject

            BattleCharacter attackerLabel = GetPlayerCharacter(index);

            switch (type)  // If any spkAtk is 0 the dmgLabel doesnt display the type icon
            {
                case Types.Strength:
                    if (int.TryParse(attackerLabel.skills.GetValues(Types.Strength).spAtk, out int strSpAtk2) && strSpAtk2 <= 0)
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DamageLabel");
                        physicalAttack = true;
                    }
                    else
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DamageLabelStrength"); // loads the damage label type prefab
                        dmgLabelPrefab.GetComponent<TextMeshPro>().fontMaterial = Resources.Load<Material>("Materials/LiberationSansSDFDropShadow");
                        physicalAttack = false;
                    }
                    break;
                case Types.Magic:
                    if (int.TryParse(attackerLabel.skills.GetValues(Types.Magic).spAtk, out int magSpAtk2) && magSpAtk2 <= 0)
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DamageLabel");
                        physicalAttack = true;
                    }
                    else
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DamageLabelMagic"); // loads the damage label type prefab
                        dmgLabelPrefab.GetComponent<TextMeshPro>().fontMaterial = Resources.Load<Material>("Materials/LiberationSansSDFDropShadow");
                        physicalAttack = false;
                    }
                    break;
                case Types.Fire:
                    if (int.TryParse(attackerLabel.skills.GetValues(Types.Fire).spAtk, out int firSpAtk2) && firSpAtk2 <= 0)
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DamageLabel");
                        physicalAttack = true;
                    }
                    else
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DamageLabelFire"); // loads the damage label type prefab
                        dmgLabelPrefab.GetComponent<TextMeshPro>().fontMaterial = Resources.Load<Material>("Materials/LiberationSansSDFDropShadow");
                        physicalAttack = false;
                    }
                    break;
                case Types.Venom:
                    if (int.TryParse(attackerLabel.skills.GetValues(Types.Venom).spAtk, out int venSpAtk2) && venSpAtk2 <= 0)
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DamageLabel");
                        physicalAttack = true;
                    }
                    else
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DamageLabelVenom"); // loads the damage label type prefab
                        dmgLabelPrefab.GetComponent<TextMeshPro>().fontMaterial = Resources.Load<Material>("Materials/LiberationSansSDFDropShadow");
                        physicalAttack = false;
                    }
                    break;
                case Types.Possession:
                    if (int.TryParse(attackerLabel.skills.GetValues(Types.Possession).spAtk, out int posSpAtk2) && posSpAtk2 <= 0)
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DamageLabel");
                        physicalAttack = true;
                    }
                    else
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DamageLabelPossession"); // loads the damage label type prefab
                        dmgLabelPrefab.GetComponent<TextMeshPro>().fontMaterial = Resources.Load<Material>("Materials/LiberationSansSDFDropShadow");
                        physicalAttack = false;
                    }
                    break;
                case Types.Electricity:
                    if (int.TryParse(attackerLabel.skills.GetValues(Types.Electricity).spAtk, out int eleSpAtk2) && eleSpAtk2 <= 0)
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DamageLabel");
                        physicalAttack = true;
                    }
                    else
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DamageLabelElectricity"); // loads the damage label type prefab
                        dmgLabelPrefab.GetComponent<TextMeshPro>().fontMaterial = Resources.Load<Material>("Materials/LiberationSansSDFDropShadow");
                        physicalAttack = false;
                    }
                    break;
                case Types.Acid:
                    if (int.TryParse(attackerLabel.skills.GetValues(Types.Acid).spAtk, out int aciSpAtk2) && aciSpAtk2 <= 0)
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DamageLabel");
                        physicalAttack = true;
                    }
                    else
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DamageLabelAcid"); // loads the damage label type prefab
                        dmgLabelPrefab.GetComponent<TextMeshPro>().fontMaterial = Resources.Load<Material>("Materials/LiberationSansSDFDropShadow");
                        physicalAttack = false;
                    }
                    break;
                case Types.Frost:
                    if (int.TryParse(attackerLabel.skills.GetValues(Types.Frost).spAtk, out int froSpAtk2) && froSpAtk2 <= 0)
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DamageLabel");
                        physicalAttack = true;
                    }
                    else
                    {
                        dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DamageLabelFrost"); // loads the damage label type prefab
                        dmgLabelPrefab.GetComponent<TextMeshPro>().fontMaterial = Resources.Load<Material>("Materials/LiberationSansSDFDropShadow");
                        physicalAttack = false;
                    }
                    break;
                default:
                    dmgLabelPrefab = Resources.Load<GameObject>("Prefabs/DamageLabel");
                    physicalAttack = true;
                    break;
            }
        }
        if (missed)
        {
            if (reciver.isEnemy) enemyActions = Mathf.Min(4, enemyActions + 1);
            else playerActions = Mathf.Min(4, playerActions + 1);
        }

        if ((critical || weak || superEffective) && !missed) // if the damage is critical, it doesn't let the character enter the cooldown
        {
            int extraActions = (critical ? 1 : 0) + (weak || superEffective ? 1 : 0);
            if (reciver.isEnemy) { attackerCharacter.GetComponent<PlayerInterface>().didCrit = true; playerActions = Mathf.Min(4 - playerEmptyActions, extraActions + playerActions); }
            else { attackerCharacter.GetComponent<EnemyInterface>().didCrit = true; enemyActions = Mathf.Min(4 - enemyEmptyActions, extraActions + enemyActions); }
        }

        GameObject dmgLabel = Instantiate(dmgLabelPrefab, reciverCharacter.transform.position, Quaternion.identity); // creates the damage label
        GameObject dmgStar = Instantiate(dmgStarPrefab, attackerCharacter.transform.position, Quaternion.identity); // creates the damage star
        GameObject hitEffectPrefab = Resources.Load<GameObject>("Prefabs/HitEffect"); // loads the hit effect prefab

        Instantiate(hitEffectPrefab, reciverCharacter.transform.position - new Vector3(0, 0, 0.5f), Quaternion.identity); // creates the hit effect

        StartCoroutine(dmgStar.GetComponent<DamageStar>().MoveObject(reciverCharacter.transform.position)); // makes the damage star move


        dmgLabel.transform.localScale /= 13f; // makes the damage label smaller, this is the reason that the scale needs to be currected or it would be different on the enemies and players

        dmgLabel.transform.position += new Vector3(UnityEngine.Random.Range(-0.5f, 0.6f), UnityEngine.Random.Range(-0.3f, 0.4f), -0.1f); // randomizes the start position of the damage label

        if (reciver.isStunned && reciver.isEnemy && reciver.canReceiveCoins)
        {
            GameObject coinLabel = Instantiate(Resources.Load<GameObject>("Prefabs/DamageLabelCoin"), reciverCharacter.transform.position, Quaternion.identity); // creates the damage label
            coinLabel.transform.localScale /= 13f; // makes the damage label smaller, this is the reason that the scale needs to be currected or it would be different on the enemies and players
            coinLabel.transform.position += new Vector3(UnityEngine.Random.Range(-0.5f, 0.6f), UnityEngine.Random.Range(-0.3f, 0.4f), -0.1f); // randomizes the start position of the damage label
            int amountGained = GoldenStrike[Mathf.Min(grid.combo, 6)];
            coinLabel.GetComponent<TextMeshPro>().text = "+ " + amountGained + " Gold";
            money += amountGained;
        }

        damageNumber = amount;

        // sets the text of the damage label            // Formats numbers 1000 to 1.000
        string amountFormatted = amount.ToString("N0", new CultureInfo("de-DE"));
        string damageText = missed ? "MISSED" : "-" + amountFormatted;
        if (!missed)
        {
                if (pIndex == 0 && !reciver.isEnemy) // makes sure the label doesnt go offscreen
            {
                if (critical) damageText += "\n<align=center><size=50%>CRIT</size></align>";
                if (weak) damageText += "\n<align=center><size=50%>WEAK</size></align>";
                if (superEffective) damageText += "\n<align=center><size=50%>SUPER EFFECTIVE</size></align>";
                if (notEffective) damageText += "\n<align=center><size=50%>NOT EFFECTIVE</size></align>";
            }
             else if (pIndex == 3 && !reciver.isEnemy) // // makes sure the label doesnt go offscreen
            {
                if (critical) damageText += "\n<align=center><size=50%>CRIT</size></align>";
                if (weak) damageText += "\n<align=center><size=50%>WEAK</size></align>";
                if (superEffective) damageText += "\n<align=center><size=50%>SUPER EFFECTIVE</size></align>";
                if (notEffective) damageText += "\n<align=center><size=50%>NOT EFFECTIVE</size></align>";
            }
            else
            {
                if (critical) damageText += "\n<align=center><size=50%>CRIT</size></align>";
                if (weak) damageText += "\n<align=center><size=50%>WEAK</size></align>";
                if (superEffective) damageText += "\n<align=center><size=50%>SUPER EFFECTIVE</size></align>";
                if (notEffective) damageText += "\n<align=center><size=50%>NOT EFFECTIVE</size></align>";
            }
        }
        dmgLabel.GetComponent<TextMeshPro>().text = damageText;

        dmgLabel.GetComponent<TextMeshPro>().fontStyle = FontStyles.Bold; // makes the text bold

        return true;
    }

    //**************************//
    //**************************//
    // Buffs and Debuffs System //
    //**************************//
    //**************************//

    public int AddBuffNDebuff() // adds a new buff and returns its index
    {
        buffs.Add(new BuffNDebuffs(buffs.Count));

        return buffs.Count - 1;
    }

    public void RemoveBuff(BuffNDebuffs buff) => buffs.Remove(buff); // removes a buff from the list of buffs

    public int GetBuff(BuffNDebuffs buff) => buffs.IndexOf(buff); // returns the index of the buff

    public BuffNDebuffs GetBuff(int index) => buffs[index]; // returns the buff at the index

    public List<BuffNDebuffs> GetBuffs() => buffs; // returns the list of buffs

    public void SetBuff(BuffNDebuffs buff, int index) // sets the buff at the index in the skill section on the Action menu
    {
        SkillSelection.SetActive(true); // sets the skill selection to active

        List<GameObject> buffsList = new(); // creates a temporary list of gameobjects

        SkillSelection.transform.GetChild(0).GetComponents<Button>().ToList().ForEach(b => buffsList.Add(b.gameObject)); // adds the gameobjects to the list that are in the BuffL GameObject

        SkillSelection.transform.GetChild(1).GetComponents<Button>().ToList().ForEach(b => buffsList.Add(b.gameObject)); // same thing but for the DebuffL GameObject

        GameObject buffOBJ = buffsList.FindLast(b => b.GetComponent<SkillUIHandler>().skill == buffs[index]); // gets the last gameobject in the list that has the same buff


        if (buffOBJ != null) buffOBJ.GetComponent<SkillUIHandler>().UpdateSkill(buff); // if it's not null it updates the skill

        buffs[index] = buff; // updates the buff the buff on the list

        SkillSelection.SetActive(false); // sets the skill selection to inactive
    }

    public void SetBuff(BuffNDebuffs buff) // sets a buff to the selected player
    {
        BattleCharacter character = pC[Array.IndexOf(playerSelected, true)]; // gets the player that is selected

        PlayerInterface playerInterface = playersInterface[Array.IndexOf(playerSelected, true)].GetComponent<PlayerInterface>(); // gets the player interface of the player that is selected

        bool isBuff = true; // boolean that checks if the buff is a buff or a debuff, just to add the correct icon to the player interface

        if (ppLeft >= buff.pp && !character.statusIcons.TryGetValue(buff.name, out _)) // if the player has enough energy
        {
            ppLeft -= buff.pp; // reduses the energy left
            switch (buff.variable) // switch case with the variable, the cases are self explanatory, Def is for defense, Atk is for attack, SpDef is for special defense and SpAtk is for special attack
            {
                case "Def":
                    if (buff.sign == "+")
                    {
                        character.defAdders.Add((int)buff.value);
                        if (buff.value <= 0) isBuff = false;
                    }
                    if (buff.sign == "-")
                    {
                        character.defAdders.Add((int)-buff.value);
                        if (buff.value >= 0) isBuff = false;
                    }
                    if (buff.sign == "=")
                    {
                        character.defEquals.Add((int)buff.value);
                        if (buff.value <= 0) isBuff = false;
                    }
                    if (buff.sign == "*")
                    {
                        character.defMultipliers.Add(buff.value);
                        if (buff.value < 1) isBuff = false;
                    }
                    break;
                case "Atk":
                    if (buff.sign == "+")
                    {
                        character.atkAdders.Add((int)buff.value);
                        if (buff.value <= 0) isBuff = false;
                    }
                    if (buff.sign == "-")
                    {
                        character.atkAdders.Add((int)-buff.value);
                        if (buff.value >= 0) isBuff = false;
                    }
                    if (buff.sign == "=")
                    {
                        character.atkEquals.Add((int)buff.value);
                        if (buff.value <= 0) isBuff = false;
                    }
                    if (buff.sign == "*")
                    {
                        character.atkMultipliers.Add(buff.value);
                        if (buff.value < 1) isBuff = false;
                    }
                    break;
                case "Sp.Def":
                    if (buff.sign == "+")
                    {
                        character.spDefAdders.Add((int)buff.value);
                        if (buff.value <= 0) isBuff = false;
                    }
                    if (buff.sign == "-")
                    {
                        character.spDefAdders.Add((int)-buff.value);
                        if (buff.value >= 0) isBuff = false;
                    }
                    if (buff.sign == "=")
                    {
                        character.spDefEquals.Add((int)buff.value);
                        if (buff.value <= 0) isBuff = false;
                    }
                    if (buff.sign == "*")
                    {
                        character.spDefMultipliers.Add(buff.value);
                        if (buff.value < 1) isBuff = false;
                    }
                    break;
                case "Sp.Atk":
                    if (buff.sign == "+")
                    {
                        character.spAtkAdders.Add((int)buff.value);
                        if (buff.value <= 0) isBuff = false;
                    }
                    if (buff.sign == "-")
                    {
                        character.spAtkAdders.Add((int)-buff.value);
                        if (buff.value >= 0) isBuff = false;
                    }
                    if (buff.sign == "=")
                    {
                        character.spAtkEquals.Add((int)buff.value);
                        if (buff.value <= 0) isBuff = false;
                    }
                    if (buff.sign == "*")
                    {
                        character.spAtkMultipliers.Add(buff.value);
                        if (buff.value < 1) isBuff = false;
                    }
                    break;
            }

            Sprite icon = Resources.Load<Sprite>("Sprites/StatusEffects/" + buff.variable + (isBuff ? "Buff" : "Debuff")); // loads the icon

            playerInterface.AddStatusIcon(icon, buff.name); // adds the icon to the player interface

            playerTurn = false;
            StartCoroutine(grid.SwitchTurns());
        }
    }

    public void RemoveBuffFromCharacter(string name, BattleCharacter character) // removes the buff from the character
    {
        character = GetCharacterByID(character.id); // gets the character, this is just in case the character is not passed as a reference to the list of characters

        BuffNDebuffs buff = buffs.FindLast(b => b.name == name); // finds the buff

        if (name == "Guarding") character.isGuard = false; // if the buff is guarding, the character is no longer guarding

        if (buff == null) return; // if the buff is not found, return

        switch (buff.variable)  // removes the buff from the character
        {
            case "Def":
                if (buff.sign == "+")
                {
                    character.defAdders.Remove((int)buff.value);
                }
                if (buff.sign == "-")
                {
                    character.defAdders.Remove((int)-buff.value);
                }
                if (buff.sign == "=")
                {
                    character.defEquals.Remove((int)buff.value);
                }
                if (buff.sign == "*")
                {
                    character.defMultipliers.Remove(buff.value);
                }
                break;
            case "Atk":
                if (buff.sign == "+")
                {
                    character.atkAdders.Remove((int)buff.value);
                }
                if (buff.sign == "-")
                {
                    character.atkAdders.Remove((int)-buff.value);
                }
                if (buff.sign == "=")
                {
                    character.atkEquals.Remove((int)buff.value);
                }
                if (buff.sign == "*")
                {
                    character.atkMultipliers.Remove(buff.value);
                }
                break;
            case "Sp.Def":
                if (buff.sign == "+")
                {
                    character.spDefAdders.Remove((int)buff.value);
                }
                if (buff.sign == "-")
                {
                    character.spDefAdders.Remove((int)-buff.value);
                }
                if (buff.sign == "=")
                {
                    character.spDefEquals.Remove((int)buff.value);
                }
                if (buff.sign == "*")
                {
                    character.spDefMultipliers.Remove(buff.value);
                }
                break;
            case "Sp.Atk":
                if (buff.sign == "+")
                {
                    character.spAtkAdders.Remove((int)buff.value);
                }
                if (buff.sign == "-")
                {
                    character.spAtkAdders.Remove((int)-buff.value);
                }
                if (buff.sign == "=")
                {
                    character.spAtkEquals.Remove((int)buff.value);
                }
                if (buff.sign == "*")
                {
                    character.spAtkMultipliers.Remove(buff.value);
                }
                break;
        }
    }


    //**************************//
    //**************************//
    //****** Party System ******//
    //**************************//
    //**************************//

    public float CalculatePartyAverageLevel()
    {
        float totalMembers = partyCharacters[partySelected].stockCharacters.Count(sc => sc != null);
        float totalLevels = partyCharacters[partySelected].stockCharacters.Where(sc => sc != null).Sum(sc => sc.level);

        return totalLevels / Mathf.Max(1,totalMembers);
    }

    public bool IsCharacterInActive(BattleCharacter character) => pC.FirstOrDefault(pc => pc == character) != null;


    public void HealDeadCharacters(float percentage)
    {
        foreach (var character in partyCharacters[partySelected].stockCharacters) if(character != null && character.IsDead) character.Heal((int)(character.maxHP * (percentage / 100)) + 1);
    }

    // gets the party characters by index
    public PartyCharacters GetPartyCharacters(int index)
    {
        // Safety check: ensure index is valid and party exists
        if (index < 0 || index >= partyCharacters.Count)
        {
            return null;
        }
        return partyCharacters[index];
    }

    // gets the stock character in the selected party by index
    public BattleCharacter GetStockCharacter(int index)
    {
        if (partySelected == -1) return null;

        return partyCharacters[partySelected].stockCharacters[index];
    }

    // sets the character to the party
    public void SetCharacterToParty(int partyIndex, int slotIndex, bool isActive, BattleCharacter character)
    {
        if (isActive) partyCharacters[partyIndex].activeCharacters[slotIndex] = character;
        else partyCharacters[partyIndex].stockCharacters[slotIndex] = character;

        // Save the updated party configuration to JSON files
        SaveParties(); // Update the reference in LoadedValues
        StartCoroutine(LoadedValues.save()); // Trigger JSON file saving
    }

    // sets the party name
    public void SetPartyName(int index, string name) => partyCharacters[index].name = name;

    // sets the selected party
    public void SetSelectedParty(int index)
    {
        pC = index != -1 ? partyCharacters[index].activeCharacters : new BattleCharacter[4];

        if (index != partySelected)
            SetEnergyTime(0);

        partySelected = index;
    }

    // checks if the character is on the party
    public bool IsCharacterOnParty(BattleCharacter character, int index)
    {
        // Safety check: ensure index is valid and party exists
        if (index < 0 || index >= partyCharacters.Count || partyCharacters[index] == null)
        {
            return false;
        }

        return partyCharacters[index].activeCharacters.Contains(character) || partyCharacters[index].stockCharacters.Contains(character);
    }


    //**************************//
    //**************************//
    //**** Character System ****//
    //**************************//
    //**************************//

    public float CalculateAverageLevel()
    {
        float totalCharacters = eC.Count(ec => ec != null) + partyCharacters[partySelected].stockCharacters.Count(sc => sc != null);
        float totalLevels = eC.Where(ec => ec != null).Sum(ec => ec.level) + partyCharacters[partySelected].stockCharacters.Where(sc => sc != null).Sum(sc => sc.level);

        return totalLevels / totalCharacters;
    }

    public bool IsThereCharacters() // returns if there are players and enemies, if not anything related to damage and turns is not executed
    {
        bool isTherePlayers = Array.FindIndex(pC, pc => pc != null && !pc.IsDead) != -1; // check if there are players by finding the index of the first player that isn't dead or null

        bool isThereEnemies = Array.FindIndex(eC, ec => ec != null && !ec.IsDead) != -1; // same thing but for the enemies

        return isTherePlayers && isThereEnemies;

    }

    public int AddCharacter() // adds a new character
    {
        string id = (characters.Count < 1) ? "0" : (int.Parse(characters[characters.Count - 1].id) + 1).ToString(); // if the list of characters is empty, the id is "0", if not, the id is the last character's id + 1

        // Ensure default lists are not empty, create defaults if needed
        if (defaultStats.Count == 0)
        {
            defaultStats.Add(new CharacterStatus());
        }
        if (defaultSkills.Count == 0)
        {
            defaultSkills.Add(new CharacterSkills());
        }
        if (defaultMods.Count == 0)
        {
            defaultMods.Add(new CharacterMods());
        }

        // Create new random objects instead of reusing templates to ensure randomization
        CharacterStatus cSt = new CharacterStatus(); // Creates random HP, ATK, DEF values
        CharacterSkills cSk = new CharacterSkills(); // Creates random skill values 0-15 for each type
        CharacterMods cMo = new CharacterMods(); // Creates random mod values 1-100

        BattleCharacter character = new(id, cSt, cSk, cMo); // creates a new battle character

        characters.Add(character); // adds the character to the list

        // Update character mapping and mark as dirty for incremental saving
        CharacterChangeTracker.Instance.UpdateCharacterMapping(characters);
        CharacterChangeTracker.Instance.MarkCharacterDirty(character.id);

        // Save the updated character list and default lists to JSON files
        SaveCharacters(); // Update the reference in LoadedValues
        StartCoroutine(LoadedValues.save()); // Trigger JSON file saving

        // Notify virtualized system of character addition
        VirtualizedCharacterIntegration integration = FindObjectOfType<VirtualizedCharacterIntegration>();
        if (integration != null)
        {
            integration.OnCharacterAdded(character);
        }

        return characters.Count - 1; // returns the index
    }

    public void RemoveCharacter(BattleCharacter character) // removes the character
    {
        characters.Remove(character);

        // Character removal requires full save due to index changes
        CharacterChangeTracker.Instance.UpdateCharacterMapping(characters);
        CharacterChangeTracker.Instance.RequireFullSave();

        // Save the updated character list to JSON files
        SaveCharacters(); // Update the reference in LoadedValues
        StartCoroutine(LoadedValues.save()); // Trigger JSON file saving

        // Notify virtualized system of character removal
        VirtualizedCharacterIntegration integration = FindObjectOfType<VirtualizedCharacterIntegration>();
        if (integration != null)
        {
            integration.OnCharacterRemoved(character);
        }
    }

    public int GetCharacter(BattleCharacter chararacter) => characters.IndexOf(chararacter); // gets the index of the character

    public BattleCharacter GetCharacter(int index) => characters[index]; // gets the character at the index

    public List<BattleCharacter> GetCharacters() => characters; // Gets all the characters

    public BattleCharacter GetCharacterByID(string id) => characters.Find(c => c.id == id); // gets the character by id

    public void SetCharacter(BattleCharacter character, int index) // sets the character at the index
    {
        characters[index] = character;

        // Mark character as dirty for incremental saving
        CharacterChangeTracker.Instance.MarkCharacterDirtyByIndex(index, characters);

        // Save the updated character list to JSON files
        SaveCharacters(); // Update the reference in LoadedValues
        StartCoroutine(LoadedValues.save()); // Trigger JSON file saving
    }


    //***************************//
    //***************************//
    //****** Player System ******//
    //***************************//
    //***************************//

    public bool CantPlayerPlay()
    {
        bool isThereAvalables = partyCharacters[partySelected].stockCharacters.All(c => (c?.IsDead ?? true) || (c?.isStunned ?? true));
        return pC.All(pc => (pc?.isStunned ?? isThereAvalables) || (pc?.IsDead ?? isThereAvalables));
    }

    public float CalculateAverageActivePartySpeed()
    {
        float totalCharacters = pC.Count(pc => pc != null);
        float totalSpeed = pC.Where(pc => pc != null).Sum(pc => pc.mods.GetSpeed());

        return totalSpeed / totalCharacters;
    }

    // checks if the selected player is avalable
    public bool IsSelectedPlayerAvalable() => pC[Array.IndexOf(playerSelected, true)] != null && !pC[Array.IndexOf(playerSelected, true)].IsDead && playersInterface[GetSelectedPlayer()].GetComponent<PlayerInterface>().AttackCooldown.fillAmount <= 0;

    public bool IsThereAnyPlayerNotInCooldown()
    {
        bool isItThere = false;

        for (int i = 0; i < pC.Length; i++)
        {
            PlayerInterface player = playersInterface[i].GetComponent<PlayerInterface>();
            if (player.AttackCooldown.fillAmount <= 0.0001f && pC[i] != null && !pC[i].IsDead && !pC[i].isStunned)
            {
                isItThere = true;
                if (playersInterface[GetSelectedPlayer()].GetComponent<PlayerInterface>().AttackCooldown.fillAmount > 0.0001f) SetPlayerSelected(i);
                break;
            }
        }

        return isItThere;
    }

    public void RemovePlayer() => pC[Array.IndexOf(playerSelected, true)] = null; // removes the player

    public BattleCharacter GetPlayerCharacter(int index) => pC[index]; // gets the player character at the index

    public int GetPlayerIndex(BattleCharacter character) => Array.IndexOf(pC, character); // gets the index of the player

    public int GetSelectedPlayer() => Array.IndexOf(playerSelected, true);

    public void SetPlayerCharacter(int index) // sets the player character to the selected player slot
    {
        int selectedPlayer = Array.IndexOf(playerSelected, true);

        int selectedParty = CharactersForParty.GetComponent<CharatersForPartyUIHandler>().selectedParty;

        partyCharacters[selectedParty].activeCharacters[selectedPlayer] = partyCharacters[selectedParty].stockCharacters[index];

        SetSelectedParty(selectedParty);

        playersInterface[selectedPlayer].GetComponent<PlayerInterface>().didCrit = true;
        
        playerTurn = false;
        StartCoroutine(grid.SwitchTurns());
    }

    public bool GetPlayerSelected(int index) => playerSelected[index]; // checks if the player is selected

    public void SetPlayerSelected(int index) // sets the player selected
    {
        playerSelected = new bool[playerSelected.Length];

        playerSelected[index] = true;
    }


    //**************************//
    //**************************//
    //****** Enemy System ******//
    //**************************//
    //**************************//

    public float CalculateAverageEnemyLevel()
    {
        float totalEnemies = eC.Count(ec => ec != null);
        float totalLevels = eC.Where(ec => ec != null).Sum(ec => ec.level);

        return totalLevels / totalEnemies;
    }

    public bool IsThereAnyEnemyNotInCooldown() // checks if there is any enemy not in cooldown
    {
        for (int i = 0; i < eC.Length; i++) // loops through the enemies
        {
            EnemyInterface enemy = enemiesInterface[i].GetComponent<EnemyInterface>(); // gets the enemy
            if (enemy.AttackCooldown.fillAmount <= 0.0001f && eC[i] != null && !eC[i].IsDead) // checks if that enemy isn't in cooldown, is alive and isn't null
            {
                // changes the selected enemy to the one that isn't in cooldown if the current selected enemy is in cooldown
                if (GetSelectedEnemy() != -1 && enemiesInterface[GetSelectedEnemy()].GetComponent<EnemyInterface>().AttackCooldown.fillAmount > 0.0001f) SetEnemySelected(i);
                return true;
            }
        }

        return false;
    }

    private IEnumerator GetAllEnemies()
    {
        while (!stopGetAllEnemies)
        {
            List<BattleCharacter> tempEnemies = new();
            for (int i = 0; i < characters.Count; i++)
            {
                if (characters[i].isEnemy) tempEnemies.Add(characters[i]);

                if (i % 10 == 0) yield return new WaitForSeconds(0);
            }
            yield return null;
            enemies = tempEnemies;
        }
    }

    public void StopGetAllEnemies()
    {
        stopGetAllEnemies = true;
    }

    public int GetNumberOfEnemies() => Mathf.Min(4, ChangeOnlyNulls.GetComponent<Toggle>().isOn ? eC.Count(ec => ec != null && !ec.IsDead) : characters.Count(c => c != null && c.isEnemy && !c.IsDead));

    public BattleCharacter GetEnemyCharacter(int index) => eC[index]; // gets the enemy character at the index

    public int GetSelectedEnemy() => Array.IndexOf(enemySelected, true); // gets the selected enemy

    public IEnumerator SetEnemyCharacter(BattleCharacter character, bool changeOnlyNulls = true) // sets the enemy character
    {
        if (Array.IndexOf(eC, eC.FirstOrDefault(ec => ec != null && ec.IsDead)) != -1 && !changeOnlyNulls) while (!grid.playerTurn || enemyTurns != playerTurns) yield return null;

        int index = Array.IndexOf(eC, eC.FirstOrDefault(ec => ec == null || (ec.IsDead && !changeOnlyNulls)));
        if (index != -1 && Array.IndexOf(eC, character) == -1)
        {
            if (eC[index] != null && eC[index].IsDead && !changeOnlyNulls) enemiesInterface[index].GetComponent<EnemyAnimation>().respawnOffset += new Vector3(0, 2, 0);
            eC[index] = character;
        }
    }

    public bool GetEnemySelected(int index) => enemySelected[index]; // checks if the enemy is selected

    public void SetEnemySelected(int index) // sets the enemy selected
    {
        bool curentSelecedState = enemySelected[index];

        enemySelected = new bool[enemySelected.Length];

        enemySelected[index] = !curentSelecedState; // sets the enemy selected
    }


    //*************************//
    //*************************//
    //****** Save System ******//
    //*************************//
    //*************************//

    // Save the values if the reset button is pressed
    public void SaveParties() => LoadedValues.SaveParties(partyCharacters);

    public void SaveCharacters()
    {
        LoadedValues.SaveCharacters(characters);

        // Use incremental saving if chunked storage is enabled
        if (JsonSaveHelper.IsUsingChunkedStorage())
        {
            CharacterChangeTracker.Instance.SaveDirtyCharacters(characters);
        }
    }

    /// <summary>
    /// Marks a character as modified for incremental saving
    /// Call this whenever a character's data is changed outside of SetCharacter/AddCharacter/RemoveCharacter
    /// </summary>
    public void MarkCharacterModified(BattleCharacter character)
    {
        if (character != null && !string.IsNullOrEmpty(character.id))
        {
            CharacterChangeTracker.Instance.MarkCharacterDirty(character.id);
        }
    }

    /// <summary>
    /// Marks a character as modified by index for incremental saving
    /// </summary>
    public void MarkCharacterModified(int characterIndex)
    {
        if (characterIndex >= 0 && characterIndex < characters.Count)
        {
            CharacterChangeTracker.Instance.MarkCharacterDirtyByIndex(characterIndex, characters);
        }
    }

    public void SaveValues() => LoadedValues.SaveValues(B, C, D, E, F, G, Difficulty, StockWeight, MaxPP, ReductionPerCombo, FractionOfAttacksPerAction, ModFraction, IDBOffset);

    public void SaveBuffs() => LoadedValues.SaveBuffs(buffs);

    public void SaveEnergyTimer() => LoadedValues.SaveEnergyTimer(energyTimer);


    //***************************//
    //***************************//
    //****** Other Systems ******//
    //***************************//
    //***************************//

    // resets the energy amount
    public void SetEnergyTime(int amount = 0)
    {
        if (amount <= 0) amount = energyTimer;
        energyLeft = amount;
    }

    public bool IsEnergyLeft() => energyLeft > 0; // checks if there is energy left

    public bool IsPPLeft() => ppLeft > 0; // checks if there is power left

    // Counts down the energy
    public IEnumerator CountDown()
    {
        if (canCountdown) yield break;
        canCountdown = true;
        SetEnergyTime();
        while (energyLeft > 0 && canCountdown) // while there is energy, reduces it with deltaTime
        {
            energyLeft -= Time.deltaTime;
            yield return null;
        }

        if (energyLeft < 0)
        {
            playerTurn = false;
            StartCoroutine(grid.SwitchTurns());
        }
    }

    public int ChanceOfEscape() // calculates the chance of escape
    {
        List<BattleCharacter> party = GetPartyCharacters(partySelected).activeCharacters.Concat(GetPartyCharacters(partySelected).stockCharacters).ToList(); // gets the party

        int numOfPlayers = party.Count(pc => pc != null && !pc.IsDead), numOfEnemies = eC.Count(ec => ec != null && !ec.IsDead); // gets the number of players and enemies

        float spdParty = 0, lukParty = 0, spdEnemies = 0, lukEnemies = 0, partyMed = 0, enemiesMed = 0; // creates the variables 

        foreach (var player in party) if (player != null && !player.IsDead) { spdParty += player.mods.GetSpeed(); lukParty += player.mods.GetLuck(); } // gets the total speed and luck of the players
        foreach (var enemy in eC) if (enemy != null && !enemy.IsDead) { spdEnemies += enemy.mods.GetSpeed(); lukEnemies += enemy.mods.GetLuck(); } // same thing but for the enemies

        if (numOfPlayers > 0) partyMed = spdParty / (float)numOfPlayers * (lukParty / (float)numOfPlayers); // calculates the median of the party
        if (numOfEnemies > 0) enemiesMed = spdEnemies / (float)numOfEnemies * (lukEnemies / (float)numOfEnemies); // same thing but for the enemies

        int chance = (int)(partyMed / (partyMed + enemiesMed) * 100); // calculates the chance

        // makes sure the chance is between 0 and 100
        if (chance > 100) return 100;
        if (chance < 0) return 0;

        return chance; // otherwise returns the chance
    }

    public void TryToEscape() // tries to escape
    {
        // gets a random number between 0 and 100 and checks if it is less than the chance
        if (UnityEngine.Random.Range(0, 100) < ChanceOfEscape()) new RestartGame(); // if it is, it restarts the game
        else // otherwise shows the escape fail popup and switches the turns
        {
            EscapeFailPopup.SetActive(true);
            playerActions = 1;

            playerTurn = false;
            StartCoroutine(grid.SwitchTurns());
        }
    }

    public void UpdateComboChance(int index, int value) // updates the combo chance at the index
    {
        ComboChance[index] = value;
        LoadedValues.SaveComboChance(ComboChance);
    }

    public void UpdateGoldenStrike(int index, int value) // updates the combo chance at the index
    {
        GoldenStrike[index] = value;
        LoadedValues.SaveGoldenStrike(GoldenStrike);
    }

    public IEnumerator ScreenShake(float strength, float duration) // screen shakes
    {
        duration /= 60f; // makes the duration in seconds, using 60 fps as a reference
        float timer = 0;
        while (timer < duration)
        {
            timer += Time.deltaTime;
            Vector3 shakePos = new(UnityEngine.Random.Range(-strength, strength), UnityEngine.Random.Range(-strength, strength), 0); // calculates the shake offset with a random range of -strength to strength

            GameObject.Find("BackGround").transform.localPosition = new Vector3(0f, 0f, 400f) + shakePos * 2f; // moves the background

            for (int i = 0; i < eC.Length; i++) enemiesInterface[i].GetComponent<EnemyAnimation>().offset = shakePos / 20f; // moves the enemies
            yield return null;
        }
        GameObject.Find("BackGround").transform.localPosition = new Vector3(0f, 0f, 400f); // resets the background position
        for (int i = 0; i < eC.Length; i++) enemiesInterface[i].GetComponent<EnemyAnimation>().offset = new(); // resets the enemy offset
    }
}
