{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 14916, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 14916, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 14916, "tid": 2627, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 14916, "tid": 2627, "ts": 1751367669503303, "dur": 822, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 14916, "tid": 2627, "ts": 1751367669507022, "dur": 1306, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 14916, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 14916, "tid": 1, "ts": 1751367667886634, "dur": 9506, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 14916, "tid": 1, "ts": 1751367667896147, "dur": 182325, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 14916, "tid": 1, "ts": 1751367668078488, "dur": 176473, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 14916, "tid": 2627, "ts": 1751367669508352, "dur": 91, "ph": "X", "name": "", "args": {}}, {"pid": 14916, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667883232, "dur": 16037, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667899273, "dur": 1591215, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667900311, "dur": 2578, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667902896, "dur": 1908, "ph": "X", "name": "ProcessMessages 20487", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667904821, "dur": 949, "ph": "X", "name": "ReadAsync 20487", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667905776, "dur": 33, "ph": "X", "name": "ProcessMessages 20547", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667905812, "dur": 253, "ph": "X", "name": "ReadAsync 20547", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667906069, "dur": 18, "ph": "X", "name": "ProcessMessages 15608", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667906088, "dur": 76, "ph": "X", "name": "ReadAsync 15608", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667906167, "dur": 4, "ph": "X", "name": "ProcessMessages 2702", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667906215, "dur": 80, "ph": "X", "name": "ReadAsync 2702", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667906301, "dur": 3, "ph": "X", "name": "ProcessMessages 818", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667906306, "dur": 101, "ph": "X", "name": "ReadAsync 818", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667906411, "dur": 3, "ph": "X", "name": "ProcessMessages 1757", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667906416, "dur": 64, "ph": "X", "name": "ReadAsync 1757", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667906483, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667906486, "dur": 46, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667906534, "dur": 2, "ph": "X", "name": "ProcessMessages 1405", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667906613, "dur": 147, "ph": "X", "name": "ReadAsync 1405", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667906766, "dur": 6, "ph": "X", "name": "ProcessMessages 3952", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667906774, "dur": 352, "ph": "X", "name": "ReadAsync 3952", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667907129, "dur": 2, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667907133, "dur": 89, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667907224, "dur": 8, "ph": "X", "name": "ProcessMessages 8051", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667907233, "dur": 620, "ph": "X", "name": "ReadAsync 8051", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667907857, "dur": 2, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667907861, "dur": 180, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908045, "dur": 12, "ph": "X", "name": "ProcessMessages 12068", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908059, "dur": 68, "ph": "X", "name": "ReadAsync 12068", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908130, "dur": 3, "ph": "X", "name": "ProcessMessages 1394", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908134, "dur": 17, "ph": "X", "name": "ReadAsync 1394", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908152, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908155, "dur": 36, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908197, "dur": 3, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908202, "dur": 79, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908287, "dur": 3, "ph": "X", "name": "ProcessMessages 693", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908292, "dur": 38, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908332, "dur": 2, "ph": "X", "name": "ProcessMessages 1947", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908336, "dur": 14, "ph": "X", "name": "ReadAsync 1947", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908352, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908357, "dur": 69, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908429, "dur": 2, "ph": "X", "name": "ProcessMessages 1263", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908433, "dur": 19, "ph": "X", "name": "ReadAsync 1263", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908466, "dur": 2, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908478, "dur": 64, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908544, "dur": 3, "ph": "X", "name": "ProcessMessages 1771", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908548, "dur": 15, "ph": "X", "name": "ReadAsync 1771", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908565, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908573, "dur": 14, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908589, "dur": 1, "ph": "X", "name": "ProcessMessages 217", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908592, "dur": 19, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908613, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908615, "dur": 20, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908637, "dur": 1, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908639, "dur": 17, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908659, "dur": 1, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908662, "dur": 19, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908694, "dur": 1, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908697, "dur": 17, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908716, "dur": 1, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908718, "dur": 17, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908738, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908740, "dur": 18, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908760, "dur": 1, "ph": "X", "name": "ProcessMessages 811", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908762, "dur": 14, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908795, "dur": 1, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908797, "dur": 55, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908855, "dur": 2, "ph": "X", "name": "ProcessMessages 1681", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908860, "dur": 18, "ph": "X", "name": "ReadAsync 1681", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908880, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908883, "dur": 19, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908904, "dur": 1, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908907, "dur": 24, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908933, "dur": 1, "ph": "X", "name": "ProcessMessages 667", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908936, "dur": 16, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908954, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908957, "dur": 18, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908977, "dur": 1, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667908979, "dur": 20, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909002, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909004, "dur": 59, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909065, "dur": 2, "ph": "X", "name": "ProcessMessages 1162", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909069, "dur": 48, "ph": "X", "name": "ReadAsync 1162", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909136, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909139, "dur": 37, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909182, "dur": 4, "ph": "X", "name": "ProcessMessages 1826", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909189, "dur": 54, "ph": "X", "name": "ReadAsync 1826", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909245, "dur": 2, "ph": "X", "name": "ProcessMessages 1386", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909248, "dur": 41, "ph": "X", "name": "ReadAsync 1386", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909293, "dur": 1, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909296, "dur": 27, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909327, "dur": 2, "ph": "X", "name": "ProcessMessages 909", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909331, "dur": 29, "ph": "X", "name": "ReadAsync 909", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909362, "dur": 2, "ph": "X", "name": "ProcessMessages 770", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909365, "dur": 19, "ph": "X", "name": "ReadAsync 770", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909386, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909389, "dur": 100, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909492, "dur": 3, "ph": "X", "name": "ProcessMessages 2392", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909496, "dur": 43, "ph": "X", "name": "ReadAsync 2392", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909546, "dur": 3, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909551, "dur": 32, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909585, "dur": 2, "ph": "X", "name": "ProcessMessages 1250", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909588, "dur": 90, "ph": "X", "name": "ReadAsync 1250", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909682, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909685, "dur": 55, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909743, "dur": 7, "ph": "X", "name": "ProcessMessages 2272", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909752, "dur": 38, "ph": "X", "name": "ReadAsync 2272", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909793, "dur": 2, "ph": "X", "name": "ProcessMessages 1405", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909797, "dur": 44, "ph": "X", "name": "ReadAsync 1405", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909846, "dur": 2, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909849, "dur": 30, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909882, "dur": 2, "ph": "X", "name": "ProcessMessages 972", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909886, "dur": 20, "ph": "X", "name": "ReadAsync 972", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909908, "dur": 1, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909910, "dur": 19, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909937, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909939, "dur": 16, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909958, "dur": 1, "ph": "X", "name": "ProcessMessages 214", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909960, "dur": 20, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909983, "dur": 1, "ph": "X", "name": "ProcessMessages 663", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667909985, "dur": 21, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667910027, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667910030, "dur": 18, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667910050, "dur": 1, "ph": "X", "name": "ProcessMessages 933", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667910053, "dur": 15, "ph": "X", "name": "ReadAsync 933", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667910070, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667910072, "dur": 26, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667910100, "dur": 1, "ph": "X", "name": "ProcessMessages 693", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667910103, "dur": 17, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667910122, "dur": 1, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667910124, "dur": 15, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667910313, "dur": 2, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667910317, "dur": 29, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667910349, "dur": 1, "ph": "X", "name": "ProcessMessages 930", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667910493, "dur": 84, "ph": "X", "name": "ReadAsync 930", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667910579, "dur": 345, "ph": "X", "name": "ProcessMessages 8086", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667910928, "dur": 134, "ph": "X", "name": "ReadAsync 8086", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911068, "dur": 11, "ph": "X", "name": "ProcessMessages 9518", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911081, "dur": 29, "ph": "X", "name": "ReadAsync 9518", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911111, "dur": 2, "ph": "X", "name": "ProcessMessages 1873", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911115, "dur": 24, "ph": "X", "name": "ReadAsync 1873", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911142, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911145, "dur": 31, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911180, "dur": 3, "ph": "X", "name": "ProcessMessages 736", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911184, "dur": 111, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911299, "dur": 2, "ph": "X", "name": "ProcessMessages 1017", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911309, "dur": 38, "ph": "X", "name": "ReadAsync 1017", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911350, "dur": 4, "ph": "X", "name": "ProcessMessages 2959", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911355, "dur": 18, "ph": "X", "name": "ReadAsync 2959", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911375, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911378, "dur": 14, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911406, "dur": 62, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911471, "dur": 2, "ph": "X", "name": "ProcessMessages 944", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911474, "dur": 23, "ph": "X", "name": "ReadAsync 944", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911500, "dur": 2, "ph": "X", "name": "ProcessMessages 1088", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911503, "dur": 24, "ph": "X", "name": "ReadAsync 1088", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911529, "dur": 1, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911532, "dur": 27, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911562, "dur": 1, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911564, "dur": 24, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911590, "dur": 1, "ph": "X", "name": "ProcessMessages 695", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911592, "dur": 45, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911639, "dur": 2, "ph": "X", "name": "ProcessMessages 785", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911643, "dur": 18, "ph": "X", "name": "ReadAsync 785", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911663, "dur": 1, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911668, "dur": 18, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911688, "dur": 1, "ph": "X", "name": "ProcessMessages 522", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911691, "dur": 17, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911710, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911741, "dur": 25, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911768, "dur": 2, "ph": "X", "name": "ProcessMessages 1287", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911771, "dur": 19, "ph": "X", "name": "ReadAsync 1287", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911792, "dur": 1, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911794, "dur": 18, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911815, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911818, "dur": 25, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911845, "dur": 1, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911848, "dur": 20, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911870, "dur": 1, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911872, "dur": 17, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911892, "dur": 1, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911894, "dur": 18, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911914, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911916, "dur": 16, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911935, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911937, "dur": 16, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911955, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911957, "dur": 19, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911977, "dur": 1, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667911982, "dur": 17, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912003, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912005, "dur": 13, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912020, "dur": 1, "ph": "X", "name": "ProcessMessages 70", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912023, "dur": 48, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912074, "dur": 2, "ph": "X", "name": "ProcessMessages 1275", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912077, "dur": 22, "ph": "X", "name": "ReadAsync 1275", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912101, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912104, "dur": 16, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912122, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912124, "dur": 22, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912148, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912151, "dur": 18, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912171, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912174, "dur": 63, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912239, "dur": 2, "ph": "X", "name": "ProcessMessages 1427", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912243, "dur": 16, "ph": "X", "name": "ReadAsync 1427", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912261, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912263, "dur": 20, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912287, "dur": 2, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912291, "dur": 73, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912369, "dur": 2, "ph": "X", "name": "ProcessMessages 725", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912372, "dur": 30, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912404, "dur": 3, "ph": "X", "name": "ProcessMessages 1579", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912409, "dur": 48, "ph": "X", "name": "ReadAsync 1579", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912460, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912462, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912488, "dur": 1, "ph": "X", "name": "ProcessMessages 662", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912497, "dur": 17, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912529, "dur": 1, "ph": "X", "name": "ProcessMessages 171", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912532, "dur": 18, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912552, "dur": 1, "ph": "X", "name": "ProcessMessages 685", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912555, "dur": 19, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912576, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912578, "dur": 47, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912628, "dur": 2, "ph": "X", "name": "ProcessMessages 983", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912652, "dur": 21, "ph": "X", "name": "ReadAsync 983", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912676, "dur": 1, "ph": "X", "name": "ProcessMessages 1349", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912678, "dur": 51, "ph": "X", "name": "ReadAsync 1349", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912734, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912757, "dur": 2, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912761, "dur": 19, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912782, "dur": 1, "ph": "X", "name": "ProcessMessages 662", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912785, "dur": 22, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912811, "dur": 140, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912955, "dur": 2, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667912959, "dur": 45, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913008, "dur": 4, "ph": "X", "name": "ProcessMessages 3836", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913014, "dur": 24, "ph": "X", "name": "ReadAsync 3836", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913041, "dur": 1, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913043, "dur": 22, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913070, "dur": 2, "ph": "X", "name": "ProcessMessages 193", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913073, "dur": 14, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913091, "dur": 17, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913111, "dur": 1, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913113, "dur": 18, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913133, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913136, "dur": 22, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913161, "dur": 2, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913164, "dur": 54, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913220, "dur": 2, "ph": "X", "name": "ProcessMessages 957", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913223, "dur": 210, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913435, "dur": 6, "ph": "X", "name": "ProcessMessages 5112", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913442, "dur": 20, "ph": "X", "name": "ReadAsync 5112", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913465, "dur": 1, "ph": "X", "name": "ProcessMessages 693", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913468, "dur": 49, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913520, "dur": 2, "ph": "X", "name": "ProcessMessages 1035", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913523, "dur": 16, "ph": "X", "name": "ReadAsync 1035", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913541, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913544, "dur": 17, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913563, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913566, "dur": 22, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913590, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913593, "dur": 25, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913620, "dur": 1, "ph": "X", "name": "ProcessMessages 658", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913625, "dur": 26, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913653, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913655, "dur": 31, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913700, "dur": 2, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913705, "dur": 22, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913730, "dur": 2, "ph": "X", "name": "ProcessMessages 861", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913736, "dur": 20, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913759, "dur": 1, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913762, "dur": 22, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913786, "dur": 2, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913789, "dur": 20, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913811, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913814, "dur": 43, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913859, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913862, "dur": 53, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913918, "dur": 2, "ph": "X", "name": "ProcessMessages 1696", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913922, "dur": 19, "ph": "X", "name": "ReadAsync 1696", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913944, "dur": 1, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913946, "dur": 25, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913975, "dur": 3, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913979, "dur": 18, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667913999, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914002, "dur": 18, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914022, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914024, "dur": 15, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914041, "dur": 1, "ph": "X", "name": "ProcessMessages 82", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914043, "dur": 22, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914067, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914070, "dur": 22, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914096, "dur": 13, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914111, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914113, "dur": 21, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914138, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914158, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914161, "dur": 16, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914179, "dur": 1, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914181, "dur": 41, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914226, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914243, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914245, "dur": 35, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914284, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914304, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914306, "dur": 15, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914325, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914327, "dur": 18, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914347, "dur": 1, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914349, "dur": 16, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914367, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914370, "dur": 18, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914394, "dur": 1, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914396, "dur": 14, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914413, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914415, "dur": 15, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914432, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914434, "dur": 45, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914483, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914504, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914506, "dur": 16, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914525, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914527, "dur": 16, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914545, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914547, "dur": 61, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914611, "dur": 2, "ph": "X", "name": "ProcessMessages 867", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914614, "dur": 22, "ph": "X", "name": "ReadAsync 867", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914640, "dur": 2, "ph": "X", "name": "ProcessMessages 758", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914643, "dur": 20, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914666, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914669, "dur": 34, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914722, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914724, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914749, "dur": 2, "ph": "X", "name": "ProcessMessages 1087", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914752, "dur": 13, "ph": "X", "name": "ReadAsync 1087", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914767, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914769, "dur": 48, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914821, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914865, "dur": 2, "ph": "X", "name": "ProcessMessages 1072", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914869, "dur": 41, "ph": "X", "name": "ReadAsync 1072", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914914, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914916, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914936, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914938, "dur": 15, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914957, "dur": 17, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914977, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667914979, "dur": 50, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915045, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915081, "dur": 2, "ph": "X", "name": "ProcessMessages 1069", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915085, "dur": 40, "ph": "X", "name": "ReadAsync 1069", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915129, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915151, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915154, "dur": 23, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915180, "dur": 1, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915182, "dur": 14, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915199, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915202, "dur": 52, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915276, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915305, "dur": 2, "ph": "X", "name": "ProcessMessages 996", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915308, "dur": 23, "ph": "X", "name": "ReadAsync 996", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915336, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915359, "dur": 2, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915362, "dur": 16, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915382, "dur": 59, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915444, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915447, "dur": 18, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915485, "dur": 2, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915488, "dur": 21, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915511, "dur": 1, "ph": "X", "name": "ProcessMessages 905", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915514, "dur": 37, "ph": "X", "name": "ReadAsync 905", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915557, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915577, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915579, "dur": 21, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915604, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915607, "dur": 15, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915624, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915626, "dur": 38, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915667, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915688, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915691, "dur": 19, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915712, "dur": 1, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915715, "dur": 46, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915765, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915785, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915787, "dur": 20, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915816, "dur": 1, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915818, "dur": 22, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915842, "dur": 1, "ph": "X", "name": "ProcessMessages 58", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915845, "dur": 26, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915875, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915898, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915901, "dur": 23, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915926, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915928, "dur": 27, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915958, "dur": 1, "ph": "X", "name": "ProcessMessages 58", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667915960, "dur": 32, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916010, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916012, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916040, "dur": 2, "ph": "X", "name": "ProcessMessages 1082", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916043, "dur": 13, "ph": "X", "name": "ReadAsync 1082", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916058, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916061, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916106, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916126, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916129, "dur": 36, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916168, "dur": 1, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916170, "dur": 27, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916201, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916222, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916225, "dur": 20, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916247, "dur": 1, "ph": "X", "name": "ProcessMessages 631", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916249, "dur": 14, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916265, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916267, "dur": 42, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916312, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916333, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916335, "dur": 50, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916387, "dur": 1, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916390, "dur": 22, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916416, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916438, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916441, "dur": 27, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916470, "dur": 1, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916482, "dur": 34, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916520, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916569, "dur": 1, "ph": "X", "name": "ProcessMessages 734", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916571, "dur": 57, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916630, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916633, "dur": 22, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916657, "dur": 1, "ph": "X", "name": "ProcessMessages 791", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916660, "dur": 16, "ph": "X", "name": "ReadAsync 791", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916678, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916681, "dur": 46, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916734, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916799, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916802, "dur": 19, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916823, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916826, "dur": 22, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916851, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916863, "dur": 22, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916906, "dur": 2, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916910, "dur": 21, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916935, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916937, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916960, "dur": 1, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916963, "dur": 31, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916996, "dur": 1, "ph": "X", "name": "ProcessMessages 880", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667916999, "dur": 17, "ph": "X", "name": "ReadAsync 880", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917018, "dur": 1, "ph": "X", "name": "ProcessMessages 232", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917021, "dur": 27, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917050, "dur": 1, "ph": "X", "name": "ProcessMessages 698", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917053, "dur": 15, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917070, "dur": 1, "ph": "X", "name": "ProcessMessages 59", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917072, "dur": 17, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917091, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917093, "dur": 39, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917137, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917158, "dur": 1, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917160, "dur": 18, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917180, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917183, "dur": 46, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917233, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917253, "dur": 1, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917256, "dur": 18, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917276, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917278, "dur": 16, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917296, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917298, "dur": 18, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917319, "dur": 1, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917321, "dur": 56, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917379, "dur": 2, "ph": "X", "name": "ProcessMessages 841", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917383, "dur": 45, "ph": "X", "name": "ReadAsync 841", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917433, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917461, "dur": 2, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917465, "dur": 20, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917488, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917491, "dur": 44, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917538, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917562, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917565, "dur": 46, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917614, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917617, "dur": 26, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917647, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917669, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917672, "dur": 24, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917698, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917701, "dur": 22, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917726, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917728, "dur": 32, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917763, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917767, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917791, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917794, "dur": 17, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917813, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917815, "dur": 17, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917835, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667917837, "dur": 188, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667918029, "dur": 1, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667918032, "dur": 108, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667918143, "dur": 5, "ph": "X", "name": "ProcessMessages 3848", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667918149, "dur": 33, "ph": "X", "name": "ReadAsync 3848", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667918194, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667918196, "dur": 28, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667918229, "dur": 2, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667918233, "dur": 24, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667918260, "dur": 1, "ph": "X", "name": "ProcessMessages 1120", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667918262, "dur": 17, "ph": "X", "name": "ReadAsync 1120", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667918281, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667918284, "dur": 17, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667918303, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667918306, "dur": 18, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667918328, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667918331, "dur": 17, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667918350, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667918352, "dur": 39, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667918395, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667918435, "dur": 2, "ph": "X", "name": "ProcessMessages 859", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667918438, "dur": 16, "ph": "X", "name": "ReadAsync 859", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667918456, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667918458, "dur": 46, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667918509, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667918531, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667918534, "dur": 22, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667918558, "dur": 1, "ph": "X", "name": "ProcessMessages 761", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667918561, "dur": 192, "ph": "X", "name": "ReadAsync 761", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667918755, "dur": 4, "ph": "X", "name": "ProcessMessages 2955", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667918760, "dur": 18, "ph": "X", "name": "ReadAsync 2955", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667918784, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667918808, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667918811, "dur": 20, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667918833, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667918835, "dur": 45, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667918885, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667918905, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667918909, "dur": 18, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667918930, "dur": 1, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919060, "dur": 43, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919105, "dur": 4, "ph": "X", "name": "ProcessMessages 2918", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919111, "dur": 38, "ph": "X", "name": "ReadAsync 2918", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919204, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919207, "dur": 24, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919233, "dur": 2, "ph": "X", "name": "ProcessMessages 1132", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919236, "dur": 25, "ph": "X", "name": "ReadAsync 1132", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919263, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919266, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919293, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919296, "dur": 20, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919319, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919322, "dur": 40, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919366, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919387, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919390, "dur": 22, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919413, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919416, "dur": 15, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919434, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919437, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919477, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919528, "dur": 2, "ph": "X", "name": "ProcessMessages 1110", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919531, "dur": 32, "ph": "X", "name": "ReadAsync 1110", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919568, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919589, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919592, "dur": 19, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919613, "dur": 1, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919615, "dur": 22, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919640, "dur": 1, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919643, "dur": 18, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919663, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919666, "dur": 19, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919687, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919689, "dur": 14, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919706, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919708, "dur": 16, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919726, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919729, "dur": 45, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919777, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919795, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919797, "dur": 19, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919818, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919821, "dur": 18, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919842, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919845, "dur": 36, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919885, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919914, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919917, "dur": 19, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919939, "dur": 1, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919942, "dur": 39, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667919985, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920020, "dur": 1, "ph": "X", "name": "ProcessMessages 830", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920023, "dur": 19, "ph": "X", "name": "ReadAsync 830", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920045, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920048, "dur": 17, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920068, "dur": 1, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920070, "dur": 42, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920116, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920138, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920141, "dur": 43, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920187, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920190, "dur": 51, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920245, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920265, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920285, "dur": 20, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920307, "dur": 2, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920310, "dur": 32, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920346, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920370, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920372, "dur": 19, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920394, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920397, "dur": 41, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920442, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920464, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920467, "dur": 28, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920497, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920500, "dur": 34, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920538, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920561, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920564, "dur": 20, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920586, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920589, "dur": 19, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920610, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920613, "dur": 20, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920635, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920638, "dur": 37, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920677, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920679, "dur": 16, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920698, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920700, "dur": 42, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920755, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920780, "dur": 1, "ph": "X", "name": "ProcessMessages 841", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920783, "dur": 16, "ph": "X", "name": "ReadAsync 841", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920801, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920803, "dur": 69, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920875, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920877, "dur": 16, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920896, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920899, "dur": 16, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920917, "dur": 1, "ph": "X", "name": "ProcessMessages 234", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920919, "dur": 41, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667920965, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921038, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921041, "dur": 20, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921063, "dur": 1, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921068, "dur": 21, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921091, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921094, "dur": 20, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921116, "dur": 1, "ph": "X", "name": "ProcessMessages 642", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921119, "dur": 10, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921132, "dur": 42, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921178, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921233, "dur": 2, "ph": "X", "name": "ProcessMessages 1258", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921238, "dur": 28, "ph": "X", "name": "ReadAsync 1258", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921270, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921272, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921292, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921295, "dur": 19, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921316, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921336, "dur": 15, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921353, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921356, "dur": 19, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921379, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921431, "dur": 2, "ph": "X", "name": "ProcessMessages 1135", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921435, "dur": 34, "ph": "X", "name": "ReadAsync 1135", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921474, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921498, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921501, "dur": 19, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921523, "dur": 1, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921525, "dur": 14, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921541, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921544, "dur": 36, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921583, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921604, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921606, "dur": 20, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921628, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921631, "dur": 62, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921695, "dur": 1, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921697, "dur": 17, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921716, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921719, "dur": 18, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921740, "dur": 1, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921743, "dur": 44, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921791, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921811, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921815, "dur": 18, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921835, "dur": 1, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921838, "dur": 42, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921884, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921916, "dur": 1, "ph": "X", "name": "ProcessMessages 964", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921919, "dur": 15, "ph": "X", "name": "ReadAsync 964", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921936, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921938, "dur": 48, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921989, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667921991, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922068, "dur": 2, "ph": "X", "name": "ProcessMessages 1570", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922072, "dur": 24, "ph": "X", "name": "ReadAsync 1570", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922098, "dur": 2, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922102, "dur": 19, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922124, "dur": 1, "ph": "X", "name": "ProcessMessages 209", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922126, "dur": 16, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922145, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922147, "dur": 36, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922187, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922211, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922213, "dur": 20, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922236, "dur": 1, "ph": "X", "name": "ProcessMessages 611", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922239, "dur": 48, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922291, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922315, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922318, "dur": 19, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922338, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922341, "dur": 44, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922388, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922391, "dur": 22, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922416, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922418, "dur": 21, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922441, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922444, "dur": 16, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922463, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922466, "dur": 36, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922506, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922528, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922531, "dur": 19, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922552, "dur": 1, "ph": "X", "name": "ProcessMessages 642", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922555, "dur": 45, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922604, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922628, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922630, "dur": 19, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922651, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922654, "dur": 18, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922674, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922677, "dur": 20, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922700, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922702, "dur": 18, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922725, "dur": 1, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922728, "dur": 15, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922745, "dur": 1, "ph": "X", "name": "ProcessMessages 74", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922749, "dur": 18, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922769, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922771, "dur": 41, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922816, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922864, "dur": 2, "ph": "X", "name": "ProcessMessages 1063", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922867, "dur": 36, "ph": "X", "name": "ReadAsync 1063", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922907, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922928, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922931, "dur": 22, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922955, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667922957, "dur": 42, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923002, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923024, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923027, "dur": 19, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923048, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923051, "dur": 57, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923110, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923113, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923147, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923150, "dur": 19, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923172, "dur": 2, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923175, "dur": 42, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923234, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923237, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923261, "dur": 2, "ph": "X", "name": "ProcessMessages 1075", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923264, "dur": 12, "ph": "X", "name": "ReadAsync 1075", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923281, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923327, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923351, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923353, "dur": 19, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923375, "dur": 1, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923377, "dur": 50, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923430, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923433, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923452, "dur": 18, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923472, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923475, "dur": 22, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923502, "dur": 1, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923505, "dur": 42, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923551, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923573, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923576, "dur": 22, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923600, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923603, "dur": 14, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923619, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923621, "dur": 45, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923670, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923726, "dur": 2, "ph": "X", "name": "ProcessMessages 1382", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923730, "dur": 21, "ph": "X", "name": "ReadAsync 1382", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923753, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923755, "dur": 20, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923778, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923781, "dur": 16, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923799, "dur": 1, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923802, "dur": 15, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923819, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923821, "dur": 45, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923870, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923892, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923894, "dur": 19, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923916, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923918, "dur": 15, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923936, "dur": 1, "ph": "X", "name": "ProcessMessages 58", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923938, "dur": 42, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923984, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667923986, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924007, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924010, "dur": 19, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924032, "dur": 1, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924035, "dur": 20, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924058, "dur": 1, "ph": "X", "name": "ProcessMessages 667", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924060, "dur": 19, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924082, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924085, "dur": 21, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924108, "dur": 1, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924111, "dur": 38, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924151, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924153, "dur": 25, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924184, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924212, "dur": 2, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924218, "dur": 21, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924241, "dur": 1, "ph": "X", "name": "ProcessMessages 838", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924243, "dur": 18, "ph": "X", "name": "ReadAsync 838", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924264, "dur": 1, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924266, "dur": 17, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924285, "dur": 1, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924288, "dur": 22, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924312, "dur": 1, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924315, "dur": 16, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924334, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924336, "dur": 61, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924400, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924402, "dur": 16, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924420, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924422, "dur": 19, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924444, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924446, "dur": 22, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924471, "dur": 1, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924474, "dur": 22, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924498, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924501, "dur": 19, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924522, "dur": 1, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924524, "dur": 19, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924546, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924549, "dur": 46, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924600, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924619, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924622, "dur": 78, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924705, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924709, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667924755, "dur": 734, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667925495, "dur": 58, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667925559, "dur": 16, "ph": "X", "name": "ProcessMessages 848", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667925578, "dur": 36, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667925618, "dur": 3, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667925623, "dur": 33, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667925662, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667925668, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667925697, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667925701, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667925733, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667925738, "dur": 32, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667925776, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667925782, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667925823, "dur": 4, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667925830, "dur": 33, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667925869, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667925876, "dur": 49, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667925930, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667925935, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667925979, "dur": 5, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667925987, "dur": 29, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667926019, "dur": 3, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667926029, "dur": 40, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667926076, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667926083, "dur": 30, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667926116, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667926122, "dur": 30, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667926158, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667926164, "dur": 60, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667926231, "dur": 6, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667926239, "dur": 26, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667926270, "dur": 5, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667926278, "dur": 33, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667926316, "dur": 8, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667926327, "dur": 35, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667926367, "dur": 6, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667926376, "dur": 34, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667926414, "dur": 5, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667926421, "dur": 33, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667926458, "dur": 4, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667926464, "dur": 38, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667926506, "dur": 5, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667926513, "dur": 85, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667926603, "dur": 8, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667926614, "dur": 40, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667926679, "dur": 6, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667926688, "dur": 44, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667926737, "dur": 7, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667926746, "dur": 62, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667926814, "dur": 8, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667926824, "dur": 37, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667926867, "dur": 5, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667926875, "dur": 40, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667926920, "dur": 6, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667926928, "dur": 37, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667926971, "dur": 6, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667926980, "dur": 34, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667927019, "dur": 5, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667927026, "dur": 38, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667927070, "dur": 5, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667927077, "dur": 32, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667927113, "dur": 4, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667927120, "dur": 35, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667927159, "dur": 5, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667927166, "dur": 28, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667927197, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667927201, "dur": 93, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667927299, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667927304, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667927342, "dur": 5, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667927349, "dur": 39, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667927395, "dur": 6, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667927405, "dur": 38, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667927448, "dur": 4, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667927455, "dur": 28, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667927486, "dur": 5, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667927493, "dur": 27, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667927526, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667927533, "dur": 32, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667927569, "dur": 5, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667927577, "dur": 29, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667927611, "dur": 4, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667927617, "dur": 31, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667927654, "dur": 5, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667927662, "dur": 84, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667927750, "dur": 7, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667927760, "dur": 30, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667927796, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667927801, "dur": 29, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667927835, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667927839, "dur": 23530, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667951380, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667951386, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667951420, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667951423, "dur": 7973, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667959407, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667959415, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667959458, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667959462, "dur": 118, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667959587, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667959593, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667959638, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667959644, "dur": 7906, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667967607, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667967614, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667967646, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667967845, "dur": 35, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667967885, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667967896, "dur": 621, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667968525, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667968529, "dur": 80, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667968636, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667968643, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667968684, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667968688, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667968711, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667968715, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667968755, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667968760, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667968792, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667968795, "dur": 396, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667969196, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667969200, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667969237, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667969242, "dur": 168, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667969418, "dur": 24, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667969458, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667969491, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667969496, "dur": 57, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667969581, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667969586, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667969638, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667969645, "dur": 91, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667969745, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667969813, "dur": 85, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667969903, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667969909, "dur": 62, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667969976, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667969989, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667970033, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667970036, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667970055, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667970058, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667970104, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667970108, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667970145, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667970148, "dur": 71, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667970224, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667970229, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667970247, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667970250, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667970287, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667970300, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667970333, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667970337, "dur": 286, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667970628, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667970631, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667970653, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667970658, "dur": 336, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667970999, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667971003, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667971028, "dur": 2, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667971032, "dur": 18, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667971054, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667971058, "dur": 89, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667971169, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667971173, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667971223, "dur": 4, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667971230, "dur": 19, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667971253, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667971257, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667971283, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667971286, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667971307, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667971310, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667971339, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667971342, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667971366, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667971370, "dur": 89, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667971463, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667971466, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667971484, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667971487, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667971516, "dur": 32, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667971552, "dur": 391, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667971947, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667971951, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667971998, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667972004, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667972032, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667972036, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667972062, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667972066, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667972155, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667972160, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667972225, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667972230, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667972252, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667972263, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667972289, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667972294, "dur": 84, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667972383, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667972389, "dur": 17, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667972409, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667972412, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667972445, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667972448, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667972481, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667972485, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667972513, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667972516, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667972547, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667972551, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667972577, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667972581, "dur": 224, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667972809, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667972813, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667972846, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667972850, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667972871, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667972874, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667972898, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667972901, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667972948, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667972953, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667972988, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667972992, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667973010, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667973013, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667973086, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667973089, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667973110, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667973114, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667973193, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667973197, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667973229, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667973233, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667973251, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667973254, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667973315, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667973318, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667973354, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667973360, "dur": 122, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667973487, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667973491, "dur": 121, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667973620, "dur": 5, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667973627, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667973683, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667973689, "dur": 30, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667973725, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667973729, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667973792, "dur": 5, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667973799, "dur": 125, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667973928, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667973933, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667973971, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667973976, "dur": 98, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667974079, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667974083, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667974103, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667974106, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667974132, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667974138, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667974196, "dur": 38, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667974237, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667974317, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667974320, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667974340, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667974344, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667974377, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667974381, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667974407, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667974411, "dur": 371, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667974795, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667974799, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667974854, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667974858, "dur": 154, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667975018, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667975021, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667975044, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667975048, "dur": 19, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667975072, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667975076, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667975121, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667975125, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667975145, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667975148, "dur": 71, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667975223, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667975227, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667975256, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667975260, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667975280, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667975284, "dur": 20, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667975310, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667975315, "dur": 99, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667975417, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667975421, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667975451, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667975456, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667975522, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667975525, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667975598, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667975604, "dur": 108, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667975717, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667975720, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667975800, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667975803, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667975826, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667975829, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667976078, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667976081, "dur": 81, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667976167, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667976173, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667976196, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667976200, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667976234, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667976237, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667976340, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667976344, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667976379, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667976384, "dur": 84, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667976473, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667976477, "dur": 83, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667976567, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667976668, "dur": 71, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667976742, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667976746, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667976778, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667976782, "dur": 14, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667976800, "dur": 65, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667976875, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667976880, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667976983, "dur": 736, "ph": "X", "name": "ProcessMessages 72", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667977726, "dur": 43, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667977773, "dur": 39, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667977814, "dur": 195, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667978020, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667978023, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667978072, "dur": 8, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667978083, "dur": 150, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667978238, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667978241, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667978339, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667978355, "dur": 35, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667978396, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667978401, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667978430, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667978434, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667978459, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667978462, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667978503, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667978507, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667978540, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667978543, "dur": 326, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667978875, "dur": 31, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667978908, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667978959, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667978963, "dur": 26, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667978991, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667978994, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667979049, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667979052, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667979100, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667979104, "dur": 99, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667979209, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667979213, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667979282, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667979286, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667979313, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667979316, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667979336, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667979339, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667979359, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667979362, "dur": 56, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667979429, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667979433, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667979456, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667979459, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667979499, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667979502, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667979589, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667979592, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667979617, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667979620, "dur": 168, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667979791, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667979805, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667979817, "dur": 331, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667980166, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667980190, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667980194, "dur": 123, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667980321, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667980338, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667980340, "dur": 69, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667980416, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667980436, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667980438, "dur": 254, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667980699, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667980702, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667980733, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667980736, "dur": 196, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667980937, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667980941, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667980966, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667980968, "dur": 108, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667981081, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667981084, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667981120, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667981125, "dur": 236, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667981366, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667981370, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667981393, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667981397, "dur": 237, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667981640, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667981643, "dur": 209, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667981860, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667981864, "dur": 71, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667981940, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667981944, "dur": 626, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667982578, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667982582, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667982619, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367667982625, "dur": 42523, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668025293, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668025300, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668025369, "dur": 2473, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668027850, "dur": 5118, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668032979, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668032986, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668033020, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668033024, "dur": 48, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668033076, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668033079, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668033155, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668033160, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668033200, "dur": 76, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668033284, "dur": 270, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668033560, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668033564, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668033588, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668033592, "dur": 158, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668033753, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668033757, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668033787, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668033790, "dur": 375, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668034169, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668034172, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668034188, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668034191, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668034250, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668034254, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668034268, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668034272, "dur": 912, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668035188, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668035191, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668035209, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668035212, "dur": 63, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668035280, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668035286, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668035321, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668035325, "dur": 68, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668035396, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668035408, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668035424, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668035427, "dur": 227, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668035657, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668035660, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668035677, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668035679, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668035722, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668035726, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668035741, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668035744, "dur": 80, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668035828, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668035831, "dur": 80, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668035915, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668035918, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668035933, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668035936, "dur": 219, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668036161, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668036164, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668036182, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668036185, "dur": 602, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668036791, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668036794, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668036824, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668036830, "dur": 214, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668037049, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668037052, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668037069, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668037072, "dur": 704, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668037780, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668037783, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668037799, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668037802, "dur": 75, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668037881, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668037885, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668037903, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668037906, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668037942, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668037944, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668037959, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668037961, "dur": 238, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668038206, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668038209, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668038223, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668038226, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668038244, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668038247, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668038324, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668038328, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668038345, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668038348, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668038373, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668038376, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668038391, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668038393, "dur": 202, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668038599, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668038602, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668038629, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668038633, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668038649, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668038651, "dur": 132, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668038787, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668038790, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668038806, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668038809, "dur": 153, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668038966, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668038968, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668038984, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668038986, "dur": 420, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668039415, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668039419, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668039437, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668039440, "dur": 247, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668039691, "dur": 54, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668039749, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668039789, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668039793, "dur": 696, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668040496, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668040502, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668040572, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668040576, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668040610, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668040613, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668040672, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668040678, "dur": 203, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668040885, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668040888, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668040916, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668040923, "dur": 125, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668041055, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668041059, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668041081, "dur": 32, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668041136, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668041159, "dur": 20, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668041182, "dur": 91, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668041279, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668041283, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668041343, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668041347, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668041367, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668041370, "dur": 128, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668041502, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668041505, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668041522, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668041526, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668041552, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668041557, "dur": 265, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668041828, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668041833, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668041855, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668041858, "dur": 505, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668042369, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668042374, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668042449, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668042457, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668042524, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668042527, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668042573, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668042577, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668042646, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668042649, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668042664, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668042667, "dur": 624, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668043297, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668043301, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668043325, "dur": 60, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668043388, "dur": 184, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668043576, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668043580, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668043617, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668043621, "dur": 274, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668043899, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668043903, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668043964, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668043968, "dur": 90, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668044063, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668044066, "dur": 299, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668044373, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668044377, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668044429, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668044433, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668044455, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668044458, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668044477, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668044479, "dur": 743, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668045228, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668045232, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668045309, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668045314, "dur": 172, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668045490, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668045493, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668045511, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668045514, "dur": 295, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668045814, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668045817, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668045841, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668045845, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668045861, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668045864, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668045892, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668045895, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668045950, "dur": 12, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668045965, "dur": 60, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668046060, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668046070, "dur": 70, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668046146, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668046153, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668046195, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668046198, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668046222, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668046226, "dur": 11, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668046239, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668046241, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668046262, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668046265, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668046283, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668046287, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668046308, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668046311, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668046332, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668046336, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668046357, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668046361, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668046412, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668046417, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668046467, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668046532, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668046557, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668046570, "dur": 66, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668046641, "dur": 369, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668047253, "dur": 86, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668047344, "dur": 28, "ph": "X", "name": "ProcessMessages 1564", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668047375, "dur": 29, "ph": "X", "name": "ReadAsync 1564", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668047412, "dur": 3, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668047417, "dur": 28, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668047451, "dur": 3, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668047458, "dur": 22, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668047483, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668047487, "dur": 15, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668047504, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668047508, "dur": 64, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668047578, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668047582, "dur": 83, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668047670, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668047673, "dur": 246, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668047925, "dur": 13, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668047940, "dur": 175, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668048121, "dur": 3, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668048127, "dur": 114, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668048247, "dur": 5, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367668048259, "dur": 1067981, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669116251, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669116258, "dur": 119, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669116383, "dur": 28, "ph": "X", "name": "ProcessMessages 1632", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669116413, "dur": 8086, "ph": "X", "name": "ReadAsync 1632", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669124510, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669124516, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669124623, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669124633, "dur": 96237, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669220893, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669220899, "dur": 127, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669221033, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669221040, "dur": 104859, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669325911, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669325918, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669325948, "dur": 29, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669325979, "dur": 16497, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669342490, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669342496, "dur": 148, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669342651, "dur": 14, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669342667, "dur": 877, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669343551, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669343567, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669343659, "dur": 74, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669343737, "dur": 23622, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669367372, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669367378, "dur": 107, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669367492, "dur": 30, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669367525, "dur": 15254, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669382791, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669382798, "dur": 137, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669382942, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669382951, "dur": 61620, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669444598, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669444604, "dur": 106, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669444717, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669444725, "dur": 560, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669445291, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669445295, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669445331, "dur": 23, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669445356, "dur": 463, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669445825, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669445829, "dur": 113, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669445948, "dur": 19, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669445969, "dur": 14399, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669460380, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669460401, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669460460, "dur": 5, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669460467, "dur": 266, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669460739, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669460743, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669460785, "dur": 105, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669460894, "dur": 1110, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669462011, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669462014, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669462082, "dur": 584, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751367669462684, "dur": 27547, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 14916, "tid": 2627, "ts": 1751367669508445, "dur": 5262, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 14916, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 14916, "tid": 8589934592, "ts": 1751367667879326, "dur": 375687, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 14916, "tid": 8589934592, "ts": 1751367668255016, "dur": 6, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 14916, "tid": 8589934592, "ts": 1751367668255024, "dur": 1232, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 14916, "tid": 2627, "ts": 1751367669513712, "dur": 11, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 14916, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 14916, "tid": 4294967296, "ts": 1751367667855864, "dur": 1636371, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 14916, "tid": 4294967296, "ts": 1751367667861436, "dur": 8948, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 14916, "tid": 4294967296, "ts": 1751367669492655, "dur": 5390, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 14916, "tid": 4294967296, "ts": 1751367669495932, "dur": 161, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 14916, "tid": 4294967296, "ts": 1751367669498145, "dur": 31, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 14916, "tid": 2627, "ts": 1751367669513726, "dur": 20, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751367667895965, "dur": 1805, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751367667897777, "dur": 1704, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751367667899601, "dur": 148, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751367667899749, "dur": 331, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751367667900967, "dur": 1956, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_4B40DB5AF1CE9D22.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751367667903573, "dur": 2339, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751367667906696, "dur": 185, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Extras.ref.dll_FD54DBCE44D88AE4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751367667906981, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_A1148DEAE75EA1F6.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751367667907087, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PlasticSCM.Editor.ref.dll_EBB1919501649DC3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751367667907997, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751367667908740, "dur": 114, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751367667908923, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751367667911360, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Animation.Editor.ref.dll_132A69C58FEF3318.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751367667911797, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751367667913244, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.ForUI.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751367667913514, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751367667913823, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751367667914238, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751367667900092, "dur": 25364, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751367667925473, "dur": 1536154, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751367669461629, "dur": 946, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751367669462717, "dur": 50, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751367669462782, "dur": 23236, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751367667900782, "dur": 24812, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751367667925596, "dur": 518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_57E453305C48C0D1.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751367667926161, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_D0FAA40E40B36FD7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751367667926244, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_E3AA45DCF433BC79.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751367667926404, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_767627B5731476EF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751367667926542, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_239A276499DBD1EB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751367667926666, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_4B40DB5AF1CE9D22.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751367667926898, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_189352823CD57890.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751367667927072, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751367667927335, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751367667927518, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751367667927647, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/PsdPlugin.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751367667928012, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751367667928570, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751367667928847, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751367667929077, "dur": 1315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751367667930392, "dur": 806, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751367667931198, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751367667931911, "dur": 948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751367667932860, "dur": 1961, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751367667934824, "dur": 3517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751367667938341, "dur": 3065, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751367667943806, "dur": 614, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@911117698220\\Editor\\Generation\\Targets\\BuiltIn\\Editor\\ShaderGraph\\BuiltInMetadata.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751367667944420, "dur": 513, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@911117698220\\Editor\\Generation\\Targets\\BuiltIn\\Editor\\ShaderGraph\\BuiltInFields.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751367667941406, "dur": 3781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751367667945187, "dur": 2815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751367667948556, "dur": 724, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@911117698220\\Editor\\Data\\Interfaces\\IMayRequireTime.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751367667948002, "dur": 4297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751367667952299, "dur": 2691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751367667954990, "dur": 2664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751367667957654, "dur": 2745, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751367667960456, "dur": 913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751367667961369, "dur": 1631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751367667964017, "dur": 819, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.Core\\Inspection\\MetadataDictionaryAdaptor.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751367667965170, "dur": 563, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.Core\\Inspection\\InspectorProvider.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751367667963000, "dur": 2769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751367667966524, "dur": 553, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.common@bb1fc9b3d81b\\Editor\\InternalBridge\\TexturePlatformSettings\\TexturePlatformSettings.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751367667965769, "dur": 1427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751367667967196, "dur": 1177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751367667968401, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751367667969146, "dur": 2001, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.common@bb1fc9b3d81b\\Runtime\\UTess2D\\Refinery.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751367667971175, "dur": 729, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.common@bb1fc9b3d81b\\Runtime\\UTess2D\\ArraySlice.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751367667968882, "dur": 3215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751367667972097, "dur": 663, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751367667972799, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751367667973007, "dur": 805, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751367667973863, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/ScriptablePacker.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751367667974087, "dur": 628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/ScriptablePacker.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751367667974780, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751367667975013, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751367667975235, "dur": 531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751367667975767, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751367667975897, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751367667976110, "dur": 610, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751367667976721, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751367667976782, "dur": 280, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751367667977065, "dur": 2074, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751367667979140, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751367667979210, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751367667979347, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751367667979742, "dur": 2497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751367667982239, "dur": 49254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751367668031494, "dur": 2589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/NativeFilePicker.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751367668034084, "dur": 833, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751367668034924, "dur": 2579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751367668037504, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751367668037651, "dur": 2571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751367668040260, "dur": 2881, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751367668043176, "dur": 2499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualEffectGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751367668045676, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751367668045732, "dur": 2838, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751367668048602, "dur": 1413050, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367667900542, "dur": 24966, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367667925511, "dur": 567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_3122CFBB2DC47329.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751367667926112, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_1237F680F70DA85A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751367667926450, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367667926530, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_B72398FA1A1CE63D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751367667926658, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_0E5ABA1EF864C1B6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751367667927071, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751367667927374, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751367667928016, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367667928251, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10054408285822421624.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751367667928576, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367667928876, "dur": 1854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367667930731, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367667931537, "dur": 1066, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367667932603, "dur": 1093, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367667935086, "dur": 543, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@ba0fe1b085e9\\Editor\\Inspector\\VFXSlotContainerEditor.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751367667933696, "dur": 2593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367667936289, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367667936579, "dur": 3609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367667940420, "dur": 570, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\inspectors\\ClipInspector\\ClipInspectorCurveEditor.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751367667940188, "dur": 2867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367667943056, "dur": 3018, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367667948363, "dur": 515, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@911117698220\\Editor\\Data\\Nodes\\Math\\Basic\\SquareRootNode.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751367667946075, "dur": 4887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367667950963, "dur": 2590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367667954033, "dur": 518, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@a394fe607f89\\Runtime\\UniversalRenderPipelineCore.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751367667955491, "dur": 572, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@a394fe607f89\\Runtime\\UniversalAdditionalLightData.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751367667953553, "dur": 4706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367667958715, "dur": 745, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@ba0fe1b085e9\\Runtime\\Utilities\\PropertyBinding\\Implementation\\VFXPreviousPositionBinder.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751367667958260, "dur": 2366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367667960626, "dur": 864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367667961490, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367667961729, "dur": 1543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367667963815, "dur": 1156, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.Core\\Dependencies\\ReorderableList\\Internal\\GUIHelper.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751367667963272, "dur": 2052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367667965324, "dur": 869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367667966527, "dur": 546, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.common@bb1fc9b3d81b\\Editor\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751367667966194, "dur": 1312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367667969177, "dur": 605, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Core\\Listeners\\MonoBehaviour\\UnityOnTransformParentChangedMListener.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751367667967507, "dur": 2275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367667969783, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751367667970069, "dur": 599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751367667970669, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367667970807, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367667970882, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751367667971131, "dur": 591, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751367667971772, "dur": 765, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751367667972538, "dur": 280, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367667972822, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751367667973044, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367667973191, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367667973865, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367667974454, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367667974879, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367667975625, "dur": 126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367667975815, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367667975926, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751367667976119, "dur": 412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751367667976532, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367667976630, "dur": 376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367667977009, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751367667977157, "dur": 402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751367667977620, "dur": 1536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367667979156, "dur": 3080, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367667982237, "dur": 49244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367668031482, "dur": 2309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751367668033794, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367668033993, "dur": 2653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751367668036647, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367668036766, "dur": 1957, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751367668038724, "dur": 445, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367668039176, "dur": 2622, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751367668041799, "dur": 386, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367668042191, "dur": 2150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751367668044342, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367668044422, "dur": 2147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751367668046570, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367668046910, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367668047158, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367668047317, "dur": 978, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367668048295, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751367668048365, "dur": 1072809, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367669121196, "dur": 97899, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751367669121176, "dur": 99327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751367669221466, "dur": 179, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751367669221935, "dur": 104726, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751367669343051, "dur": 102251, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751367669343044, "dur": 102259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751367669445320, "dur": 757, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751367669446083, "dur": 15548, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751367667900372, "dur": 25106, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751367667925544, "dur": 477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_98CF6329D3D6F9AB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751367667926041, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_12F61435F0A80E5C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751367667926141, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_424FA579C047A49A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751367667926411, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_32AC050EF3A70AF5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751367667926611, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_A0847C1FE8DF0FE2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751367667926724, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_4EAA64F102651546.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751367667927034, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751367667927194, "dur": 24938, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751367667953497, "dur": 601, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@7bffd5bb179a\\Editor\\LookDev\\LookDev.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751367667952218, "dur": 3778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751367667956437, "dur": 715, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.Flow\\Plugin\\Migrations\\Migration_1_3_0_to_1_4_0.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751367667958197, "dur": 598, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.Flow\\Plugin\\Migrations\\Migration_1_1_1_to_1_1_2.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751367667955996, "dur": 4024, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751367667960020, "dur": 1101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751367667963464, "dur": 1326, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Flow\\Framework\\Collections\\FirstItem.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751367667961122, "dur": 3686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751367667966018, "dur": 568, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@7bffd5bb179a\\Runtime\\Utilities\\GPUSort\\GPUSort.Data.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751367667964808, "dur": 1778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751367667966587, "dur": 1315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751367667969319, "dur": 1051, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Core\\Ensure\\EnsureThat.Strings.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751367667967902, "dur": 2853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751367667970756, "dur": 684, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751367667971492, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751367667971648, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751367667971887, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751367667972080, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751367667972206, "dur": 583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751367667972790, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751367667972932, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751367667973205, "dur": 859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751367667974064, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751367667974391, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751367667974978, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751367667975227, "dur": 679, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751367667975907, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751367667976016, "dur": 993, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751367667977009, "dur": 748, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751367667977758, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751367667977922, "dur": 1110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751367667979097, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751367667979152, "dur": 2644, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751367667981797, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751367667981912, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751367667982234, "dur": 49241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751367668031477, "dur": 2285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751367668033763, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751367668033833, "dur": 2247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751367668036080, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751367668036151, "dur": 2530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751367668038733, "dur": 2418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751367668041153, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751367668041456, "dur": 3316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751367668044812, "dur": 3892, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751367668048791, "dur": 1412854, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751367667900436, "dur": 25051, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751367667925546, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_AAD14E71D524EA60.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751367667926048, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_D8154579280ABE30.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751367667926131, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_05122CAABA8E3B59.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751367667926238, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_DAD3C4523F8649DE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751367667926417, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_41563AEB7BF951B8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751367667926536, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_099E8E7D5E4735AD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751367667926692, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_E2C1B1DA45896D6E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751367667928032, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751367667928235, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11908194702949990099.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751367667928608, "dur": 395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751367667929003, "dur": 1291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751367667930295, "dur": 840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751367667931135, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751367667931736, "dur": 1194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751367667932930, "dur": 1738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751367667934852, "dur": 500, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@ba0fe1b085e9\\Editor\\Debug\\DotGraphOutput.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751367667934668, "dur": 3858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751367667940296, "dur": 547, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\TimelineUtility.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751367667938526, "dur": 3944, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751367667942470, "dur": 4129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751367667948380, "dur": 542, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@911117698220\\Editor\\Data\\Nodes\\Input\\Geometry\\TangentVectorNode.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751367667946599, "dur": 4045, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751367667950645, "dur": 3274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751367667953919, "dur": 3639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751367667957559, "dur": 2647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751367667960206, "dur": 1248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751367667961455, "dur": 881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751367667962337, "dur": 1045, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751367667963824, "dur": 925, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.Core\\Analysis\\AnalyserAttribute.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751367667963382, "dur": 2058, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751367667966620, "dur": 510, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@7bffd5bb179a\\Runtime\\Debugging\\Prefabs\\Scripts\\DebugUIHandlerButton.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751367667965441, "dur": 2674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751367667968116, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751367667969330, "dur": 544, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Core\\Collections\\ISet.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751367667968432, "dur": 1673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751367667970106, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751367667970292, "dur": 794, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751367667971087, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751367667971179, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751367667971411, "dur": 614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751367667972119, "dur": 647, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751367667972766, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751367667972868, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity-editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751367667973212, "dur": 1066, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity-editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751367667974326, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751367667974887, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751367667975187, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751367667975995, "dur": 1023, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751367667977018, "dur": 2149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751367667979167, "dur": 3066, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751367667982233, "dur": 49223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751367668031458, "dur": 3313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751367668034772, "dur": 324, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751367668035103, "dur": 2762, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751367668037908, "dur": 2552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751367668040460, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751367668040540, "dur": 2898, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751367668043439, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751367668043497, "dur": 2178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751367668045719, "dur": 2457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751367668048177, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751367668048361, "dur": 211657, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751367668260019, "dur": 1201615, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751367667900513, "dur": 24985, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751367667925500, "dur": 550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_7B22B0BDD3082718.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751367667926071, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_24CD4430EA38EE97.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751367667926210, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_A1C14A714FD44069.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751367667926349, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_88313D863DE50351.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751367667926430, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_E951570DA9D21231.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751367667926698, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_62645ED13E051483.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751367667926991, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751367667927127, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751367667927356, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751367667928026, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751367667928252, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18127688178268093734.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751367667928603, "dur": 344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751367667928947, "dur": 1666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751367667930613, "dur": 861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751367667931475, "dur": 1466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751367667932942, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751367667933592, "dur": 1754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751367667935346, "dur": 3724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751367667940760, "dur": 713, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\inspectors\\TimelineAssetInspector.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751367667939070, "dur": 3368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751367667942438, "dur": 4115, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751367667946553, "dur": 4450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751367667951882, "dur": 599, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.State\\Flow\\StateUnitDescriptor.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751367667951003, "dur": 3887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751367667954890, "dur": 4117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751367667959007, "dur": 1644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751367667960651, "dur": 1312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751367667961963, "dur": 1617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751367667963825, "dur": 946, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@24b10291b18f\\Runtime\\TMP\\TMP_FontAssetUtilities.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751367667963581, "dur": 1782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751367667966305, "dur": 811, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@7bffd5bb179a\\Runtime\\Debugging\\Prefabs\\Scripts\\DebugUIHandlerMessageBox.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751367667965363, "dur": 1934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751367667967297, "dur": 1641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751367667968938, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751367667969232, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751367667969490, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751367667969628, "dur": 1273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751367667970901, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751367667971038, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751367667971094, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751367667971292, "dur": 5248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751367667976540, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751367667976749, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751367667977019, "dur": 542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751367667977605, "dur": 1555, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751367667979160, "dur": 3074, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751367667982234, "dur": 49226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751367668031461, "dur": 1904, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751367668033366, "dur": 418, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751367668033793, "dur": 2012, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751367668035806, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751367668036128, "dur": 2480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp-firstpass.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751367668038608, "dur": 1203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751367668039818, "dur": 2767, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751367668042586, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751367668042649, "dur": 2485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751367668045135, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751367668045214, "dur": 3039, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751367668048325, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751367668048554, "dur": 1413072, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751367667900498, "dur": 24994, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751367667925495, "dur": 518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_7A744375D0C40DCD.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751367667926044, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_7BCDC5BDF664862C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751367667926127, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_47EFA976E901FEC1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751367667926236, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_299E64FBD7A1C295.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751367667926406, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_76128D4AF776323F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751367667926474, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_34D7E2BEC34818D5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751367667926593, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_9B331AB6767E3EC1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751367667927289, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751367667928013, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751367667928229, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751367667928492, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751367667928601, "dur": 347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751367667928949, "dur": 1592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751367667930541, "dur": 593, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@24b10291b18f\\Editor\\TMP\\TMPro_FontAssetCreatorWindow.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751367667930541, "dur": 1932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751367667932474, "dur": 840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751367667933315, "dur": 1464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751367667934897, "dur": 594, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@ba0fe1b085e9\\Editor\\Controls\\VFXStringField.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751367667934779, "dur": 4072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751367667938851, "dur": 3531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751367667942385, "dur": 3538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751367667945924, "dur": 3961, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751367667949886, "dur": 1599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751367667953785, "dur": 640, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@7bffd5bb179a\\Editor\\ShaderStripping\\ShaderStrippingReport.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751367667951486, "dur": 3094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751367667955097, "dur": 1953, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@a394fe607f89\\Runtime\\Passes\\CapturePass.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751367667954580, "dur": 5107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751367667959687, "dur": 1004, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751367667961967, "dur": 703, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Hierarchy\\OnTransformChildrenChanged.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751367667960691, "dur": 2111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751367667964013, "dur": 814, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.Core\\Inspection\\Primitives\\UlongInspector.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751367667962803, "dur": 3066, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751367667966410, "dur": 608, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.spriteshape@9e35352ae135\\Runtime\\SpriteShapeGeometryCreator.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751367667965870, "dur": 2242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751367667968112, "dur": 640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751367667969144, "dur": 1654, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collections@56bff8827a7e\\Unity.Collections\\Jobs\\EarlyInitHelpers.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751367667968753, "dur": 2413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751367667971167, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751367667971347, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751367667971559, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751367667971743, "dur": 356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751367667972125, "dur": 681, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751367667972807, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751367667972871, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751367667973293, "dur": 559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751367667973852, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751367667973917, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751367667974606, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751367667974877, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751367667975199, "dur": 542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751367667975818, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751367667975930, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751367667976115, "dur": 535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751367667976651, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751367667976708, "dur": 297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751367667977007, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751367667977143, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751367667977504, "dur": 1627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751367667979133, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751367667979264, "dur": 582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751367667979906, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751367667980006, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751367667980304, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751367667980444, "dur": 1787, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751367667982231, "dur": 49227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751367668031459, "dur": 3072, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751367668034532, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751367668034602, "dur": 3169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751367668037772, "dur": 850, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751367668038630, "dur": 3440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751367668042071, "dur": 1291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751367668043391, "dur": 2374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751367668045766, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751367668045876, "dur": 2621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751367668048543, "dur": 1413087, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367667900667, "dur": 24887, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367667925556, "dur": 487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_21E5C8D6C2CA360E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751367667926157, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_692349F0772110A2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751367667926256, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_60DEBCA51ABA1468.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751367667926336, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_D3788CF92C3C660B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751367667926408, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_EAC3CE9D642351D7.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751367667926550, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_634E261A11766DA3.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751367667926631, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_4AAF7BB67CBB0CA5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751367667926718, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_AFF4160AB2736538.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751367667927203, "dur": 265, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_B867065A6613A379.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751367667927809, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751367667928049, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367667928599, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367667928847, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367667929927, "dur": 830, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367667930758, "dur": 1203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367667931961, "dur": 822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367667932784, "dur": 1007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367667933792, "dur": 2891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367667936684, "dur": 4493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367667941177, "dur": 2922, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367667947924, "dur": 755, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@911117698220\\Editor\\Drawing\\Controls\\DielectricSpecularControl.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751367667944100, "dur": 4840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367667948940, "dur": 3478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367667953787, "dur": 575, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@7bffd5bb179a\\Editor\\Lighting\\LightUnit\\LightUnitSlider.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751367667954704, "dur": 500, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@7bffd5bb179a\\Editor\\Lighting\\LightUI.Drawers.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751367667952418, "dur": 4047, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367667957863, "dur": 517, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.Flow\\Linker\\LinkerCreator.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751367667956466, "dur": 3628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367667960094, "dur": 1039, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367667961133, "dur": 1571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367667962705, "dur": 592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367667963845, "dur": 959, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.Core\\Decorators\\MultiDecoratorProvider.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751367667964991, "dur": 679, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.Core\\Context\\GraphContextExtensionProvider.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751367667963297, "dur": 2756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367667966522, "dur": 547, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.common@bb1fc9b3d81b\\Path\\Editor\\IMGUI\\GUIFramework\\GenericDefaultControl.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751367667966054, "dur": 1604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367667969154, "dur": 574, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Core\\Listeners\\MonoBehaviour\\UnityOnCollisionExit2DMessageListener.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751367667967658, "dur": 2166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367667969826, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751367667969976, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367667970032, "dur": 590, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751367667970623, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367667970725, "dur": 1277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751367667972074, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.PixelPerfect.ref.dll_444C51352B70AA18.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751367667972200, "dur": 663, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751367667972864, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367667973011, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367667973245, "dur": 1159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367667974405, "dur": 348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367667974986, "dur": 901, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@001b54a8988a\\Editor\\Views\\PendingChanges\\FilesFilterPatternsMenuBuilder.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751367667974786, "dur": 1835, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367667976621, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367667977004, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751367667977155, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751367667977521, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367667977610, "dur": 1537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367667979148, "dur": 1046, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367667980194, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751367667980310, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751367667980663, "dur": 1593, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367667982256, "dur": 51823, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367668034080, "dur": 2438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751367668036575, "dur": 2349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-unity.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751367668038924, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367668039084, "dur": 2548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751367668041633, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367668041901, "dur": 2484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/ScriptablePacker.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751367668044433, "dur": 2108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751367668046542, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367668046834, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367668046963, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367668047364, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367668047716, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367668048021, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367668048130, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751367668048342, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751367668048608, "dur": 1413016, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751367667900531, "dur": 24972, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751367667925505, "dur": 503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_89517EAC2A874601.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751367667926038, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_C32E3DE896B50E07.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751367667926145, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_07ED47E0EFE03F3D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751367667926434, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_EFD593047BD2E6A8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751367667926553, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_C3115380BB649476.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751367667926641, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_2E3E35D488CA3F33.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751367667926891, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_B364C0B29F0D0D42.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751367667926976, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751367667927290, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751367667927437, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751367667927573, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751367667928012, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751367667928138, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751367667928245, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14507123614329245381.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751367667928578, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751367667928861, "dur": 1099, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751367667929962, "dur": 1081, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751367667931044, "dur": 975, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751367667932019, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751367667933041, "dur": 770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751367667933811, "dur": 2676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751367667936487, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751367667936733, "dur": 3548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751367667941625, "dur": 527, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Attributes\\MenuEntryAttribute.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751367667940282, "dur": 3883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751367667944824, "dur": 749, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@911117698220\\Editor\\Drawing\\Controls\\ChannelEnumMaskControl.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751367667945573, "dur": 615, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@911117698220\\Editor\\Drawing\\Controls\\ChannelEnumControl.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751367667946669, "dur": 546, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@911117698220\\Editor\\Drawing\\Controllers\\SGController.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751367667947215, "dur": 607, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@911117698220\\Editor\\Drawing\\Controllers\\BlackboardController.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751367667944166, "dur": 5590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751367667951196, "dur": 536, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@911117698220\\Editor\\Data\\Graphs\\Matrix2ShaderProperty.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751367667949757, "dur": 2819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751367667952577, "dur": 3355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751367667955932, "dur": 3873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751367667959805, "dur": 1385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751367667961190, "dur": 1401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751367667963938, "dur": 882, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.Core\\Interface\\Fuzzy\\FuzzyOptionNode.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751367667962592, "dur": 2285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751367667964877, "dur": 2591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751367667967469, "dur": 698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751367667968167, "dur": 1026, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751367667969194, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751367667969437, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751367667969550, "dur": 6004, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751367667975555, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751367667975680, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751367667975896, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751367667976137, "dur": 699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751367667976837, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751367667977002, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751367667977242, "dur": 459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751367667977739, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751367667977846, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751367667978383, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751367667978539, "dur": 502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751367667979131, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751367667979268, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751367667979698, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751367667979801, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751367667980188, "dur": 2041, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751367667982230, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751367667982386, "dur": 49099, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751367668031486, "dur": 2303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751367668033790, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751367668033878, "dur": 2372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751367668036250, "dur": 347, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751367668036601, "dur": 2579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751367668039181, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751367668039451, "dur": 2372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751367668041824, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751367668042001, "dur": 5565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751367668047567, "dur": 457, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751367668048111, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751367668048286, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751367668048354, "dur": 435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751367668048789, "dur": 1412860, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367667900606, "dur": 24941, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367667925550, "dur": 511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_D07FFB3B2770754A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751367667926094, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_966AC74B818A5F90.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751367667926282, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_41BBAEC6AFBBC340.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751367667926360, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_366576C77F483F0B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751367667926431, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_A5513E9150BC6315.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751367667926559, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_B9FBC03C59DCE731.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751367667926646, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_E496E41FECA98925.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751367667926911, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751367667926990, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367667927068, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367667927211, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_D4B653123EE78879.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751367667927339, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751367667927439, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1751367667928034, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367667928572, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367667928936, "dur": 1860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367667930796, "dur": 1223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367667932020, "dur": 1104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367667933125, "dur": 1507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367667937108, "dur": 644, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@ba0fe1b085e9\\Editor\\Expressions\\VFXExpressionAbstractFloatOperation.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751367667934632, "dur": 4130, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367667938762, "dur": 3607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367667944751, "dur": 603, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@911117698220\\Editor\\Generation\\Descriptors\\PassDescriptor.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751367667942369, "dur": 4202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367667950201, "dur": 736, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@911117698220\\Editor\\Data\\Nodes\\Input\\Matrix\\Matrix4Node.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751367667946572, "dur": 5100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367667951673, "dur": 3558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367667956969, "dur": 787, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.State\\IStateTransitionDebugData.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751367667955231, "dur": 4722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367667959953, "dur": 1494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367667961447, "dur": 715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367667963820, "dur": 1033, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.Core\\Plugin\\Changelogs\\LegacyLudiqCore\\Changelog_1_3_0.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751367667962163, "dur": 3063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367667965599, "dur": 504, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@7bffd5bb179a\\Runtime\\RenderGraph\\IRenderGraphBuilder.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751367667966515, "dur": 509, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@7bffd5bb179a\\Runtime\\PostProcessing\\LensFlareDataSRP.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751367667965226, "dur": 1811, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367667967037, "dur": 1690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367667969192, "dur": 593, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collections@56bff8827a7e\\Unity.Collections\\NativeQueue.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751367667968728, "dur": 1547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367667970277, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751367667970497, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367667970595, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751367667970954, "dur": 1000, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751367667971955, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367667972036, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751367667972192, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367667972348, "dur": 500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751367667972849, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367667972913, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.ref.dll_5CA1595A93B2C59B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751367667973041, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751367667973449, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751367667973688, "dur": 542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751367667974231, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367667974406, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367667974798, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367667975228, "dur": 593, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@001b54a8988a\\Editor\\UI\\UnityEvents.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751367667975066, "dur": 1149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367667976215, "dur": 795, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367667977013, "dur": 1373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367667978387, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751367667978518, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751367667978838, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751367667979133, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367667979297, "dur": 2931, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367667982230, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751367667982368, "dur": 49095, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367668031473, "dur": 2291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751367668033765, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367668033980, "dur": 2052, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751367668036034, "dur": 2988, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751367668039055, "dur": 2920, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751367668041976, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367668042045, "dur": 2268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751367668044315, "dur": 410, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367668044730, "dur": 2131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751367668046861, "dur": 666, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367668047630, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367668047738, "dur": 306, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367668048077, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367668048157, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367668048355, "dur": 538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751367668048893, "dur": 1412769, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751367667900681, "dur": 24879, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751367667925563, "dur": 519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_D5DC31B4B0DA901E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751367667926113, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_F01F6C04D397C746.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751367667926466, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_24D87271FC85A7AF.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751367667926589, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_7D6DB1EEB95EE9BC.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751367667926662, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_5099CB98C03D5F98.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751367667926886, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_A474E787AE0D7A75.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751367667927067, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1751367667927955, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1751367667928037, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751367667928221, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1751367667928612, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751367667928936, "dur": 1521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751367667930458, "dur": 1015, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751367667931473, "dur": 786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751367667932260, "dur": 1038, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751367667933928, "dur": 662, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@ba0fe1b085e9\\Editor\\Models\\Deprecated\\CollisionConeDeprecatedV2.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751367667933299, "dur": 2293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751367667935593, "dur": 444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751367667936037, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751367667936303, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751367667936553, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751367667939791, "dur": 653, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Window\\TimelineEditorWindow.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751367667936831, "dur": 3793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751367667942742, "dur": 548, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@911117698220\\Editor\\ShaderGraphToolbarExtension.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751367667940624, "dur": 3679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751367667944303, "dur": 3553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751367667947856, "dur": 3615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751367667953268, "dur": 724, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@7bffd5bb179a\\Editor\\Volume\\VolumeMenuItems.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751367667951471, "dur": 3759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751367667955230, "dur": 3618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751367667958848, "dur": 1860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751367667960708, "dur": 1703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751367667962411, "dur": 1133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751367667964083, "dur": 765, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@24b10291b18f\\Runtime\\TMP\\TMP_RichTextTagsCommon.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751367667963544, "dur": 1688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751367667966513, "dur": 533, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@7bffd5bb179a\\Runtime\\Lighting\\ProbeVolume\\ProbeVolumeGlobalSettings.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751367667965232, "dur": 2222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751367667967455, "dur": 1679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751367667969134, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751367667969407, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751367667969567, "dur": 3689, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751367667973257, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751367667973393, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751367667973723, "dur": 1153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751367667974969, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751367667975157, "dur": 687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751367667975845, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751367667975922, "dur": 748, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 10, "ts": 1751367667976671, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751367667976789, "dur": 302, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751367667977714, "dur": 48217, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 10, "ts": 1751367668031452, "dur": 2296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751367668033790, "dur": 2203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-csharp.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751367668035994, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751367668036063, "dur": 2462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751367668038526, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751367668038795, "dur": 2290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751367668041086, "dur": 616, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751367668041708, "dur": 2149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751367668043858, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751367668043954, "dur": 2147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-unity-editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751367668046105, "dur": 457, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751367668046569, "dur": 2015, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751367668048639, "dur": 1413018, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751367667900695, "dur": 24872, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751367667925569, "dur": 467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Apple.Extensions.Common.dll_D4E020400338F5BB.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751367667926056, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_385F174668EB898F.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751367667926133, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_3713E40F818C5856.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751367667926251, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_EB030C3D9E9F35E6.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751367667926472, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_B70C9B9527050002.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751367667926679, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_3C70E268A86B5279.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751367667926891, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_BACE11EBDBD2371C.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751367667927440, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751367667927809, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1751367667928030, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751367667928369, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18074560900205879295.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751367667928597, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751367667928886, "dur": 1592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751367667930479, "dur": 852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751367667931331, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751367667931894, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751367667932760, "dur": 1306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751367667934066, "dur": 3326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751367667938845, "dur": 566, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Window\\Modes\\TimelineActiveMode.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751367667937393, "dur": 3681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751367667941074, "dur": 2728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751367667943803, "dur": 3960, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751367667947763, "dur": 3767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751367667951765, "dur": 573, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@7bffd5bb179a\\Editor\\ShaderGenerator\\ShaderGeneratorMenu.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751367667951530, "dur": 3205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751367667957285, "dur": 503, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@a394fe607f89\\Runtime\\IntermediateTextureMode.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751367667957789, "dur": 525, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@a394fe607f89\\Runtime\\History\\TaaHistory.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751367667954736, "dur": 4184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751367667958920, "dur": 1775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751367667960695, "dur": 1231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751367667961926, "dur": 1672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751367667963634, "dur": 1098, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@24b10291b18f\\Runtime\\TMP\\ITextPreProcessor.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751367667963598, "dur": 1332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751367667964930, "dur": 1143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751367667966469, "dur": 665, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.common@bb1fc9b3d81b\\Editor\\TextureGenerator\\TextureGeneratorHelper.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751367667966074, "dur": 1197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751367667969208, "dur": 616, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Core\\Reflection\\Operators\\ModuloHandler.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751367667967271, "dur": 2553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751367667969825, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751367667970035, "dur": 2944, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751367667972980, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751367667973123, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751367667973317, "dur": 782, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751367667974099, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751367667974183, "dur": 411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751367667974595, "dur": 526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751367667975122, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751367667975186, "dur": 777, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751367667976005, "dur": 1009, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751367667977014, "dur": 2149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751367667979163, "dur": 3094, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751367667982257, "dur": 49212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751367668031474, "dur": 2285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/OutlineFx.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751367668033799, "dur": 2283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751367668036083, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751367668036141, "dur": 2952, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751367668039094, "dur": 2231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751367668041332, "dur": 5789, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751367668047353, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751367668047435, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751367668047762, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751367668048075, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751367668048324, "dur": 182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751367668048507, "dur": 1412494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751367669461019, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751367669461002, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751367669461110, "dur": 462, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751367669461576, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751367667900727, "dur": 24846, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751367667925575, "dur": 483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_E4B698B849B24AEB.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751367667926076, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_AB9848F6D5CE3BFE.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751367667926147, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_5A6241C5E4FBF31E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751367667926416, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_15728F368AD157F8.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751367667926651, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_26C00F9C5DF6EF10.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751367667927245, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1751367667928019, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751367667928139, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1751367667928279, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14324468880911302389.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751367667928470, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1929131816769834074.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751367667928649, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751367667928914, "dur": 1613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751367667930527, "dur": 731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751367667931258, "dur": 1413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751367667932672, "dur": 872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751367667933545, "dur": 1625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751367667935170, "dur": 3445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751367667941376, "dur": 3276, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Shortcuts.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751367667945286, "dur": 558, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Recording\\AnimationTrackRecorder.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751367667938615, "dur": 7429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751367667946045, "dur": 3720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751367667949766, "dur": 2773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751367667954753, "dur": 532, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@7bffd5bb179a\\Editor\\EditorWindowWithHelpButton.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751367667952539, "dur": 4096, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751367667958405, "dur": 529, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.Flow\\Framework\\Control\\SelectUnitDescriptor.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751367667956635, "dur": 3377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751367667960012, "dur": 1239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751367667961251, "dur": 1784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751367667963888, "dur": 927, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.Core\\Graph\\GraphGUI.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751367667963036, "dur": 2046, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751367667965374, "dur": 512, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@7bffd5bb179a\\Runtime\\RenderGraph\\RenderGraphResourceTexture.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751367667965082, "dur": 1536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751367667966619, "dur": 2023, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751367667969092, "dur": 641, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collections@56bff8827a7e\\Unity.Collections\\UnsafeParallelHashSet.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751367667968643, "dur": 1704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751367667970349, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751367667970640, "dur": 4282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751367667974993, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751367667975220, "dur": 739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751367667975960, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751367667976125, "dur": 883, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751367667977008, "dur": 733, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751367667977742, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751367667977855, "dur": 496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751367667978419, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751367667978592, "dur": 1494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751367667980160, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751367667980270, "dur": 846, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751367667981171, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751367667981264, "dur": 481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751367667981793, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751367667981901, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751367667982228, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751367667982375, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751367667982675, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751367667983350, "dur": 54, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751367667984315, "dur": 1132694, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751367669121455, "dur": 3255, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751367669121138, "dur": 3652, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751367669125118, "dur": 63, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751367669134384, "dur": 233728, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751367669371981, "dur": 9066, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751367669371973, "dur": 10285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751367669383356, "dur": 164, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751367669383789, "dur": 62853, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751367669460975, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1751367669460968, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1751367669461058, "dur": 539, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1751367667900761, "dur": 24823, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751367667925586, "dur": 550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_8AA31634E5830E2F.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751367667926164, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_68F0E05FD8235E34.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751367667926248, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_B5214F7A6C172998.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751367667926334, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_847E2D6319A6BEA0.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751367667926414, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_CE214F9E6C036FF9.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751367667926569, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_3CE377B30C500CB9.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751367667926713, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_ACB0EC32CF196063.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751367667926982, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751367667927141, "dur": 32985, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751367667960128, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751367667960256, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751367667960420, "dur": 7892, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751367667968398, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751367667968519, "dur": 800, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751367667969404, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751367667969527, "dur": 2090, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751367667971618, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751367667971873, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751367667972139, "dur": 601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751367667972797, "dur": 588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751367667973385, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751367667973539, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751367667973789, "dur": 617, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751367667974467, "dur": 402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751367667974869, "dur": 390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751367667975259, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751367667975818, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751367667975905, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751367667976170, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751367667976238, "dur": 1433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751367667977754, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751367667977894, "dur": 1181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751367667979075, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751367667979208, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751367667979371, "dur": 741, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751367667980193, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751367667980363, "dur": 555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751367667980985, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751367667981136, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751367667981513, "dur": 719, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751367667982232, "dur": 49262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751367668031495, "dur": 2795, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751367668034291, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751367668034402, "dur": 2542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751367668036999, "dur": 2357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751367668039357, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751367668039640, "dur": 2670, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751367668042355, "dur": 2938, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751367668045328, "dur": 3093, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751367668048488, "dur": 1294588, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751367669343096, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1751367669343078, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1751367669343231, "dur": 1096, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1751367669344331, "dur": 117301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667900746, "dur": 24832, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667925580, "dur": 490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_BA35B04A057142E1.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751367667926071, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667926150, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_B10B806206A4C9C0.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751367667926423, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_DA3F53EC74158B55.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751367667926625, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_A1DAD78AF9F61DC7.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751367667926704, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_39A3DCC912A474A9.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751367667927229, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751367667927747, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1751367667928026, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667928238, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6091109579032960000.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751367667928606, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667928884, "dur": 1403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667930288, "dur": 1126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667931414, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667931746, "dur": 1387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667933134, "dur": 819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667933954, "dur": 1704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667935658, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667935952, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667936190, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667936500, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667937844, "dur": 618, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.aseprite@1f731787b516\\Editor\\Common\\ImagePacker\\Jobs\\FindTightRectJob.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751367667936784, "dur": 3775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667940559, "dur": 2667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667943227, "dur": 3440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667949056, "dur": 541, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@911117698220\\Editor\\Data\\Nodes\\Channel\\SwizzleNode.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751367667946667, "dur": 3974, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667950641, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667950825, "dur": 3434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667954260, "dur": 2538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667956798, "dur": 2943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667959741, "dur": 1310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667961051, "dur": 1117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667962168, "dur": 1005, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667963881, "dur": 929, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.Core\\Description\\IMacroDescription.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751367667963173, "dur": 2290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667966314, "dur": 895, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@7bffd5bb179a\\Runtime\\Debugging\\DebugManager.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751367667965463, "dur": 2251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667967714, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667969236, "dur": 683, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\fsTypeConverter.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751367667968400, "dur": 1720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667970121, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751367667970344, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751367667970585, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751367667970799, "dur": 832, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751367667971632, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667971760, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751367667971998, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751367667972163, "dur": 949, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667973115, "dur": 858, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751367667973974, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667974077, "dur": 389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667974740, "dur": 612, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@920b46832575\\InputSystem\\Editor\\Analytics\\InputBuildAnalytic.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751367667974468, "dur": 884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667975819, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667975929, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751367667976112, "dur": 487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751367667976600, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667976690, "dur": 323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667977013, "dur": 1408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667978421, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751367667978602, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751367667979071, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667979155, "dur": 3080, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367667982236, "dur": 49248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367668031485, "dur": 2273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751367668033764, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367668033823, "dur": 2311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751367668036135, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367668036229, "dur": 2342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualEffectGraph.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751367668038572, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367668038640, "dur": 3435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751367668042076, "dur": 307, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367668042387, "dur": 2839, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751367668045269, "dur": 2628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/OutlineFx.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751367668048040, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367668048165, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1751367668048347, "dur": 334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751367668048681, "dur": 1412980, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751367667900823, "dur": 24777, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751367667925600, "dur": 505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_FB049F22F4C5497A.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751367667926138, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_A5FFABD953007C60.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751367667926287, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_94C8A9F1371998FD.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751367667926368, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_CD971E07FD450EB6.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751367667926463, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_8AA30A6C8DDCA8A3.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751367667926574, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_5312197D285F8F1E.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751367667927103, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751367667927337, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1751367667927487, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751367667927903, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751367667928009, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751367667928178, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751367667928568, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751367667928874, "dur": 1462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751367667930337, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751367667930555, "dur": 537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751367667931092, "dur": 1245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751367667932338, "dur": 852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751367667933190, "dur": 1048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751367667934238, "dur": 3343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751367667937581, "dur": 4427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751367667944339, "dur": 668, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@911117698220\\Editor\\Generation\\Processors\\GenerationUtils.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751367667942008, "dur": 2999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751367667946793, "dur": 550, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@911117698220\\Editor\\Data\\Nodes\\Utility\\Logic\\IsInfiniteNode.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751367667945007, "dur": 4262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751367667950802, "dur": 551, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@911117698220\\Editor\\Data\\Graphs\\Vector1ShaderProperty.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751367667952431, "dur": 727, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@911117698220\\Editor\\Data\\Graphs\\Texture2DShaderProperty.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751367667953515, "dur": 629, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@911117698220\\Editor\\Data\\Graphs\\Texture2DArrayShaderProperty.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751367667949270, "dur": 5238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751367667954508, "dur": 2078, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751367667956586, "dur": 2955, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751367667959542, "dur": 956, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751367667960498, "dur": 1387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751367667961885, "dur": 1479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751367667963833, "dur": 976, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.Core\\BoltGUI.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751367667963364, "dur": 2617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751367667966254, "dur": 836, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.spriteshape@9e35352ae135\\Runtime\\External\\LibTessDotNet\\Dict.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751367667967920, "dur": 527, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.common@bb1fc9b3d81b\\Path\\Editor\\Selection\\ISelection.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751367667965981, "dur": 2563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751367667969178, "dur": 582, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Core\\Cloning\\Cloners\\GradientCloner.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751367667968544, "dur": 2236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751367667970780, "dur": 1843, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751367667972624, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751367667972886, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_4A5B02DEDB940168.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751367667972979, "dur": 407, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751367667973395, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751367667973629, "dur": 761, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751367667974390, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751367667974478, "dur": 946, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751367667975477, "dur": 628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751367667976106, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751367667976360, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751367667976557, "dur": 484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751367667977041, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751367667977321, "dur": 1822, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751367667979143, "dur": 765, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751367667979910, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751367667980026, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751367667980436, "dur": 1802, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751367667982238, "dur": 49245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751367668031485, "dur": 2214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751367668033701, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751367668033907, "dur": 2218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751367668036126, "dur": 3095, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751367668039226, "dur": 2515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751367668041742, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751367668041813, "dur": 2414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751367668044228, "dur": 2105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751367668046339, "dur": 2425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751367668048765, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751367668048853, "dur": 1412789, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367667900777, "dur": 24813, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367667925592, "dur": 500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_7D19F4582D6FA60D.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751367667926129, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_50CFC6D0BF59D948.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751367667926289, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_F4BBF2BF2203F2D5.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751367667926386, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_F4BBF2BF2203F2D5.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751367667926478, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_4C3484A57A05DA64.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751367667926687, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_173B2236195846C7.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751367667927133, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_AEBFCF2A2C8F2407.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751367667927255, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1751367667928050, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367667928417, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11859097900959245704.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751367667928582, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367667929040, "dur": 1168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367667930209, "dur": 1131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367667931340, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367667931678, "dur": 1032, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367667932710, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367667933321, "dur": 918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367667934239, "dur": 3869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367667938108, "dur": 4084, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367667942192, "dur": 2803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367667945940, "dur": 618, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@911117698220\\Editor\\Data\\Util\\KeywordCollector.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751367667948450, "dur": 797, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@911117698220\\Editor\\Data\\Nodes\\UV\\ParallaxOcclusionMappingNode.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751367667944996, "dur": 4607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367667949603, "dur": 3290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367667952894, "dur": 3509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367667957801, "dur": 552, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.Flow\\Plugin\\Changelogs\\Changelog_1_0_4.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751367667958768, "dur": 563, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.Flow\\Plugin\\BoltFlowResources.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751367667956403, "dur": 3393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367667959796, "dur": 1732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367667961528, "dur": 1267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367667964079, "dur": 759, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.Core\\Inspection\\Special\\UnknownEditor.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751367667962795, "dur": 2937, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367667966525, "dur": 546, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@7bffd5bb179a\\Runtime\\Common\\ComponentSingleton.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751367667965733, "dur": 1338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367667967072, "dur": 821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367667967893, "dur": 1042, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367667968936, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367667969227, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367667969455, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751367667969587, "dur": 629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751367667970296, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367667970411, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751367667970774, "dur": 1357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751367667972132, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367667972203, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367667972288, "dur": 831, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751367667973120, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367667973215, "dur": 414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367667973670, "dur": 617, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751367667974357, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751367667974591, "dur": 1057, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751367667975666, "dur": 607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751367667976274, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367667976358, "dur": 644, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367667977003, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751367667977135, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751367667977446, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367667977546, "dur": 1589, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367667979140, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751367667979283, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751367667979754, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367667979872, "dur": 2365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367667982237, "dur": 49216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367668031455, "dur": 2301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751367668033757, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367668033901, "dur": 2280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751367668036181, "dur": 319, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367668036506, "dur": 2714, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751367668039220, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367668039494, "dur": 2506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/NativeFilePicker.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751367668042000, "dur": 369, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367668042374, "dur": 4651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751367668047079, "dur": 589, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367668048149, "dur": 186, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1751367668048359, "dur": 208710, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367668258546, "dur": 186, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 16, "ts": 1751367668258732, "dur": 1202, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 16, "ts": 1751367668257070, "dur": 2915, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751367668259986, "dur": 1201658, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751367669489505, "dur": 1264, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 14916, "tid": 2627, "ts": 1751367669514641, "dur": 3329, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 14916, "tid": 2627, "ts": 1751367669518129, "dur": 18394, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 14916, "tid": 2627, "ts": 1751367669505632, "dur": 32228, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}