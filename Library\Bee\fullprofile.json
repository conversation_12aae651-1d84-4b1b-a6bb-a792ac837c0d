{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 2364, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 2364, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 2364, "tid": 1554, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 2364, "tid": 1554, "ts": 1751382819744861, "dur": 11, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 2364, "tid": 1554, "ts": 1751382819744894, "dur": 8, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 2364, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 2364, "tid": 1, "ts": 1751382819372935, "dur": 2544, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 2364, "tid": 1, "ts": 1751382819375485, "dur": 93936, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 2364, "tid": 1, "ts": 1751382819469426, "dur": 155083, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 2364, "tid": 1554, "ts": 1751382819744906, "dur": 20, "ph": "X", "name": "", "args": {}}, {"pid": 2364, "tid": 73014444032, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819372892, "dur": 12617, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819385512, "dur": 358905, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819385524, "dur": 84, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819385615, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819385622, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819385662, "dur": 185, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819385850, "dur": 3181, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389038, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389042, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389086, "dur": 4, "ph": "X", "name": "ProcessMessages 1022", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389091, "dur": 70, "ph": "X", "name": "ReadAsync 1022", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389163, "dur": 1, "ph": "X", "name": "ProcessMessages 863", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389166, "dur": 97, "ph": "X", "name": "ReadAsync 863", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389269, "dur": 5, "ph": "X", "name": "ProcessMessages 1977", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389276, "dur": 49, "ph": "X", "name": "ReadAsync 1977", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389332, "dur": 4, "ph": "X", "name": "ProcessMessages 1486", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389338, "dur": 44, "ph": "X", "name": "ReadAsync 1486", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389386, "dur": 3, "ph": "X", "name": "ProcessMessages 1200", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389391, "dur": 33, "ph": "X", "name": "ReadAsync 1200", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389432, "dur": 4, "ph": "X", "name": "ProcessMessages 1074", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389438, "dur": 28, "ph": "X", "name": "ReadAsync 1074", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389468, "dur": 2, "ph": "X", "name": "ProcessMessages 1031", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389471, "dur": 24, "ph": "X", "name": "ReadAsync 1031", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389502, "dur": 3, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389508, "dur": 22, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389532, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389535, "dur": 15, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389555, "dur": 1, "ph": "X", "name": "ProcessMessages 141", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389558, "dur": 22, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389582, "dur": 2, "ph": "X", "name": "ProcessMessages 747", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389585, "dur": 23, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389611, "dur": 1, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389613, "dur": 26, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389642, "dur": 2, "ph": "X", "name": "ProcessMessages 737", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389646, "dur": 31, "ph": "X", "name": "ReadAsync 737", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389683, "dur": 3, "ph": "X", "name": "ProcessMessages 642", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389689, "dur": 34, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389725, "dur": 2, "ph": "X", "name": "ProcessMessages 1166", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389728, "dur": 21, "ph": "X", "name": "ReadAsync 1166", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389752, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389755, "dur": 31, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389790, "dur": 2, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389793, "dur": 25, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389821, "dur": 2, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389824, "dur": 25, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389852, "dur": 2, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389855, "dur": 31, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389890, "dur": 2, "ph": "X", "name": "ProcessMessages 833", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389895, "dur": 26, "ph": "X", "name": "ReadAsync 833", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389923, "dur": 2, "ph": "X", "name": "ProcessMessages 868", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389926, "dur": 19, "ph": "X", "name": "ReadAsync 868", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389950, "dur": 2, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389954, "dur": 27, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389983, "dur": 1, "ph": "X", "name": "ProcessMessages 940", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819389986, "dur": 22, "ph": "X", "name": "ReadAsync 940", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390012, "dur": 2, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390016, "dur": 16, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390034, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390036, "dur": 19, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390057, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390060, "dur": 26, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390088, "dur": 1, "ph": "X", "name": "ProcessMessages 898", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390091, "dur": 17, "ph": "X", "name": "ReadAsync 898", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390110, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390112, "dur": 19, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390133, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390136, "dur": 19, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390162, "dur": 3, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390166, "dur": 29, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390197, "dur": 2, "ph": "X", "name": "ProcessMessages 897", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390201, "dur": 18, "ph": "X", "name": "ReadAsync 897", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390221, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390224, "dur": 26, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390252, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390255, "dur": 23, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390281, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390284, "dur": 18, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390303, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390306, "dur": 17, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390325, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390327, "dur": 18, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390347, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390349, "dur": 17, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390369, "dur": 2, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390372, "dur": 18, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390392, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390394, "dur": 18, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390415, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390418, "dur": 18, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390438, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390441, "dur": 21, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390464, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390467, "dur": 22, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390492, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390509, "dur": 30, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390542, "dur": 2, "ph": "X", "name": "ProcessMessages 909", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390545, "dur": 24, "ph": "X", "name": "ReadAsync 909", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390572, "dur": 2, "ph": "X", "name": "ProcessMessages 685", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390576, "dur": 18, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390596, "dur": 1, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390598, "dur": 13, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390612, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390615, "dur": 30, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390647, "dur": 2, "ph": "X", "name": "ProcessMessages 739", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390650, "dur": 18, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390670, "dur": 1, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390674, "dur": 17, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390693, "dur": 1, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390696, "dur": 27, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390729, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390732, "dur": 23, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390757, "dur": 2, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390761, "dur": 17, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390779, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390782, "dur": 18, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390802, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390817, "dur": 25, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390844, "dur": 2, "ph": "X", "name": "ProcessMessages 1170", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390847, "dur": 17, "ph": "X", "name": "ReadAsync 1170", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390866, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390869, "dur": 36, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390907, "dur": 2, "ph": "X", "name": "ProcessMessages 928", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390911, "dur": 21, "ph": "X", "name": "ReadAsync 928", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390935, "dur": 2, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390938, "dur": 18, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390958, "dur": 1, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390960, "dur": 13, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390976, "dur": 1, "ph": "X", "name": "ProcessMessages 58", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390978, "dur": 14, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819390996, "dur": 17, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391017, "dur": 2, "ph": "X", "name": "ProcessMessages 270", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391020, "dur": 37, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391060, "dur": 2, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391063, "dur": 17, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391083, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391085, "dur": 20, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391108, "dur": 2, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391111, "dur": 18, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391131, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391133, "dur": 21, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391157, "dur": 1, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391160, "dur": 84, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391249, "dur": 4, "ph": "X", "name": "ProcessMessages 1523", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391255, "dur": 19, "ph": "X", "name": "ReadAsync 1523", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391276, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391278, "dur": 31, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391312, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391314, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391339, "dur": 2, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391342, "dur": 18, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391361, "dur": 1, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391364, "dur": 16, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391382, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391385, "dur": 17, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391404, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391409, "dur": 19, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391431, "dur": 2, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391435, "dur": 25, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391462, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391465, "dur": 47, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391516, "dur": 2, "ph": "X", "name": "ProcessMessages 1041", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391523, "dur": 19, "ph": "X", "name": "ReadAsync 1041", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391553, "dur": 1, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391556, "dur": 25, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391584, "dur": 2, "ph": "X", "name": "ProcessMessages 845", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391588, "dur": 25, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391616, "dur": 2, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391620, "dur": 20, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391641, "dur": 1, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391644, "dur": 17, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391664, "dur": 1, "ph": "X", "name": "ProcessMessages 69", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391668, "dur": 21, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391692, "dur": 1, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391695, "dur": 22, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391719, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391722, "dur": 23, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391750, "dur": 2, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391754, "dur": 27, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391784, "dur": 2, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391788, "dur": 19, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391809, "dur": 1, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391811, "dur": 16, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391830, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391833, "dur": 30, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391866, "dur": 2, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391869, "dur": 24, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391896, "dur": 2, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391900, "dur": 21, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391923, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391926, "dur": 20, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391949, "dur": 2, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391953, "dur": 24, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391980, "dur": 2, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819391983, "dur": 29, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392017, "dur": 2, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392021, "dur": 30, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392054, "dur": 2, "ph": "X", "name": "ProcessMessages 767", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392058, "dur": 21, "ph": "X", "name": "ReadAsync 767", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392081, "dur": 1, "ph": "X", "name": "ProcessMessages 728", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392107, "dur": 38, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392158, "dur": 2, "ph": "X", "name": "ProcessMessages 1104", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392162, "dur": 26, "ph": "X", "name": "ReadAsync 1104", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392190, "dur": 2, "ph": "X", "name": "ProcessMessages 1053", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392194, "dur": 14, "ph": "X", "name": "ReadAsync 1053", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392210, "dur": 1, "ph": "X", "name": "ProcessMessages 166", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392213, "dur": 24, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392239, "dur": 1, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392242, "dur": 22, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392267, "dur": 2, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392272, "dur": 29, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392307, "dur": 1, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392310, "dur": 17, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392329, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392332, "dur": 22, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392356, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392359, "dur": 21, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392383, "dur": 1, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392385, "dur": 24, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392413, "dur": 2, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392417, "dur": 23, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392443, "dur": 1, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392445, "dur": 18, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392465, "dur": 1, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392468, "dur": 17, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392488, "dur": 2, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392492, "dur": 20, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392514, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392517, "dur": 16, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392536, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392539, "dur": 22, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392564, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392567, "dur": 24, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392593, "dur": 1, "ph": "X", "name": "ProcessMessages 783", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392596, "dur": 24, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392625, "dur": 2, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392629, "dur": 20, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392651, "dur": 1, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392654, "dur": 15, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392674, "dur": 2, "ph": "X", "name": "ProcessMessages 81", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392677, "dur": 25, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392705, "dur": 1, "ph": "X", "name": "ProcessMessages 667", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392707, "dur": 19, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392729, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392732, "dur": 25, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392759, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392762, "dur": 21, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392786, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392804, "dur": 25, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392833, "dur": 2, "ph": "X", "name": "ProcessMessages 893", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392837, "dur": 23, "ph": "X", "name": "ReadAsync 893", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392863, "dur": 2, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392866, "dur": 16, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392884, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392886, "dur": 16, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392905, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392907, "dur": 14, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392923, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819392925, "dur": 78, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393008, "dur": 4, "ph": "X", "name": "ProcessMessages 1774", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393014, "dur": 20, "ph": "X", "name": "ReadAsync 1774", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393036, "dur": 1, "ph": "X", "name": "ProcessMessages 829", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393039, "dur": 18, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393060, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393062, "dur": 25, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393090, "dur": 1, "ph": "X", "name": "ProcessMessages 491", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393093, "dur": 24, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393120, "dur": 2, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393124, "dur": 22, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393147, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393150, "dur": 17, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393171, "dur": 2, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393174, "dur": 30, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393209, "dur": 1, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393212, "dur": 25, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393238, "dur": 1, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393241, "dur": 15, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393259, "dur": 1, "ph": "X", "name": "ProcessMessages 74", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393262, "dur": 19, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393283, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393286, "dur": 22, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393310, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393313, "dur": 40, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393355, "dur": 2, "ph": "X", "name": "ProcessMessages 1114", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393359, "dur": 26, "ph": "X", "name": "ReadAsync 1114", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393388, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393391, "dur": 19, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393412, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393414, "dur": 20, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393438, "dur": 2, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393441, "dur": 18, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393470, "dur": 2, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393473, "dur": 26, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393504, "dur": 2, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393508, "dur": 112, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393624, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393627, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393653, "dur": 2, "ph": "X", "name": "ProcessMessages 995", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393656, "dur": 22, "ph": "X", "name": "ReadAsync 995", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393681, "dur": 1, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393698, "dur": 32, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393734, "dur": 2, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393737, "dur": 24, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393763, "dur": 1, "ph": "X", "name": "ProcessMessages 1044", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393766, "dur": 16, "ph": "X", "name": "ReadAsync 1044", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393787, "dur": 2, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393791, "dur": 24, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393816, "dur": 1, "ph": "X", "name": "ProcessMessages 844", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393825, "dur": 15, "ph": "X", "name": "ReadAsync 844", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393843, "dur": 1, "ph": "X", "name": "ProcessMessages 81", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393845, "dur": 20, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393868, "dur": 1, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393871, "dur": 27, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393901, "dur": 2, "ph": "X", "name": "ProcessMessages 734", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393905, "dur": 21, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393928, "dur": 2, "ph": "X", "name": "ProcessMessages 687", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393931, "dur": 15, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393949, "dur": 1, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393952, "dur": 18, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819393974, "dur": 24, "ph": "X", "name": "ReadAsync 126", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394000, "dur": 1, "ph": "X", "name": "ProcessMessages 702", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394003, "dur": 34, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394039, "dur": 2, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394042, "dur": 20, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394066, "dur": 2, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394070, "dur": 20, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394092, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394094, "dur": 16, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394112, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394114, "dur": 23, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394140, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394142, "dur": 21, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394167, "dur": 2, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394171, "dur": 21, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394195, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394198, "dur": 22, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394223, "dur": 1, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394227, "dur": 32, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394261, "dur": 2, "ph": "X", "name": "ProcessMessages 906", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394264, "dur": 32, "ph": "X", "name": "ReadAsync 906", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394298, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394301, "dur": 20, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394325, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394327, "dur": 18, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394349, "dur": 2, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394352, "dur": 26, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394382, "dur": 2, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394386, "dur": 25, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394417, "dur": 2, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394421, "dur": 18, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394441, "dur": 1, "ph": "X", "name": "ProcessMessages 727", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394444, "dur": 17, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394463, "dur": 1, "ph": "X", "name": "ProcessMessages 414", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394466, "dur": 16, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394483, "dur": 1, "ph": "X", "name": "ProcessMessages 579", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394486, "dur": 16, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394506, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394509, "dur": 21, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394532, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394535, "dur": 23, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394563, "dur": 2, "ph": "X", "name": "ProcessMessages 763", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394567, "dur": 19, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394587, "dur": 1, "ph": "X", "name": "ProcessMessages 716", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394590, "dur": 31, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394624, "dur": 1, "ph": "X", "name": "ProcessMessages 667", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394627, "dur": 22, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394651, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394653, "dur": 27, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394686, "dur": 3, "ph": "X", "name": "ProcessMessages 745", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394691, "dur": 26, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394720, "dur": 2, "ph": "X", "name": "ProcessMessages 1020", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394723, "dur": 22, "ph": "X", "name": "ReadAsync 1020", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394750, "dur": 2, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394753, "dur": 32, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394789, "dur": 2, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394792, "dur": 22, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394816, "dur": 1, "ph": "X", "name": "ProcessMessages 1169", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394819, "dur": 21, "ph": "X", "name": "ReadAsync 1169", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394842, "dur": 1, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394845, "dur": 20, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394867, "dur": 1, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394870, "dur": 21, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394894, "dur": 2, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394898, "dur": 32, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394932, "dur": 2, "ph": "X", "name": "ProcessMessages 1034", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394935, "dur": 20, "ph": "X", "name": "ReadAsync 1034", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394978, "dur": 2, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819394982, "dur": 29, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395014, "dur": 2, "ph": "X", "name": "ProcessMessages 829", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395017, "dur": 22, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395043, "dur": 2, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395046, "dur": 21, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395069, "dur": 1, "ph": "X", "name": "ProcessMessages 703", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395072, "dur": 15, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395090, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395092, "dur": 24, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395120, "dur": 2, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395124, "dur": 27, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395153, "dur": 1, "ph": "X", "name": "ProcessMessages 425", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395156, "dur": 27, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395185, "dur": 1, "ph": "X", "name": "ProcessMessages 785", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395188, "dur": 21, "ph": "X", "name": "ReadAsync 785", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395211, "dur": 2, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395215, "dur": 23, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395240, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395242, "dur": 22, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395269, "dur": 2, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395272, "dur": 27, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395302, "dur": 2, "ph": "X", "name": "ProcessMessages 967", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395306, "dur": 21, "ph": "X", "name": "ReadAsync 967", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395329, "dur": 1, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395332, "dur": 13, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395347, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395349, "dur": 17, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395370, "dur": 1, "ph": "X", "name": "ProcessMessages 264", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395372, "dur": 28, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395403, "dur": 2, "ph": "X", "name": "ProcessMessages 767", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395407, "dur": 24, "ph": "X", "name": "ReadAsync 767", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395433, "dur": 2, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395436, "dur": 19, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395458, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395461, "dur": 13, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395475, "dur": 2, "ph": "X", "name": "ProcessMessages 186", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395479, "dur": 15, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395498, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395500, "dur": 20, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395523, "dur": 2, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395527, "dur": 18, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395546, "dur": 2, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395549, "dur": 18, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395571, "dur": 1, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395574, "dur": 21, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395596, "dur": 1, "ph": "X", "name": "ProcessMessages 191", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395601, "dur": 22, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395627, "dur": 2, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395630, "dur": 20, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395652, "dur": 1, "ph": "X", "name": "ProcessMessages 687", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395655, "dur": 17, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395674, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395677, "dur": 20, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395700, "dur": 1, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395703, "dur": 19, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395726, "dur": 2, "ph": "X", "name": "ProcessMessages 69", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395729, "dur": 28, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395759, "dur": 1, "ph": "X", "name": "ProcessMessages 998", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395762, "dur": 20, "ph": "X", "name": "ReadAsync 998", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395784, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395786, "dur": 19, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395809, "dur": 2, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395812, "dur": 18, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395832, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395835, "dur": 22, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395860, "dur": 1, "ph": "X", "name": "ProcessMessages 703", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395864, "dur": 19, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395885, "dur": 1, "ph": "X", "name": "ProcessMessages 783", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395888, "dur": 18, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395909, "dur": 1, "ph": "X", "name": "ProcessMessages 174", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395911, "dur": 16, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395929, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395932, "dur": 15, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395950, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395953, "dur": 17, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395972, "dur": 1, "ph": "X", "name": "ProcessMessages 685", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395975, "dur": 18, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395995, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819395997, "dur": 23, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396023, "dur": 2, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396026, "dur": 18, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396047, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396050, "dur": 19, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396071, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396190, "dur": 40, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396232, "dur": 6, "ph": "X", "name": "ProcessMessages 3161", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396239, "dur": 16, "ph": "X", "name": "ReadAsync 3161", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396258, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396261, "dur": 22, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396287, "dur": 22, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396311, "dur": 2, "ph": "X", "name": "ProcessMessages 692", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396315, "dur": 21, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396338, "dur": 2, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396342, "dur": 16, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396360, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396362, "dur": 13, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396380, "dur": 22, "ph": "X", "name": "ReadAsync 152", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396404, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396407, "dur": 22, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396432, "dur": 2, "ph": "X", "name": "ProcessMessages 704", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396436, "dur": 23, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396462, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396465, "dur": 19, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396486, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396488, "dur": 22, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396513, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396516, "dur": 25, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396543, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396546, "dur": 14, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396564, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396579, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396581, "dur": 17, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396601, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396603, "dur": 17, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396622, "dur": 1, "ph": "X", "name": "ProcessMessages 175", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396625, "dur": 23, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396650, "dur": 1, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396653, "dur": 23, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396678, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396681, "dur": 19, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396703, "dur": 1, "ph": "X", "name": "ProcessMessages 398", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396706, "dur": 17, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396725, "dur": 1, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396728, "dur": 13, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396743, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396746, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396769, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396772, "dur": 77, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396853, "dur": 2, "ph": "X", "name": "ProcessMessages 684", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396856, "dur": 31, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396890, "dur": 3, "ph": "X", "name": "ProcessMessages 1689", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396895, "dur": 21, "ph": "X", "name": "ReadAsync 1689", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396919, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396922, "dur": 18, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396942, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396945, "dur": 20, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396968, "dur": 1, "ph": "X", "name": "ProcessMessages 178", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396970, "dur": 17, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396988, "dur": 1, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819396991, "dur": 24, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397017, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397020, "dur": 22, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397046, "dur": 2, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397050, "dur": 26, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397080, "dur": 2, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397083, "dur": 28, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397115, "dur": 2, "ph": "X", "name": "ProcessMessages 1107", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397119, "dur": 26, "ph": "X", "name": "ReadAsync 1107", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397147, "dur": 2, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397151, "dur": 24, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397178, "dur": 2, "ph": "X", "name": "ProcessMessages 662", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397182, "dur": 25, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397210, "dur": 1, "ph": "X", "name": "ProcessMessages 679", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397213, "dur": 23, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397238, "dur": 1, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397241, "dur": 22, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397267, "dur": 2, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397271, "dur": 27, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397300, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397302, "dur": 23, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397327, "dur": 1, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397329, "dur": 21, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397354, "dur": 2, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397358, "dur": 27, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397389, "dur": 2, "ph": "X", "name": "ProcessMessages 790", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397393, "dur": 16, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397411, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397414, "dur": 26, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397444, "dur": 2, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397459, "dur": 29, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397490, "dur": 2, "ph": "X", "name": "ProcessMessages 852", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397494, "dur": 24, "ph": "X", "name": "ReadAsync 852", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397520, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397523, "dur": 21, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397549, "dur": 2, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397553, "dur": 26, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397580, "dur": 2, "ph": "X", "name": "ProcessMessages 939", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397584, "dur": 24, "ph": "X", "name": "ReadAsync 939", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397612, "dur": 2, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397617, "dur": 18, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397649, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397652, "dur": 30, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397688, "dur": 2, "ph": "X", "name": "ProcessMessages 836", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397692, "dur": 24, "ph": "X", "name": "ReadAsync 836", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397718, "dur": 2, "ph": "X", "name": "ProcessMessages 835", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397721, "dur": 18, "ph": "X", "name": "ReadAsync 835", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397743, "dur": 2, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397746, "dur": 21, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397769, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397771, "dur": 23, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397797, "dur": 2, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397800, "dur": 22, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397824, "dur": 1, "ph": "X", "name": "ProcessMessages 807", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397827, "dur": 19, "ph": "X", "name": "ReadAsync 807", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397849, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397851, "dur": 31, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397885, "dur": 1, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397887, "dur": 37, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397928, "dur": 2, "ph": "X", "name": "ProcessMessages 890", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397932, "dur": 20, "ph": "X", "name": "ReadAsync 890", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397954, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397956, "dur": 19, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397979, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819397982, "dur": 18, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398004, "dur": 1, "ph": "X", "name": "ProcessMessages 247", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398006, "dur": 29, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398039, "dur": 2, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398043, "dur": 24, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398070, "dur": 2, "ph": "X", "name": "ProcessMessages 658", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398073, "dur": 26, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398101, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398104, "dur": 20, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398127, "dur": 2, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398130, "dur": 28, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398160, "dur": 1, "ph": "X", "name": "ProcessMessages 798", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398163, "dur": 23, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398190, "dur": 2, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398194, "dur": 42, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398240, "dur": 2, "ph": "X", "name": "ProcessMessages 1146", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398244, "dur": 78, "ph": "X", "name": "ReadAsync 1146", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398327, "dur": 3, "ph": "X", "name": "ProcessMessages 750", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398332, "dur": 39, "ph": "X", "name": "ReadAsync 750", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398374, "dur": 3, "ph": "X", "name": "ProcessMessages 1838", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398378, "dur": 22, "ph": "X", "name": "ReadAsync 1838", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398403, "dur": 2, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398410, "dur": 42, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398455, "dur": 2, "ph": "X", "name": "ProcessMessages 1072", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398458, "dur": 84, "ph": "X", "name": "ReadAsync 1072", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398547, "dur": 2, "ph": "X", "name": "ProcessMessages 652", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398551, "dur": 32, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398586, "dur": 3, "ph": "X", "name": "ProcessMessages 1509", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398591, "dur": 26, "ph": "X", "name": "ReadAsync 1509", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398619, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398622, "dur": 70, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398697, "dur": 3, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398702, "dur": 44, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398753, "dur": 3, "ph": "X", "name": "ProcessMessages 1887", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398758, "dur": 21, "ph": "X", "name": "ReadAsync 1887", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398781, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398784, "dur": 18, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398807, "dur": 2, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398813, "dur": 25, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398840, "dur": 2, "ph": "X", "name": "ProcessMessages 1219", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398843, "dur": 23, "ph": "X", "name": "ReadAsync 1219", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398871, "dur": 2, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398875, "dur": 61, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398939, "dur": 4, "ph": "X", "name": "ProcessMessages 1335", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398945, "dur": 24, "ph": "X", "name": "ReadAsync 1335", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398971, "dur": 2, "ph": "X", "name": "ProcessMessages 1074", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398975, "dur": 18, "ph": "X", "name": "ReadAsync 1074", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819398998, "dur": 2, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399003, "dur": 66, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399073, "dur": 2, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399077, "dur": 43, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399123, "dur": 3, "ph": "X", "name": "ProcessMessages 2121", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399127, "dur": 27, "ph": "X", "name": "ReadAsync 2121", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399159, "dur": 2, "ph": "X", "name": "ProcessMessages 85", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399163, "dur": 38, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399203, "dur": 2, "ph": "X", "name": "ProcessMessages 1111", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399206, "dur": 26, "ph": "X", "name": "ReadAsync 1111", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399237, "dur": 2, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399241, "dur": 25, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399270, "dur": 2, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399274, "dur": 28, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399321, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399326, "dur": 38, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399368, "dur": 2, "ph": "X", "name": "ProcessMessages 1242", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399372, "dur": 26, "ph": "X", "name": "ReadAsync 1242", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399401, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399404, "dur": 34, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399443, "dur": 2, "ph": "X", "name": "ProcessMessages 1140", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399447, "dur": 22, "ph": "X", "name": "ReadAsync 1140", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399471, "dur": 2, "ph": "X", "name": "ProcessMessages 685", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399475, "dur": 30, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399509, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399512, "dur": 26, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399549, "dur": 1, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399552, "dur": 29, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399585, "dur": 2, "ph": "X", "name": "ProcessMessages 919", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399589, "dur": 30, "ph": "X", "name": "ReadAsync 919", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399624, "dur": 2, "ph": "X", "name": "ProcessMessages 784", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399628, "dur": 17, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399647, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399650, "dur": 38, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399692, "dur": 2, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399697, "dur": 22, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399721, "dur": 2, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399724, "dur": 19, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399747, "dur": 2, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399751, "dur": 31, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399787, "dur": 2, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399791, "dur": 33, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399827, "dur": 2, "ph": "X", "name": "ProcessMessages 889", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399830, "dur": 32, "ph": "X", "name": "ReadAsync 889", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399867, "dur": 3, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399872, "dur": 32, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399909, "dur": 3, "ph": "X", "name": "ProcessMessages 917", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399913, "dur": 34, "ph": "X", "name": "ReadAsync 917", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399949, "dur": 2, "ph": "X", "name": "ProcessMessages 935", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399953, "dur": 20, "ph": "X", "name": "ReadAsync 935", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399976, "dur": 1, "ph": "X", "name": "ProcessMessages 438", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819399979, "dur": 24, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400008, "dur": 3, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400013, "dur": 21, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400036, "dur": 1, "ph": "X", "name": "ProcessMessages 690", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400039, "dur": 18, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400059, "dur": 1, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400062, "dur": 21, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400085, "dur": 2, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400089, "dur": 15, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400106, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400108, "dur": 19, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400130, "dur": 1, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400132, "dur": 16, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400151, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400154, "dur": 17, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400173, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400176, "dur": 37, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400216, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400219, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400241, "dur": 2, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400244, "dur": 14, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400263, "dur": 1, "ph": "X", "name": "ProcessMessages 223", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400266, "dur": 23, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400292, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400315, "dur": 2, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400319, "dur": 57, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400380, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400383, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400403, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400406, "dur": 17, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400427, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400430, "dur": 17, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400449, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400452, "dur": 20, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400474, "dur": 1, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400476, "dur": 19, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400499, "dur": 2, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400503, "dur": 14, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400519, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400522, "dur": 16, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400540, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400544, "dur": 52, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400600, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400623, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400626, "dur": 21, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400649, "dur": 1, "ph": "X", "name": "ProcessMessages 711", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400652, "dur": 17, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400672, "dur": 1, "ph": "X", "name": "ProcessMessages 247", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400675, "dur": 22, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400700, "dur": 2, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400703, "dur": 18, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400723, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400725, "dur": 14, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400741, "dur": 1, "ph": "X", "name": "ProcessMessages 82", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400744, "dur": 20, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400766, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400769, "dur": 49, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400821, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400824, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400843, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400846, "dur": 25, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400873, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400875, "dur": 13, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400891, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400894, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400945, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400948, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400968, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400971, "dur": 22, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400995, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819400998, "dur": 53, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401056, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401088, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401091, "dur": 16, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401109, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401111, "dur": 16, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401129, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401133, "dur": 54, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401191, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401213, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401216, "dur": 21, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401240, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401243, "dur": 22, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401274, "dur": 1, "ph": "X", "name": "ProcessMessages 141", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401277, "dur": 29, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401309, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401311, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401333, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401335, "dur": 25, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401363, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401366, "dur": 12, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401380, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401382, "dur": 47, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401434, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401436, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401465, "dur": 2, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401469, "dur": 24, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401497, "dur": 2, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401501, "dur": 41, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401546, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401548, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401600, "dur": 2, "ph": "X", "name": "ProcessMessages 760", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401603, "dur": 18, "ph": "X", "name": "ReadAsync 760", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401625, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401631, "dur": 32, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401665, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401668, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401691, "dur": 2, "ph": "X", "name": "ProcessMessages 874", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401695, "dur": 16, "ph": "X", "name": "ReadAsync 874", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401714, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401717, "dur": 59, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401779, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401781, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401812, "dur": 2, "ph": "X", "name": "ProcessMessages 918", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401816, "dur": 15, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401834, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401837, "dur": 46, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401886, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401888, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401914, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401928, "dur": 19, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401950, "dur": 1, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401953, "dur": 35, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819401992, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402020, "dur": 2, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402024, "dur": 21, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402047, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402050, "dur": 45, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402100, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402103, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402129, "dur": 2, "ph": "X", "name": "ProcessMessages 835", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402132, "dur": 16, "ph": "X", "name": "ReadAsync 835", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402150, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402152, "dur": 48, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402204, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402227, "dur": 2, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402231, "dur": 20, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402253, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402262, "dur": 41, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402309, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402337, "dur": 1, "ph": "X", "name": "ProcessMessages 806", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402340, "dur": 23, "ph": "X", "name": "ReadAsync 806", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402365, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402368, "dur": 13, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402383, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402386, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402429, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402448, "dur": 2, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402451, "dur": 23, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402477, "dur": 1, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402479, "dur": 20, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402502, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402505, "dur": 36, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402543, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402545, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402563, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402566, "dur": 18, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402588, "dur": 2, "ph": "X", "name": "ProcessMessages 414", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402592, "dur": 22, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402616, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402619, "dur": 32, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402655, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402680, "dur": 2, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402683, "dur": 22, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402707, "dur": 1, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402710, "dur": 48, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402763, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402790, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402793, "dur": 22, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402817, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402820, "dur": 49, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402875, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402878, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402901, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402904, "dur": 20, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402927, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402930, "dur": 16, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402949, "dur": 1, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402952, "dur": 37, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402992, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819402994, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403013, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403016, "dur": 18, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403036, "dur": 1, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403038, "dur": 14, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403054, "dur": 1, "ph": "X", "name": "ProcessMessages 138", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403057, "dur": 44, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403105, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403143, "dur": 2, "ph": "X", "name": "ProcessMessages 739", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403146, "dur": 14, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403162, "dur": 1, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403171, "dur": 41, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403216, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403221, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403241, "dur": 1, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403244, "dur": 14, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403260, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403262, "dur": 17, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403282, "dur": 1, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403288, "dur": 32, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403329, "dur": 2, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403332, "dur": 21, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403355, "dur": 3, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403359, "dur": 13, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403375, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403377, "dur": 46, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403426, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403429, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403463, "dur": 2, "ph": "X", "name": "ProcessMessages 714", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403467, "dur": 16, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403485, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403487, "dur": 47, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403540, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403562, "dur": 1, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403566, "dur": 27, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403595, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403598, "dur": 25, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403625, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403628, "dur": 22, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403652, "dur": 1, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403655, "dur": 16, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403672, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403675, "dur": 15, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403693, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403701, "dur": 48, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403751, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403753, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403787, "dur": 1, "ph": "X", "name": "ProcessMessages 690", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403790, "dur": 19, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403811, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403814, "dur": 43, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403859, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403866, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403896, "dur": 2, "ph": "X", "name": "ProcessMessages 815", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403899, "dur": 17, "ph": "X", "name": "ReadAsync 815", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403918, "dur": 1, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403921, "dur": 43, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403967, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403969, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403991, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819403994, "dur": 23, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404020, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404022, "dur": 43, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404070, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404091, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404094, "dur": 32, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404133, "dur": 2, "ph": "X", "name": "ProcessMessages 802", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404136, "dur": 15, "ph": "X", "name": "ReadAsync 802", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404154, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404157, "dur": 47, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404207, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404209, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404230, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404233, "dur": 21, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404256, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404259, "dur": 24, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404285, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404288, "dur": 22, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404313, "dur": 2, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404317, "dur": 19, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404339, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404342, "dur": 21, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404366, "dur": 1, "ph": "X", "name": "ProcessMessages 234", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404368, "dur": 40, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404412, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404436, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404439, "dur": 16, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404457, "dur": 1, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404473, "dur": 16, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404491, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404493, "dur": 36, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404532, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404535, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404557, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404560, "dur": 16, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404579, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404581, "dur": 21, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404604, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404607, "dur": 23, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404634, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404637, "dur": 23, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404662, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404665, "dur": 17, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404685, "dur": 1, "ph": "X", "name": "ProcessMessages 115", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404687, "dur": 12, "ph": "X", "name": "ReadAsync 115", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404701, "dur": 1, "ph": "X", "name": "ProcessMessages 145", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404704, "dur": 46, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404753, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404755, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404776, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404779, "dur": 18, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404800, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404802, "dur": 52, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404859, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404880, "dur": 1, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404882, "dur": 16, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404901, "dur": 1, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404904, "dur": 22, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404929, "dur": 1, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404935, "dur": 18, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404955, "dur": 1, "ph": "X", "name": "ProcessMessages 709", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404958, "dur": 19, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404979, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819404982, "dur": 19, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405004, "dur": 6, "ph": "X", "name": "ProcessMessages 83", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405011, "dur": 13, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405026, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405028, "dur": 36, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405069, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405105, "dur": 2, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405109, "dur": 19, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405130, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405133, "dur": 39, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405176, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405202, "dur": 2, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405205, "dur": 22, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405229, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405231, "dur": 48, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405284, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405314, "dur": 1, "ph": "X", "name": "ProcessMessages 805", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405318, "dur": 16, "ph": "X", "name": "ReadAsync 805", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405336, "dur": 1, "ph": "X", "name": "ProcessMessages 234", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405453, "dur": 38, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405494, "dur": 4, "ph": "X", "name": "ProcessMessages 2599", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405500, "dur": 16, "ph": "X", "name": "ReadAsync 2599", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405519, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405522, "dur": 17, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405541, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405544, "dur": 42, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405590, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405627, "dur": 2, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405632, "dur": 25, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405660, "dur": 2, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405663, "dur": 29, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405695, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405701, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405747, "dur": 2, "ph": "X", "name": "ProcessMessages 1068", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405751, "dur": 35, "ph": "X", "name": "ReadAsync 1068", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405790, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405816, "dur": 2, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405819, "dur": 18, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405839, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405842, "dur": 46, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405892, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405922, "dur": 2, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405926, "dur": 25, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405953, "dur": 1, "ph": "X", "name": "ProcessMessages 611", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405956, "dur": 38, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819405998, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406024, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406027, "dur": 21, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406050, "dur": 1, "ph": "X", "name": "ProcessMessages 832", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406053, "dur": 17, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406073, "dur": 1, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406076, "dur": 18, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406097, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406100, "dur": 16, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406118, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406121, "dur": 13, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406136, "dur": 1, "ph": "X", "name": "ProcessMessages 58", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406139, "dur": 19, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406161, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406164, "dur": 52, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406219, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406221, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406251, "dur": 2, "ph": "X", "name": "ProcessMessages 790", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406255, "dur": 18, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406275, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406278, "dur": 48, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406330, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406353, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406355, "dur": 23, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406381, "dur": 2, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406386, "dur": 43, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406433, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406457, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406460, "dur": 24, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406487, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406490, "dur": 23, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406515, "dur": 2, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406519, "dur": 45, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406568, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406592, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406596, "dur": 20, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406619, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406622, "dur": 14, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406637, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406639, "dur": 60, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406703, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406706, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406726, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406729, "dur": 16, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406748, "dur": 2, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406753, "dur": 17, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406771, "dur": 1, "ph": "X", "name": "ProcessMessages 200", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406773, "dur": 41, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406819, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406847, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406850, "dur": 20, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406873, "dur": 2, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406877, "dur": 42, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406921, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406924, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406945, "dur": 2, "ph": "X", "name": "ProcessMessages 642", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406948, "dur": 16, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406966, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406969, "dur": 16, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406988, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819406990, "dur": 47, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407040, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407042, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407061, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407064, "dur": 20, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407087, "dur": 1, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407090, "dur": 19, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407111, "dur": 1, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407113, "dur": 21, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407137, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407140, "dur": 16, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407158, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407161, "dur": 17, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407180, "dur": 1, "ph": "X", "name": "ProcessMessages 75", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407183, "dur": 30, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407216, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407220, "dur": 34, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407258, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407277, "dur": 2, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407280, "dur": 19, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407302, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407304, "dur": 16, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407324, "dur": 1, "ph": "X", "name": "ProcessMessages 136", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407327, "dur": 46, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407377, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407406, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407409, "dur": 23, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407434, "dur": 1, "ph": "X", "name": "ProcessMessages 641", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407436, "dur": 16, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407455, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407458, "dur": 36, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407497, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407521, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407524, "dur": 20, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407551, "dur": 1, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407554, "dur": 42, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407604, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407623, "dur": 1, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407626, "dur": 25, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407653, "dur": 1, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407656, "dur": 47, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407708, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407732, "dur": 2, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407735, "dur": 21, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407759, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407762, "dur": 12, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407776, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407778, "dur": 44, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407825, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407827, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407848, "dur": 2, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407851, "dur": 19, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407872, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407875, "dur": 19, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407896, "dur": 1, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407899, "dur": 39, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407942, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407965, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407968, "dur": 20, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407991, "dur": 2, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819407994, "dur": 44, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408042, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408059, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408062, "dur": 20, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408085, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408087, "dur": 16, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408106, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408108, "dur": 36, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408148, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408171, "dur": 2, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408174, "dur": 23, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408200, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408203, "dur": 40, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408248, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408272, "dur": 2, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408276, "dur": 19, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408297, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408299, "dur": 46, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408347, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408350, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408374, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408377, "dur": 21, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408401, "dur": 2, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408405, "dur": 39, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408446, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408448, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408475, "dur": 2, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408478, "dur": 41, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408523, "dur": 2, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408527, "dur": 32, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408561, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408563, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408589, "dur": 1, "ph": "X", "name": "ProcessMessages 670", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408597, "dur": 23, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408622, "dur": 2, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408629, "dur": 23, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408656, "dur": 2, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408659, "dur": 22, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408683, "dur": 2, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408687, "dur": 16, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408706, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408709, "dur": 51, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408763, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408765, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408795, "dur": 2, "ph": "X", "name": "ProcessMessages 807", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408798, "dur": 17, "ph": "X", "name": "ReadAsync 807", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408817, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408820, "dur": 47, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408870, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408892, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408895, "dur": 20, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408918, "dur": 2, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408921, "dur": 47, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408971, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408973, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408994, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819408997, "dur": 16, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409015, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409017, "dur": 18, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409037, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409039, "dur": 44, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409088, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409106, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409109, "dur": 19, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409132, "dur": 1, "ph": "X", "name": "ProcessMessages 579", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409135, "dur": 18, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409156, "dur": 1, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409158, "dur": 36, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409199, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409219, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409222, "dur": 19, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409244, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409248, "dur": 24, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409274, "dur": 2, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409278, "dur": 23, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409303, "dur": 1, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409306, "dur": 19, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409327, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409330, "dur": 17, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409349, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409352, "dur": 55, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409409, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409411, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409433, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409436, "dur": 31, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409469, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409472, "dur": 16, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409490, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409493, "dur": 30, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409526, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409529, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409554, "dur": 2, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409558, "dur": 21, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409582, "dur": 2, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409585, "dur": 43, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409632, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409656, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409659, "dur": 21, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409683, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409686, "dur": 15, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409703, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409706, "dur": 43, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409752, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409754, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409778, "dur": 2, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409781, "dur": 25, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409808, "dur": 1, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409813, "dur": 49, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409864, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409866, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409885, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409894, "dur": 23, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409919, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409922, "dur": 47, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409973, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409996, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819409999, "dur": 23, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410025, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410036, "dur": 16, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410055, "dur": 1, "ph": "X", "name": "ProcessMessages 58", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410058, "dur": 25, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410088, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410125, "dur": 3, "ph": "X", "name": "ProcessMessages 841", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410129, "dur": 17, "ph": "X", "name": "ReadAsync 841", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410148, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410150, "dur": 46, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410201, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410234, "dur": 2, "ph": "X", "name": "ProcessMessages 933", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410237, "dur": 17, "ph": "X", "name": "ReadAsync 933", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410257, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410260, "dur": 49, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410313, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410340, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410343, "dur": 22, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410367, "dur": 2, "ph": "X", "name": "ProcessMessages 662", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410371, "dur": 22, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410395, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410398, "dur": 47, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410447, "dur": 2, "ph": "X", "name": "ProcessMessages 847", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410450, "dur": 17, "ph": "X", "name": "ReadAsync 847", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410470, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410473, "dur": 37, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410514, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410538, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410541, "dur": 21, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410565, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410568, "dur": 43, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410615, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410638, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410641, "dur": 21, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410664, "dur": 1, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410667, "dur": 23, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410693, "dur": 2, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410696, "dur": 21, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410719, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410723, "dur": 21, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410746, "dur": 2, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410750, "dur": 14, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410768, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410771, "dur": 49, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410822, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410825, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410857, "dur": 2, "ph": "X", "name": "ProcessMessages 790", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410860, "dur": 17, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410880, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410882, "dur": 21, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410907, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410910, "dur": 31, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410944, "dur": 2, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410948, "dur": 16, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410975, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410979, "dur": 16, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410996, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819410998, "dur": 38, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819411040, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819411042, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819411062, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819411066, "dur": 23, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819411091, "dur": 1, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819411093, "dur": 15, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819411110, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819411114, "dur": 20, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819411135, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819411138, "dur": 24, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819411165, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819411167, "dur": 17, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819411187, "dur": 1, "ph": "X", "name": "ProcessMessages 113", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819411190, "dur": 16, "ph": "X", "name": "ReadAsync 113", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819411208, "dur": 1, "ph": "X", "name": "ProcessMessages 193", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819411210, "dur": 38, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819411251, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819411253, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819411269, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819411271, "dur": 99, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819411376, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819411380, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819411415, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819411419, "dur": 293, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819411718, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819411723, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819411769, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819411773, "dur": 31, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819411809, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819411814, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819411854, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819411862, "dur": 43, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819411910, "dur": 5, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819411918, "dur": 33, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819411957, "dur": 5, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819411965, "dur": 30, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412000, "dur": 5, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412008, "dur": 33, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412045, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412050, "dur": 15, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412068, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412071, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412101, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412107, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412136, "dur": 4, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412142, "dur": 34, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412183, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412202, "dur": 38, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412245, "dur": 5, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412253, "dur": 39, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412297, "dur": 5, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412305, "dur": 35, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412343, "dur": 3, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412348, "dur": 25, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412376, "dur": 4, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412382, "dur": 22, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412406, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412411, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412434, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412439, "dur": 27, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412472, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412478, "dur": 48, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412530, "dur": 6, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412539, "dur": 28, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412572, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412579, "dur": 27, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412609, "dur": 4, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412626, "dur": 39, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412669, "dur": 5, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412676, "dur": 40, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412722, "dur": 6, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412731, "dur": 37, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412775, "dur": 7, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412785, "dur": 36, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412843, "dur": 7, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412856, "dur": 42, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412919, "dur": 8, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412930, "dur": 43, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412978, "dur": 9, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819412990, "dur": 62, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819413057, "dur": 8, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819413068, "dur": 28, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819413106, "dur": 4, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819413111, "dur": 30, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819413146, "dur": 4, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819413153, "dur": 40, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819413198, "dur": 6, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819413207, "dur": 30, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819413240, "dur": 6, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819413249, "dur": 33, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819413287, "dur": 4, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819413294, "dur": 36, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819413332, "dur": 3, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819413337, "dur": 25, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819413366, "dur": 2, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819413370, "dur": 43, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819413418, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819413422, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819413464, "dur": 4, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819413470, "dur": 39, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819413513, "dur": 6, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819413521, "dur": 36, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819413561, "dur": 4, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819413567, "dur": 32, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819413604, "dur": 5, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819413612, "dur": 26, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819413641, "dur": 4, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819413648, "dur": 33, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819413686, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819413691, "dur": 32, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819413726, "dur": 4, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819413733, "dur": 37, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819413775, "dur": 5, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819413785, "dur": 36, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819413825, "dur": 5, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819413833, "dur": 31, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819413880, "dur": 4, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819413887, "dur": 22, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819413913, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819413917, "dur": 4823, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819418752, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819418760, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819418792, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819418796, "dur": 196, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819419009, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819419014, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819419045, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819419050, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819419079, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819419084, "dur": 165, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819419254, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819419259, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819419279, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819419282, "dur": 2498, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819421787, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819421795, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819421817, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819421820, "dur": 68, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819421894, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819421897, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819421914, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819421916, "dur": 274, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819422197, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819422201, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819422257, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819422262, "dur": 154, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819422422, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819422428, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819422454, "dur": 3, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819422459, "dur": 22, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819422489, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819422494, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819422521, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819422525, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819422557, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819422585, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819422590, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819422611, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819422613, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819422633, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819422636, "dur": 97, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819422737, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819422741, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819422760, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819422763, "dur": 237, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423003, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423007, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423024, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423027, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423047, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423050, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423066, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423070, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423106, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423109, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423126, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423129, "dur": 88, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423220, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423223, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423244, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423248, "dur": 15, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423265, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423268, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423285, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423288, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423312, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423317, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423340, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423343, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423358, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423360, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423433, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423437, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423453, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423457, "dur": 12, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423471, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423473, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423493, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423497, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423538, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423543, "dur": 99, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423647, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423651, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423680, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423683, "dur": 14, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423705, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423720, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423735, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423738, "dur": 82, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423824, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423828, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423851, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423855, "dur": 30, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423891, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423895, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423913, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423916, "dur": 60, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423980, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819423984, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424001, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424005, "dur": 68, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424094, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424098, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424133, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424137, "dur": 44, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424187, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424191, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424209, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424212, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424246, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424249, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424288, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424292, "dur": 94, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424391, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424394, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424411, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424414, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424436, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424440, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424473, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424478, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424520, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424524, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424540, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424543, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424562, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424566, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424587, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424590, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424626, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424640, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424678, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424682, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424714, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424719, "dur": 188, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424910, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424913, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424929, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424932, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424948, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424951, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424974, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424978, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819424998, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425001, "dur": 56, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425059, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425069, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425089, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425093, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425109, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425112, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425129, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425133, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425150, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425152, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425169, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425172, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425192, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425196, "dur": 82, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425284, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425287, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425323, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425326, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425349, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425353, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425398, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425401, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425425, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425429, "dur": 19, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425451, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425456, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425477, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425480, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425501, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425505, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425559, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425562, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425583, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425586, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425606, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425610, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425636, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425640, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425663, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425667, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425688, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425691, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425717, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425720, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425784, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425788, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425810, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425814, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425841, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425844, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425867, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425870, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425895, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425899, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425921, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425925, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819425945, "dur": 84, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426032, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426035, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426060, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426064, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426084, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426087, "dur": 65, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426161, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426165, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426195, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426199, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426273, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426276, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426304, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426308, "dur": 101, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426413, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426416, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426437, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426442, "dur": 17, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426462, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426466, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426487, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426490, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426508, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426512, "dur": 12, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426526, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426529, "dur": 139, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426673, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426676, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426701, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426705, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426733, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426738, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426775, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426778, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426803, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426808, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426829, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426832, "dur": 36, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426874, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426878, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426898, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426902, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426926, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426930, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426958, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426963, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426985, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819426987, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427015, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427042, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427044, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427064, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427068, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427085, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427088, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427123, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427127, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427160, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427163, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427183, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427186, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427217, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427219, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427237, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427239, "dur": 71, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427317, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427321, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427346, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427349, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427367, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427370, "dur": 100, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427473, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427475, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427496, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427498, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427520, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427523, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427538, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427540, "dur": 68, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427611, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427614, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427628, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427630, "dur": 133, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427767, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427769, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427834, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427837, "dur": 77, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427918, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819427922, "dur": 81, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819428010, "dur": 4, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819428016, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819428049, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819428051, "dur": 370, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819428424, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819428427, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819428460, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819428477, "dur": 81, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819428565, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819428569, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819428601, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819428605, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819428632, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819428635, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819428669, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819428673, "dur": 126, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819428805, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819428809, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819428832, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819428835, "dur": 107, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819428946, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819428949, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819429016, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819429019, "dur": 16, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819429038, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819429040, "dur": 133, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819429182, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819429186, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819429210, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819429213, "dur": 274, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819429492, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819429496, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819429529, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819429532, "dur": 92, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819429630, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819429634, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819429657, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819429660, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819429675, "dur": 216, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819429893, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819429895, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819429917, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819429921, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819429936, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819429938, "dur": 93, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819430043, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819430046, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819430069, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819430073, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819430090, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819430093, "dur": 108, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819430203, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819430205, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819430230, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819430233, "dur": 15, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819430250, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819430252, "dur": 82, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819430340, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819430343, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819430369, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819430372, "dur": 197, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819430573, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819430576, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819430594, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819430596, "dur": 54, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819430656, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819430658, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819430673, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819430675, "dur": 15, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819430694, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819430713, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819430716, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819430744, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819430748, "dur": 67, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819430819, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819430842, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819430846, "dur": 330, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819431184, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819431223, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819431226, "dur": 19, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819431249, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819431252, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819431270, "dur": 452, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819431727, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819431732, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819431773, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819431778, "dur": 150, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819431933, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819431937, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819431965, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819431968, "dur": 202, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819432178, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819432182, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819432219, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819432223, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819432264, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819432268, "dur": 77, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819432350, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819432354, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819432372, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819432375, "dur": 333, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819432714, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819432720, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819432739, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819432742, "dur": 391, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819433139, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819433142, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819433185, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819433190, "dur": 38485, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819471685, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819471691, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819471717, "dur": 26, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819471746, "dur": 7355, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819479111, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819479121, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819479148, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819479152, "dur": 138, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819479297, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819479302, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819479327, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819479330, "dur": 73, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819479409, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819479415, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819479439, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819479443, "dur": 35, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819479483, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819479489, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819479505, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819479508, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819479549, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819479553, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819479617, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819479624, "dur": 264, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819479893, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819479897, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819479917, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819479920, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819479956, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819479962, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819479987, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819479991, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819480020, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819480024, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819480045, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819480048, "dur": 469, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819480520, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819480523, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819480546, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819480550, "dur": 1004, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819481559, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819481564, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819481602, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819481608, "dur": 116, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819481728, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819481731, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819481758, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819481761, "dur": 23, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819481791, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819481796, "dur": 240, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819482042, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819482046, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819482084, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819482090, "dur": 200, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819482295, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819482299, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819482318, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819482321, "dur": 264, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819482593, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819482598, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819482626, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819482630, "dur": 123, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819482759, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819482764, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819482785, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819482789, "dur": 102, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819482896, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819482901, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819482922, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819482926, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819482953, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819482957, "dur": 696, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819483661, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819483666, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819483689, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819483693, "dur": 671, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819484369, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819484373, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819484391, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819484394, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819484409, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819484412, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819484431, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819484435, "dur": 333, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819484773, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819484777, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819484796, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819484799, "dur": 112, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819484916, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819484921, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819484941, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819484944, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819484966, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819484969, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819484983, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819484986, "dur": 84, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819485075, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819485079, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819485095, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819485098, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819485145, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819485149, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819485165, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819485167, "dur": 208, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819485379, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819485383, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819485398, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819485423, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819485474, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819485477, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819485497, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819485501, "dur": 389, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819485894, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819485897, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819485915, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819485917, "dur": 135, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819486057, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819486061, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819486085, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819486088, "dur": 482, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819486577, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819486580, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819486609, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819486613, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819486675, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819486680, "dur": 226, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819486910, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819486914, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819486929, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819486932, "dur": 253, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819487195, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819487199, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819487219, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819487223, "dur": 432, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819487660, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819487663, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819487682, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819487686, "dur": 292, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819487981, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819487984, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819487999, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819488002, "dur": 194, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819488201, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819488205, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819488227, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819488231, "dur": 104, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819488338, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819488345, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819488364, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819488366, "dur": 197, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819488568, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819488571, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819488595, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819488598, "dur": 96, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819488697, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819488700, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819488725, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819488729, "dur": 417, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819489149, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819489152, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819489169, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819489172, "dur": 103, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819489277, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819489280, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819489295, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819489298, "dur": 144, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819489446, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819489449, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819489464, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819489467, "dur": 154, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819489626, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819489629, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819489648, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819489651, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819489711, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819489716, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819489739, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819489742, "dur": 217, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819489963, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819489966, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819489983, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819489986, "dur": 228, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819490220, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819490223, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819490260, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819490263, "dur": 91, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819490357, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819490362, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819490376, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819490379, "dur": 350, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819490733, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819490736, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819490751, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819490754, "dur": 181, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819490940, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819490943, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819490960, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819490964, "dur": 349, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819491317, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819491321, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819491337, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819491340, "dur": 214, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819491558, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819491576, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819491595, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819491598, "dur": 518, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819492120, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819492123, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819492140, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819492143, "dur": 216, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819492365, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819492370, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819492396, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819492399, "dur": 225, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819492629, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819492632, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819492650, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819492653, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819492676, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819492680, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819492698, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819492701, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819492715, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819492717, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819492735, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819492738, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819492769, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819492771, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819492796, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819492802, "dur": 17, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819492821, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819492825, "dur": 14, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819492842, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819492845, "dur": 23, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819492873, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819492877, "dur": 20, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819492900, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819492904, "dur": 16, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819492922, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819492927, "dur": 17, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819492946, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819492950, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819492977, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819492983, "dur": 24, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819493010, "dur": 3, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819493015, "dur": 25, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819493045, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819493050, "dur": 28, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819493083, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819493088, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819493128, "dur": 4, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819493135, "dur": 26, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819493163, "dur": 4, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819493169, "dur": 21, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819493194, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819493200, "dur": 18, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819493221, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819493226, "dur": 25, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819493257, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819493262, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819493287, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819493291, "dur": 16, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819493309, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819493313, "dur": 20, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819493338, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819493344, "dur": 24, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819493371, "dur": 4, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819493377, "dur": 24, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819493405, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819493409, "dur": 98, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819493513, "dur": 3, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819493518, "dur": 66, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819493591, "dur": 3, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819493596, "dur": 199, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819493802, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819493805, "dur": 139, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819493950, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819493955, "dur": 122, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819494083, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819494088, "dur": 107, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819494201, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819494210, "dur": 139, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819494355, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819494367, "dur": 102, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819494477, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819494481, "dur": 218304, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819712796, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819712801, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819712873, "dur": 37, "ph": "X", "name": "ProcessMessages 3870", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819712913, "dur": 1355, "ph": "X", "name": "ReadAsync 3870", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819714275, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819714279, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819714315, "dur": 3, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 2364, "tid": 73014444032, "ts": 1751382819714320, "dur": 30089, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 2364, "tid": 1554, "ts": 1751382819744928, "dur": 7484, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 2364, "tid": 68719476736, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 2364, "tid": 68719476736, "ts": 1751382819372693, "dur": 251838, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 2364, "tid": 68719476736, "ts": 1751382819624534, "dur": 3, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 2364, "tid": 68719476736, "ts": 1751382819624538, "dur": 72, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 2364, "tid": 1554, "ts": 1751382819752417, "dur": 16, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 2364, "tid": 64424509440, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 2364, "tid": 64424509440, "ts": 1751382819371039, "dur": 373411, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 2364, "tid": 64424509440, "ts": 1751382819371212, "dur": 1444, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 2364, "tid": 64424509440, "ts": 1751382819744456, "dur": 48, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 2364, "tid": 64424509440, "ts": 1751382819744470, "dur": 18, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 2364, "tid": 64424509440, "ts": 1751382819744506, "dur": 2, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 2364, "tid": 1554, "ts": 1751382819752435, "dur": 17, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751382819388446, "dur": 1370, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751382819389823, "dur": 1681, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751382819391604, "dur": 138, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751382819391742, "dur": 255, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751382819392009, "dur": 22248, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751382819414275, "dur": 296284, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751382819710560, "dur": 717, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751382819717175, "dur": 70, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751382819717264, "dur": 24676, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751382819392501, "dur": 21800, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819414303, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_7B22B0BDD3082718.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751382819414932, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_502EE2D33971F102.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751382819415042, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_B70C9B9527050002.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751382819415600, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_5868DE08B6C73CB9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751382819416095, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751382819416310, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819416447, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-firstpass.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751382819416847, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819417121, "dur": 846, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819417968, "dur": 609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819418578, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819418786, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819419187, "dur": 519, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@ba0fe1b085e9\\Editor\\Models\\Operators\\Implementations\\Length.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751382819418998, "dur": 732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819419730, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819419948, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819420176, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819420406, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819420621, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819420864, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819421080, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819421314, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819421539, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819421755, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819421982, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819422303, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819422516, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819422745, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819422945, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819423176, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819423378, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819423659, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819423866, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819424164, "dur": 597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819424761, "dur": 515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819425276, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751382819425517, "dur": 650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751382819426167, "dur": 333, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819426521, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751382819426822, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751382819426997, "dur": 622, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751382819427620, "dur": 465, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819428115, "dur": 741, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751382819428857, "dur": 554, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819429414, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Editor.ref.dll_916A1647F37C4937.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751382819429514, "dur": 168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819429682, "dur": 393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819430076, "dur": 1375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819431452, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751382819431624, "dur": 480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751382819432170, "dur": 351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819432522, "dur": 2631, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819435155, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751382819435334, "dur": 46946, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819482281, "dur": 2162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751382819484444, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819484546, "dur": 4638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751382819489185, "dur": 423, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819489612, "dur": 2413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751382819492026, "dur": 588, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819492621, "dur": 2636, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-unity-editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751382819495258, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819495377, "dur": 492, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819496004, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/OutlineFx.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751382819496065, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819496353, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751382819496459, "dur": 214071, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751382819392611, "dur": 21706, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751382819414320, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_98CF6329D3D6F9AB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751382819415002, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_DA3F53EC74158B55.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751382819415898, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751382819416090, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Psdimporter.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751382819416326, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751382819416440, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751382819416841, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751382819417038, "dur": 939, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751382819417978, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751382819418151, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751382819418339, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751382819418529, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751382819418752, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751382819419017, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751382819419263, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751382819419687, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751382819419904, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751382819420116, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751382819420332, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751382819420530, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751382819420734, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751382819420985, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751382819421204, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751382819421457, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751382819422139, "dur": 417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751382819422556, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751382819422770, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751382819422972, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751382819423198, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751382819423412, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751382819424115, "dur": 661, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751382819424776, "dur": 648, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751382819425425, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751382819425630, "dur": 664, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751382819426326, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751382819426523, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751382819426813, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751382819427074, "dur": 865, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751382819427940, "dur": 519, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751382819428469, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751382819428705, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/ScriptablePacker.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751382819428905, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/ScriptablePacker.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751382819429531, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751382819429797, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751382819430076, "dur": 1704, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751382819431781, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751382819431897, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751382819432197, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751382819432520, "dur": 2634, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751382819435155, "dur": 44849, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751382819480007, "dur": 2346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751382819482399, "dur": 2970, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751382819485370, "dur": 3000, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751382819488376, "dur": 2921, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/NativeFilePicker.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751382819491333, "dur": 2571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751382819493944, "dur": 3069, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751382819497074, "dur": 213484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751382819392825, "dur": 21527, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751382819414354, "dur": 360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_E4B698B849B24AEB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751382819414793, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_F01F6C04D397C746.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751382819415021, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751382819415522, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751382819415681, "dur": 5966, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751382819421751, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751382819421975, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751382819422268, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751382819422491, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751382819422703, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751382819422917, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751382819423144, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751382819423350, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751382819423551, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751382819423752, "dur": 53, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751382819423805, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751382819424123, "dur": 633, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751382819424757, "dur": 454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751382819425227, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751382819425429, "dur": 500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751382819425930, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751382819426005, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751382819426105, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751382819426346, "dur": 670, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751382819427017, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751382819427077, "dur": 669, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751382819427746, "dur": 641, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751382819428491, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751382819428684, "dur": 574, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751382819429262, "dur": 569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751382819429864, "dur": 205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751382819430070, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751382819430232, "dur": 496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751382819430784, "dur": 1725, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751382819432512, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751382819432632, "dur": 476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751382819433109, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751382819433227, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751382819433331, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751382819433728, "dur": 1431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751382819435159, "dur": 44821, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751382819479985, "dur": 2545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/OutlineFx.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751382819482530, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751382819482598, "dur": 2882, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751382819485481, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751382819485576, "dur": 2534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751382819488145, "dur": 4531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751382819492708, "dur": 2371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751382819495118, "dur": 2138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751382819497298, "dur": 213225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751382819392835, "dur": 21531, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751382819414368, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_7D19F4582D6FA60D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751382819414790, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_1237F680F70DA85A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751382819415018, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_E951570DA9D21231.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751382819415559, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1751382819415746, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751382819416311, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751382819416845, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751382819417134, "dur": 917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751382819418051, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751382819418606, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751382819418830, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751382819419022, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751382819419250, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751382819419710, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751382819419945, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751382819420152, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751382819420385, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751382819420585, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751382819420799, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751382819421256, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751382819421751, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751382819421965, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751382819422184, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751382819422677, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751382819422909, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751382819423132, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751382819423343, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751382819423563, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751382819423801, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751382819424120, "dur": 659, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751382819424786, "dur": 424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751382819425212, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751382819425415, "dur": 846, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751382819426261, "dur": 1320, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751382819427617, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751382819427823, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751382819427992, "dur": 769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751382819428762, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751382819428875, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751382819429078, "dur": 619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751382819429697, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751382819429862, "dur": 218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751382819430080, "dur": 2438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751382819432518, "dur": 2638, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751382819435157, "dur": 44846, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751382819480004, "dur": 2237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/NativeFilePicker.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751382819482284, "dur": 2230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751382819484515, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751382819484736, "dur": 2262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751382819486999, "dur": 354, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751382819487359, "dur": 3928, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751382819491289, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751382819491582, "dur": 2499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751382819494082, "dur": 467, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751382819494571, "dur": 2235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751382819496836, "dur": 213725, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819392829, "dur": 21530, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819414362, "dur": 429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_BA35B04A057142E1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751382819414807, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_3713E40F818C5856.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751382819415466, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751382819415772, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751382819415837, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751382819416038, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751382819416133, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751382819416350, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819416713, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3410792791542878585.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751382819416845, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819417061, "dur": 913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819417974, "dur": 158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819418133, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819418317, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819418590, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819418800, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819419013, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819419229, "dur": 422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819419651, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819419874, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819420086, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819420304, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819420627, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819420851, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819421079, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819421301, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819421516, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819421735, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819421960, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819422184, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819422405, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819422635, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819422865, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819423088, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819423293, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819423504, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819423801, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819424117, "dur": 685, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819424802, "dur": 413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819425216, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751382819425521, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819425584, "dur": 577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751382819426215, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Searcher.Editor.ref.dll_CE3A3B080CE6F7DB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751382819426291, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751382819426478, "dur": 633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751382819427112, "dur": 402, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819427537, "dur": 598, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751382819428136, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819428323, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751382819428584, "dur": 808, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751382819429438, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819429690, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819430079, "dur": 2441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819432521, "dur": 2637, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819435158, "dur": 44819, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819479980, "dur": 3186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751382819483166, "dur": 338, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819483513, "dur": 2236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751382819485750, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819485943, "dur": 2295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751382819488239, "dur": 803, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819489062, "dur": 2429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751382819491492, "dur": 2233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819493732, "dur": 2342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751382819496110, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819496424, "dur": 337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751382819496761, "dur": 213764, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819392308, "dur": 21973, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819414298, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819414373, "dur": 389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_57E453305C48C0D1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751382819414805, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_05122CAABA8E3B59.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751382819414974, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_3EA836CAC6D1710A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751382819415027, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819416091, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751382819416340, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819416733, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2669672505582432988.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751382819416836, "dur": 444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819417281, "dur": 1168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819418450, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819418665, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819418865, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819419069, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819419506, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819419709, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819419930, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819420135, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819420405, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819420619, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819420845, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819421063, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819421325, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819421529, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819421769, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819421996, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819422051, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819422320, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819422531, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819422756, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819422961, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819423176, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819423387, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819423666, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819423880, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819424170, "dur": 629, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819424800, "dur": 465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819425266, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751382819425469, "dur": 538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751382819426062, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751382819426246, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819426511, "dur": 1234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751382819427746, "dur": 661, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819428418, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819428481, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751382819428676, "dur": 891, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751382819429567, "dur": 440, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819430020, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751382819430118, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751382819430469, "dur": 2050, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819432519, "dur": 2644, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819435163, "dur": 44796, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819479971, "dur": 2296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751382819482268, "dur": 742, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819483017, "dur": 2825, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751382819485890, "dur": 2361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751382819488252, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819488476, "dur": 2379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751382819490856, "dur": 331, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819491193, "dur": 1990, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751382819493216, "dur": 2603, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751382819496428, "dur": 645, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751382819497073, "dur": 213458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819392855, "dur": 21518, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819414373, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_FB049F22F4C5497A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751382819415029, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_24D87271FC85A7AF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751382819415881, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751382819416089, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751382819416314, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819416453, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751382819416819, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819417022, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819417197, "dur": 999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819418196, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819418456, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819418665, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819418861, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819419059, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819419608, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819419819, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819420037, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819420262, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819420485, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819420720, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819420938, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819421153, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819421401, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819421642, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819421888, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819422136, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819422371, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819422594, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819422812, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819423021, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819423248, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819423463, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819423682, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819423930, "dur": 473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819424404, "dur": 355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819424760, "dur": 456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819425217, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751382819425419, "dur": 627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751382819426047, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819426294, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751382819426520, "dur": 826, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751382819427391, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819427544, "dur": 559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751382819428139, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751382819428318, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819428409, "dur": 703, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751382819429112, "dur": 351, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819429471, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819429768, "dur": 307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819430075, "dur": 292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819430368, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751382819430519, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819430609, "dur": 1334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751382819431987, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751382819432137, "dur": 749, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751382819432934, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751382819433087, "dur": 523, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751382819433664, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751382819433816, "dur": 402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751382819434248, "dur": 916, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819435165, "dur": 44791, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819479958, "dur": 2273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751382819482233, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819482313, "dur": 3282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751382819485596, "dur": 1044, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819486647, "dur": 2221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751382819488869, "dur": 734, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819489608, "dur": 2502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751382819492146, "dur": 2384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualEffectGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751382819494533, "dur": 1088, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819496206, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819496426, "dur": 431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751382819496858, "dur": 213699, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819392359, "dur": 21931, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819414298, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819414365, "dur": 391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_8AA31634E5830E2F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751382819415024, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_8AA30A6C8DDCA8A3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751382819416083, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751382819416305, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819416642, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14262630423516740202.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751382819416849, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819417063, "dur": 789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819417853, "dur": 1120, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819418974, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819419234, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819419675, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819419896, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819420102, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819420320, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819420545, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819420757, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819420992, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819421219, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819421450, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819421658, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819422010, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819422267, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819422489, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819422726, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819423167, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819423371, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819423570, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819423811, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819424113, "dur": 648, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819424761, "dur": 470, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819425232, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751382819425403, "dur": 327, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819425737, "dur": 576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751382819426314, "dur": 529, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819426847, "dur": 648, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751382819427495, "dur": 580, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819428078, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751382819428262, "dur": 636, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819428900, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751382819429079, "dur": 566, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751382819429646, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819429829, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819430074, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751382819430182, "dur": 382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751382819430564, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819430820, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751382819430964, "dur": 731, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751382819431777, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751382819431951, "dur": 491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751382819432509, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751382819432658, "dur": 517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751382819433223, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751382819433369, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819433657, "dur": 500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751382819434210, "dur": 958, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819435168, "dur": 44834, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819480003, "dur": 2244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751382819482283, "dur": 2346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751382819484680, "dur": 3199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp-firstpass.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751382819487912, "dur": 1829, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751382819489742, "dur": 908, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819490657, "dur": 2231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751382819492889, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819492960, "dur": 2364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751382819495327, "dur": 432, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819495767, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819496250, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.IK.Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751382819496386, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751382819496533, "dur": 213994, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819392494, "dur": 21802, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819414298, "dur": 509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_7A744375D0C40DCD.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751382819414845, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_692349F0772110A2.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751382819415001, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_41563AEB7BF951B8.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751382819415699, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1751382819415866, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751382819416333, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819416443, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751382819416831, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819417032, "dur": 1167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819418374, "dur": 741, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@920b46832575\\InputSystem\\Plugins\\InputForUI\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751382819418200, "dur": 963, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819419163, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819419612, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819419826, "dur": 388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819420214, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819420425, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819420650, "dur": 364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819421014, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819421238, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819421485, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819421773, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819422097, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819422334, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819422558, "dur": 415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819422974, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819423174, "dur": 507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819423681, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819423926, "dur": 523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819424449, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819424799, "dur": 435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819425242, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751382819425502, "dur": 911, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751382819426414, "dur": 654, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819427093, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751382819427297, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819427435, "dur": 605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751382819428041, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819428280, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity-editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751382819428485, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819428657, "dur": 692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity-editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751382819429349, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819429419, "dur": 457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751382819429900, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819430072, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751382819430192, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751382819430531, "dur": 1982, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819432514, "dur": 423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819432938, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751382819433084, "dur": 447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751382819433584, "dur": 1590, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819435174, "dur": 44814, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819479994, "dur": 2262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751382819482257, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819482406, "dur": 2270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751382819484677, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819484738, "dur": 2639, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751382819487411, "dur": 2133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751382819489570, "dur": 2297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751382819491867, "dur": 406, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819492279, "dur": 2436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751382819494716, "dur": 1045, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819496133, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819496222, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819496443, "dur": 879, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751382819497322, "dur": 213237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819392522, "dur": 21785, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819414309, "dur": 455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_89517EAC2A874601.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751382819414786, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_966AC74B818A5F90.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751382819415020, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_A5513E9150BC6315.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751382819415559, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751382819415650, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Runtime.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751382819415739, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751382819415957, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751382819416334, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819416811, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12846698987746591812.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751382819416902, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819417109, "dur": 1084, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819418194, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819418374, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819418559, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819418750, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819418956, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819419165, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819419598, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819419801, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819420012, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819420235, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819420455, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819420874, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819421093, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819421318, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819421536, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819421757, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819421984, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819422247, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819422477, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819422695, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819422902, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819423116, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819423327, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819423557, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819423805, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819424150, "dur": 627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819424778, "dur": 431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819425210, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751382819425410, "dur": 2503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751382819427961, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751382819428166, "dur": 691, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751382819428858, "dur": 549, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819429430, "dur": 473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751382819429929, "dur": 197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819430126, "dur": 2389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819432515, "dur": 714, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819433230, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751382819433344, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751382819433700, "dur": 1451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819435153, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751382819435354, "dur": 44638, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819479992, "dur": 2269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751382819482262, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819482402, "dur": 4959, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751382819487414, "dur": 2096, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751382819489511, "dur": 387, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819489905, "dur": 3315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751382819493221, "dur": 711, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751382819493939, "dur": 2538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751382819496525, "dur": 214030, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751382819392603, "dur": 21710, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751382819414315, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_3122CFBB2DC47329.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751382819415017, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751382819415094, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_239A276499DBD1EB.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751382819416273, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1751382819416349, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751382819416496, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751382819416833, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751382819417046, "dur": 904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751382819417950, "dur": 1195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751382819419145, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751382819419610, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751382819419823, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751382819420038, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751382819420276, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751382819420503, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751382819420737, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751382819420974, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751382819421223, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751382819421448, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751382819421646, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751382819421868, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751382819422086, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751382819422296, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751382819422532, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751382819422764, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751382819422962, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751382819423201, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751382819423404, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751382819423621, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751382819423835, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751382819424166, "dur": 608, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751382819424775, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751382819425210, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751382819425498, "dur": 1144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751382819426716, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751382819426891, "dur": 819, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751382819427711, "dur": 1065, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751382819428799, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751382819429081, "dur": 945, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751382819430069, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751382819430226, "dur": 518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751382819430791, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751382819430901, "dur": 465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751382819431449, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751382819431625, "dur": 1242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751382819432906, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751382819433070, "dur": 1050, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751382819434154, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751382819434245, "dur": 443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751382819434727, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751382819434845, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751382819435151, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751382819435351, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751382819435700, "dur": 369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751382819436187, "dur": 270731, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751382819392694, "dur": 21629, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819414325, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_AAD14E71D524EA60.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751382819415022, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819415882, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1751382819416092, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1751382819416228, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751382819416358, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819416588, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819416810, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11474151696266288451.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751382819416860, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819417089, "dur": 878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819417967, "dur": 885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819418853, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819419064, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819419511, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819419711, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819419923, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819420131, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819420360, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819420568, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819420771, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819420993, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819421219, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819421451, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819421658, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819421888, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819422115, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819422434, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819422655, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819422873, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819423093, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819423304, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819423527, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819423805, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819424133, "dur": 622, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819424773, "dur": 457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819425231, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751382819425423, "dur": 538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751382819425961, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819426254, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751382819426442, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751382819426683, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751382819426808, "dur": 431, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819427242, "dur": 596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751382819427838, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819428146, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751382819428465, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819428620, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751382819428903, "dur": 555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751382819429510, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819429744, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819430126, "dur": 2390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819432517, "dur": 2213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819434731, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751382819434858, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751382819435209, "dur": 44759, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819479978, "dur": 2445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751382819482479, "dur": 2518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751382819485031, "dur": 2399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-unity.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751382819487430, "dur": 328, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819487764, "dur": 2730, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751382819490494, "dur": 481, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819490982, "dur": 2345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751382819493358, "dur": 2315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751382819495787, "dur": 530, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819496324, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819496452, "dur": 143974, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751382819640427, "dur": 70135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819392760, "dur": 21568, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819414330, "dur": 479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_D07FFB3B2770754A.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751382819415022, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_EFD593047BD2E6A8.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751382819416028, "dur": 130, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751382819416344, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819416544, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819416825, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819417126, "dur": 978, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819418105, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819418330, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819418817, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819419041, "dur": 457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819419498, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819419708, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819419931, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819420140, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819420363, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819420574, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819420795, "dur": 378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819421173, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819421458, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819421667, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819421874, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819422091, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819422333, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819422579, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819422797, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819422999, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819423362, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819423580, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819423789, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819423996, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819424406, "dur": 357, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819424764, "dur": 461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819425229, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751382819425446, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819425574, "dur": 1225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751382819426852, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819426999, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751382819427180, "dur": 827, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751382819428057, "dur": 1332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751382819429496, "dur": 182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819429710, "dur": 363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819430075, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751382819430263, "dur": 532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751382819430837, "dur": 1687, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819432524, "dur": 2632, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819435156, "dur": 44829, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819479987, "dur": 2274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751382819482262, "dur": 681, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819482951, "dur": 2303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751382819485288, "dur": 2651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751382819487967, "dur": 2701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751382819490669, "dur": 522, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819491196, "dur": 2398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751382819493594, "dur": 339, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751382819493942, "dur": 2477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751382819496454, "dur": 214074, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819392782, "dur": 21552, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819414336, "dur": 471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_21E5C8D6C2CA360E.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751382819414857, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_A1C14A714FD44069.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751382819414938, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_A01C5835F2B3673A.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751382819416282, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751382819416334, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819416825, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819417026, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819417373, "dur": 770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819418143, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819418324, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819418509, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819418720, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819418914, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819419117, "dur": 453, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819419570, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819419774, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819419985, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819420206, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819420437, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819420646, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819420872, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819421086, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819421327, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819421553, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819421798, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819422011, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819422319, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819422537, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819422758, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819422964, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819423183, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819423395, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819423611, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819423808, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819424116, "dur": 685, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819424801, "dur": 419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819425229, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751382819425463, "dur": 794, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751382819426257, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819426446, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751382819426819, "dur": 618, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751382819427437, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819427700, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751382819427903, "dur": 764, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751382819428667, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819428841, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751382819429028, "dur": 607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751382819429666, "dur": 719, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 14, "ts": 1751382819430386, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819430559, "dur": 154, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819430906, "dur": 43734, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 14, "ts": 1751382819479956, "dur": 2291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751382819482248, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819482543, "dur": 2184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751382819484763, "dur": 2187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751382819486951, "dur": 1115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819488075, "dur": 2059, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751382819490188, "dur": 2226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/ScriptablePacker.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751382819492414, "dur": 1332, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819493751, "dur": 2216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/OutlineFx.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751382819496450, "dur": 140275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819638933, "dur": 306, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 14, "ts": 1751382819639239, "dur": 1085, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 14, "ts": 1751382819640325, "dur": 62, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 14, "ts": 1751382819636727, "dur": 3663, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751382819640391, "dur": 70169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819392800, "dur": 21540, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819414343, "dur": 453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_D5DC31B4B0DA901E.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751382819414995, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_15728F368AD157F8.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751382819415603, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_B867065A6613A379.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751382819415828, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEditorBridge.001.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751382819416031, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Common.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751382819416279, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.Editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1751382819416355, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819416839, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819417071, "dur": 916, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819417987, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819418640, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819418862, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819419067, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819419491, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819419695, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819419903, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819420106, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819420318, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819420556, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819420770, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819420988, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819421208, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819421543, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819421760, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819421969, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819422257, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819422493, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819422715, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819422937, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819423145, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819423350, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819423573, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819423799, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819424008, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819424206, "dur": 594, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819424801, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819425218, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751382819425412, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751382819426039, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819426317, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751382819426723, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751382819426887, "dur": 491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751382819427378, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819427698, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751382819427976, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751382819428171, "dur": 1756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751382819429928, "dur": 416, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819430365, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751382819430529, "dur": 822, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751382819431408, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751382819431509, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751382819431886, "dur": 625, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819432516, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751382819432621, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751382819432922, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819433033, "dur": 2129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819435162, "dur": 44812, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819479976, "dur": 2054, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751382819482076, "dur": 2303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-csharp.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751382819484380, "dur": 378, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819484765, "dur": 2505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualEffectGraph.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751382819487271, "dur": 1615, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819488892, "dur": 2282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751382819491174, "dur": 1265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819492445, "dur": 2392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751382819494837, "dur": 870, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819495715, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819495998, "dur": 301, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819496418, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751382819496544, "dur": 213982, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751382819392819, "dur": 21527, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751382819414348, "dur": 471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Apple.Extensions.Common.dll_D4E020400338F5BB.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751382819415023, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751382819415519, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751382819415677, "dur": 6271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751382819422019, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751382819422244, "dur": 2468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751382819424774, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751382819424872, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751382819425210, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751382819425458, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751382819425553, "dur": 639, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751382819426284, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751382819426460, "dur": 586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751382819427047, "dur": 626, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751382819427681, "dur": 553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751382819428234, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751382819428463, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751382819428554, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751382819428774, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751382819428937, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751382819429148, "dur": 799, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751382819429982, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751382819430077, "dur": 2440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751382819432518, "dur": 2649, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751382819435168, "dur": 44828, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751382819479998, "dur": 2560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751382819482559, "dur": 315, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751382819482881, "dur": 2642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751382819485524, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751382819485749, "dur": 2390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751382819488140, "dur": 1469, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751382819489616, "dur": 2046, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751382819491698, "dur": 2543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751382819494241, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751382819494312, "dur": 2364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751382819496738, "dur": 213791, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751382819745937, "dur": 1092, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 2364, "tid": 1554, "ts": 1751382819752476, "dur": 27, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 2364, "tid": 1554, "ts": 1751382819752548, "dur": 6484, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 2364, "tid": 1554, "ts": 1751382819744875, "dur": 14209, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}