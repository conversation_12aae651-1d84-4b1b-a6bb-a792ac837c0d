{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 2364, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 2364, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 2364, "tid": 1828, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 2364, "tid": 1828, "ts": 1751384790453608, "dur": 15, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 2364, "tid": 1828, "ts": 1751384790453648, "dur": 14, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 2364, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 2364, "tid": 1, "ts": 1751384790061781, "dur": 5590, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 2364, "tid": 1, "ts": 1751384790067380, "dur": 227004, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 2364, "tid": 1, "ts": 1751384790294389, "dur": 122276, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 2364, "tid": 1828, "ts": 1751384790453668, "dur": 30, "ph": "X", "name": "", "args": {}}, {"pid": 2364, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790061712, "dur": 15531, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790077246, "dur": 375829, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790077269, "dur": 43, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790077323, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790077334, "dur": 194, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790077535, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790077540, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790077587, "dur": 10, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790077601, "dur": 4744, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790082355, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790082362, "dur": 80, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790082449, "dur": 6, "ph": "X", "name": "ProcessMessages 1918", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790082458, "dur": 39, "ph": "X", "name": "ReadAsync 1918", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790082500, "dur": 4, "ph": "X", "name": "ProcessMessages 1307", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790082507, "dur": 25, "ph": "X", "name": "ReadAsync 1307", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790082536, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790082540, "dur": 35, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790082578, "dur": 2, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790082584, "dur": 27, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790082616, "dur": 1, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790082619, "dur": 36, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790082661, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790082666, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790082731, "dur": 3, "ph": "X", "name": "ProcessMessages 1472", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790082737, "dur": 30, "ph": "X", "name": "ReadAsync 1472", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790082777, "dur": 2, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790082784, "dur": 48, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790082837, "dur": 4, "ph": "X", "name": "ProcessMessages 1435", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790082843, "dur": 45, "ph": "X", "name": "ReadAsync 1435", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790082894, "dur": 6, "ph": "X", "name": "ProcessMessages 1248", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790082903, "dur": 36, "ph": "X", "name": "ReadAsync 1248", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790082944, "dur": 2, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790082949, "dur": 42, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790082997, "dur": 5, "ph": "X", "name": "ProcessMessages 723", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790083005, "dur": 59, "ph": "X", "name": "ReadAsync 723", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790083085, "dur": 4, "ph": "X", "name": "ProcessMessages 1023", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790083101, "dur": 61, "ph": "X", "name": "ReadAsync 1023", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790083177, "dur": 7, "ph": "X", "name": "ProcessMessages 1250", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790083188, "dur": 55, "ph": "X", "name": "ReadAsync 1250", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790083248, "dur": 4, "ph": "X", "name": "ProcessMessages 868", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790083256, "dur": 43, "ph": "X", "name": "ReadAsync 868", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790083305, "dur": 4, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790083313, "dur": 36, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790083354, "dur": 3, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790083360, "dur": 42, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790083407, "dur": 3, "ph": "X", "name": "ProcessMessages 715", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790083412, "dur": 36, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790083452, "dur": 4, "ph": "X", "name": "ProcessMessages 1045", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790083460, "dur": 45, "ph": "X", "name": "ReadAsync 1045", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790083508, "dur": 2, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790083511, "dur": 29, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790083546, "dur": 4, "ph": "X", "name": "ProcessMessages 860", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790083554, "dur": 38, "ph": "X", "name": "ReadAsync 860", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790083597, "dur": 3, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790083602, "dur": 43, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790083655, "dur": 3, "ph": "X", "name": "ProcessMessages 861", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790083661, "dur": 33, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790083698, "dur": 2, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790083703, "dur": 36, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790083744, "dur": 3, "ph": "X", "name": "ProcessMessages 838", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790083750, "dur": 35, "ph": "X", "name": "ReadAsync 838", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790083792, "dur": 4, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790083801, "dur": 42, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790083851, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790083856, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790083896, "dur": 3, "ph": "X", "name": "ProcessMessages 900", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790083905, "dur": 41, "ph": "X", "name": "ReadAsync 900", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790083962, "dur": 4, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790083970, "dur": 46, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790084019, "dur": 3, "ph": "X", "name": "ProcessMessages 928", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790084027, "dur": 46, "ph": "X", "name": "ReadAsync 928", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790084080, "dur": 3, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790084086, "dur": 31, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790084121, "dur": 2, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790084126, "dur": 35, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790084165, "dur": 4, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790084179, "dur": 42, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790084225, "dur": 2, "ph": "X", "name": "ProcessMessages 852", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790084230, "dur": 29, "ph": "X", "name": "ReadAsync 852", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790084263, "dur": 9, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790084275, "dur": 50, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790084332, "dur": 4, "ph": "X", "name": "ProcessMessages 918", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790084347, "dur": 57, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790084408, "dur": 4, "ph": "X", "name": "ProcessMessages 1164", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790084425, "dur": 42, "ph": "X", "name": "ReadAsync 1164", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790084477, "dur": 5, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790084490, "dur": 47, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790084542, "dur": 3, "ph": "X", "name": "ProcessMessages 1002", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790084547, "dur": 37, "ph": "X", "name": "ReadAsync 1002", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790084589, "dur": 5, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790084597, "dur": 28, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790084629, "dur": 2, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790084635, "dur": 35, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790084674, "dur": 8, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790084691, "dur": 50, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790084745, "dur": 6, "ph": "X", "name": "ProcessMessages 1073", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790084756, "dur": 34, "ph": "X", "name": "ReadAsync 1073", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790084793, "dur": 3, "ph": "X", "name": "ProcessMessages 812", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790084799, "dur": 33, "ph": "X", "name": "ReadAsync 812", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790084839, "dur": 2, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790084848, "dur": 43, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790084895, "dur": 4, "ph": "X", "name": "ProcessMessages 932", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790084905, "dur": 58, "ph": "X", "name": "ReadAsync 932", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790084973, "dur": 6, "ph": "X", "name": "ProcessMessages 880", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790084984, "dur": 54, "ph": "X", "name": "ReadAsync 880", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790085050, "dur": 6, "ph": "X", "name": "ProcessMessages 1526", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790085067, "dur": 56, "ph": "X", "name": "ReadAsync 1526", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790085134, "dur": 4, "ph": "X", "name": "ProcessMessages 1254", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790085147, "dur": 36, "ph": "X", "name": "ReadAsync 1254", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790085187, "dur": 9, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790085201, "dur": 59, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790085267, "dur": 5, "ph": "X", "name": "ProcessMessages 1070", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790085275, "dur": 42, "ph": "X", "name": "ReadAsync 1070", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790085324, "dur": 3, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790085332, "dur": 48, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790085385, "dur": 3, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790085392, "dur": 50, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790085449, "dur": 8, "ph": "X", "name": "ProcessMessages 922", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790085465, "dur": 38, "ph": "X", "name": "ReadAsync 922", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790085509, "dur": 3, "ph": "X", "name": "ProcessMessages 1215", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790085515, "dur": 30, "ph": "X", "name": "ReadAsync 1215", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790085550, "dur": 2, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790085556, "dur": 37, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790085597, "dur": 2, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790085602, "dur": 73, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790085680, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790085684, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790085725, "dur": 3, "ph": "X", "name": "ProcessMessages 970", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790085732, "dur": 34, "ph": "X", "name": "ReadAsync 970", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790085772, "dur": 3, "ph": "X", "name": "ProcessMessages 642", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790085781, "dur": 18, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790085802, "dur": 2, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790085809, "dur": 26, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790085840, "dur": 4, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790085847, "dur": 27, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790085878, "dur": 3, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790085886, "dur": 29, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790085918, "dur": 2, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790085925, "dur": 21, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790085950, "dur": 2, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790085955, "dur": 30, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790085989, "dur": 3, "ph": "X", "name": "ProcessMessages 730", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790085995, "dur": 25, "ph": "X", "name": "ReadAsync 730", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790086027, "dur": 3, "ph": "X", "name": "ProcessMessages 677", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790086033, "dur": 31, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790086066, "dur": 3, "ph": "X", "name": "ProcessMessages 685", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790086072, "dur": 27, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790086105, "dur": 3, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790086110, "dur": 39, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790086155, "dur": 3, "ph": "X", "name": "ProcessMessages 804", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790086162, "dur": 36, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790086202, "dur": 3, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790086210, "dur": 38, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790086253, "dur": 3, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790086260, "dur": 45, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790086312, "dur": 4, "ph": "X", "name": "ProcessMessages 868", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790086319, "dur": 50, "ph": "X", "name": "ReadAsync 868", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790086374, "dur": 3, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790086379, "dur": 31, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790086421, "dur": 4, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790086429, "dur": 33, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790086475, "dur": 3, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790086481, "dur": 54, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790086540, "dur": 4, "ph": "X", "name": "ProcessMessages 1039", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790086550, "dur": 50, "ph": "X", "name": "ReadAsync 1039", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790086608, "dur": 3, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790086614, "dur": 64, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790086688, "dur": 4, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790086695, "dur": 55, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790086756, "dur": 5, "ph": "X", "name": "ProcessMessages 1361", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790086767, "dur": 45, "ph": "X", "name": "ReadAsync 1361", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790086819, "dur": 2, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790086824, "dur": 47, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790086877, "dur": 4, "ph": "X", "name": "ProcessMessages 1277", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790086885, "dur": 42, "ph": "X", "name": "ReadAsync 1277", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790086935, "dur": 4, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790086946, "dur": 64, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790087014, "dur": 3, "ph": "X", "name": "ProcessMessages 1272", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790087021, "dur": 30, "ph": "X", "name": "ReadAsync 1272", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790087055, "dur": 2, "ph": "X", "name": "ProcessMessages 171", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790087061, "dur": 45, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790087112, "dur": 4, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790087123, "dur": 37, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790087168, "dur": 4, "ph": "X", "name": "ProcessMessages 1074", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790087176, "dur": 30, "ph": "X", "name": "ReadAsync 1074", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790087212, "dur": 3, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790087219, "dur": 46, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790087269, "dur": 2, "ph": "X", "name": "ProcessMessages 815", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790087274, "dur": 29, "ph": "X", "name": "ReadAsync 815", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790087310, "dur": 3, "ph": "X", "name": "ProcessMessages 619", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790087319, "dur": 65, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790087396, "dur": 2, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790087402, "dur": 36, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790087444, "dur": 3, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790087452, "dur": 56, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790087524, "dur": 3, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790087536, "dur": 40, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790087582, "dur": 3, "ph": "X", "name": "ProcessMessages 764", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790087598, "dur": 73, "ph": "X", "name": "ReadAsync 764", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790087684, "dur": 2, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790087699, "dur": 46, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790087768, "dur": 5, "ph": "X", "name": "ProcessMessages 943", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790087780, "dur": 51, "ph": "X", "name": "ReadAsync 943", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790087842, "dur": 7, "ph": "X", "name": "ProcessMessages 878", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790087853, "dur": 42, "ph": "X", "name": "ReadAsync 878", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790087899, "dur": 7, "ph": "X", "name": "ProcessMessages 979", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790087910, "dur": 49, "ph": "X", "name": "ReadAsync 979", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790087964, "dur": 5, "ph": "X", "name": "ProcessMessages 885", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790087972, "dur": 42, "ph": "X", "name": "ReadAsync 885", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790088019, "dur": 3, "ph": "X", "name": "ProcessMessages 709", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790088025, "dur": 58, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790088092, "dur": 4, "ph": "X", "name": "ProcessMessages 915", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790088100, "dur": 39, "ph": "X", "name": "ReadAsync 915", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790088148, "dur": 3, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790088166, "dur": 42, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790088219, "dur": 4, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790088228, "dur": 69, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790088303, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790088313, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790088417, "dur": 7, "ph": "X", "name": "ProcessMessages 1762", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790088427, "dur": 127, "ph": "X", "name": "ReadAsync 1762", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790088563, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790088568, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790088615, "dur": 3, "ph": "X", "name": "ProcessMessages 896", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790088623, "dur": 55, "ph": "X", "name": "ReadAsync 896", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790088688, "dur": 4, "ph": "X", "name": "ProcessMessages 1044", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790088694, "dur": 68, "ph": "X", "name": "ReadAsync 1044", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790088766, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790088776, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790088814, "dur": 4, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790088823, "dur": 41, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790088877, "dur": 8, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790088895, "dur": 67, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790088969, "dur": 5, "ph": "X", "name": "ProcessMessages 912", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790088979, "dur": 49, "ph": "X", "name": "ReadAsync 912", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790089034, "dur": 3, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790089040, "dur": 49, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790089096, "dur": 3, "ph": "X", "name": "ProcessMessages 953", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790089104, "dur": 39, "ph": "X", "name": "ReadAsync 953", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790089148, "dur": 3, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790089154, "dur": 40, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790089203, "dur": 7, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790089218, "dur": 37, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790089260, "dur": 6, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790089270, "dur": 43, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790089323, "dur": 3, "ph": "X", "name": "ProcessMessages 900", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790089338, "dur": 52, "ph": "X", "name": "ReadAsync 900", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790089396, "dur": 7, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790089409, "dur": 44, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790089461, "dur": 7, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790089472, "dur": 141, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790089622, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790089632, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790089690, "dur": 9, "ph": "X", "name": "ProcessMessages 1275", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790089705, "dur": 122, "ph": "X", "name": "ReadAsync 1275", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790089843, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790089854, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790089903, "dur": 3, "ph": "X", "name": "ProcessMessages 873", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790089911, "dur": 39, "ph": "X", "name": "ReadAsync 873", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790089956, "dur": 3, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790089962, "dur": 54, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790090019, "dur": 4, "ph": "X", "name": "ProcessMessages 1327", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790090026, "dur": 28, "ph": "X", "name": "ReadAsync 1327", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790090061, "dur": 13, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790090076, "dur": 37, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790090118, "dur": 6, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790090129, "dur": 39, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790090173, "dur": 2, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790090188, "dur": 49, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790090240, "dur": 4, "ph": "X", "name": "ProcessMessages 1267", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790090248, "dur": 17, "ph": "X", "name": "ReadAsync 1267", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790090268, "dur": 2, "ph": "X", "name": "ProcessMessages 73", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790090274, "dur": 44, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790090324, "dur": 4, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790090331, "dur": 37, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790090375, "dur": 5, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790090384, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790090448, "dur": 5, "ph": "X", "name": "ProcessMessages 1345", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790090456, "dur": 57, "ph": "X", "name": "ReadAsync 1345", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790090517, "dur": 11, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790090532, "dur": 66, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790090606, "dur": 8, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790090635, "dur": 74, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790090714, "dur": 3, "ph": "X", "name": "ProcessMessages 158", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790090720, "dur": 68, "ph": "X", "name": "ReadAsync 158", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790090808, "dur": 16, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790090833, "dur": 50, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790090901, "dur": 4, "ph": "X", "name": "ProcessMessages 883", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790090908, "dur": 39, "ph": "X", "name": "ReadAsync 883", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790090953, "dur": 2, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790090957, "dur": 32, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790090994, "dur": 3, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790091003, "dur": 50, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790091058, "dur": 4, "ph": "X", "name": "ProcessMessages 1077", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790091067, "dur": 49, "ph": "X", "name": "ReadAsync 1077", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790091124, "dur": 4, "ph": "X", "name": "ProcessMessages 751", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790091135, "dur": 39, "ph": "X", "name": "ReadAsync 751", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790091178, "dur": 4, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790091184, "dur": 20, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790091212, "dur": 3, "ph": "X", "name": "ProcessMessages 166", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790091230, "dur": 54, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790091289, "dur": 5, "ph": "X", "name": "ProcessMessages 1002", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790091304, "dur": 24, "ph": "X", "name": "ReadAsync 1002", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790091332, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790091337, "dur": 39, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790091383, "dur": 3, "ph": "X", "name": "ProcessMessages 202", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790091390, "dur": 46, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790091440, "dur": 6, "ph": "X", "name": "ProcessMessages 1077", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790091451, "dur": 47, "ph": "X", "name": "ReadAsync 1077", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790091505, "dur": 3, "ph": "X", "name": "ProcessMessages 955", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790091511, "dur": 45, "ph": "X", "name": "ReadAsync 955", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790091564, "dur": 3, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790091576, "dur": 40, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790091620, "dur": 3, "ph": "X", "name": "ProcessMessages 869", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790091633, "dur": 56, "ph": "X", "name": "ReadAsync 869", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790091695, "dur": 4, "ph": "X", "name": "ProcessMessages 929", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790091703, "dur": 66, "ph": "X", "name": "ReadAsync 929", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790091775, "dur": 3, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790091781, "dur": 35, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790091820, "dur": 4, "ph": "X", "name": "ProcessMessages 839", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790091827, "dur": 44, "ph": "X", "name": "ReadAsync 839", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790091877, "dur": 3, "ph": "X", "name": "ProcessMessages 951", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790091886, "dur": 92, "ph": "X", "name": "ReadAsync 951", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790091985, "dur": 5, "ph": "X", "name": "ProcessMessages 701", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790091992, "dur": 74, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790092072, "dur": 4, "ph": "X", "name": "ProcessMessages 1496", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790092079, "dur": 46, "ph": "X", "name": "ReadAsync 1496", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790092151, "dur": 18, "ph": "X", "name": "ProcessMessages 692", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790092177, "dur": 49, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790092233, "dur": 4, "ph": "X", "name": "ProcessMessages 1323", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790092239, "dur": 37, "ph": "X", "name": "ReadAsync 1323", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790092282, "dur": 3, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790092288, "dur": 31, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790092324, "dur": 3, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790092330, "dur": 29, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790092363, "dur": 2, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790092367, "dur": 34, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790092407, "dur": 3, "ph": "X", "name": "ProcessMessages 734", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790092413, "dur": 30, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790092448, "dur": 1, "ph": "X", "name": "ProcessMessages 125", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790092453, "dur": 36, "ph": "X", "name": "ReadAsync 125", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790092494, "dur": 4, "ph": "X", "name": "ProcessMessages 750", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790092501, "dur": 35, "ph": "X", "name": "ReadAsync 750", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790092545, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790092551, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790092605, "dur": 2, "ph": "X", "name": "ProcessMessages 130", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790092610, "dur": 29, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790092645, "dur": 3, "ph": "X", "name": "ProcessMessages 195", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790092651, "dur": 41, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790092696, "dur": 3, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790092703, "dur": 227, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790092936, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790092951, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790093019, "dur": 5, "ph": "X", "name": "ProcessMessages 1172", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790093027, "dur": 46, "ph": "X", "name": "ReadAsync 1172", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790093079, "dur": 3, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790093085, "dur": 30, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790093120, "dur": 3, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790093133, "dur": 64, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790093204, "dur": 2, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790093210, "dur": 313, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790093531, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790093540, "dur": 71, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790093618, "dur": 4, "ph": "X", "name": "ProcessMessages 983", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790093629, "dur": 41, "ph": "X", "name": "ReadAsync 983", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790093673, "dur": 2, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790093679, "dur": 37, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790093724, "dur": 4, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790093732, "dur": 57, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790093794, "dur": 7, "ph": "X", "name": "ProcessMessages 1102", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790093806, "dur": 38, "ph": "X", "name": "ReadAsync 1102", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790093850, "dur": 3, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790093857, "dur": 43, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790093905, "dur": 3, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790093912, "dur": 41, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790093959, "dur": 8, "ph": "X", "name": "ProcessMessages 977", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790093973, "dur": 43, "ph": "X", "name": "ReadAsync 977", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790094022, "dur": 4, "ph": "X", "name": "ProcessMessages 743", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790094031, "dur": 39, "ph": "X", "name": "ReadAsync 743", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790094075, "dur": 2, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790094079, "dur": 34, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790094118, "dur": 3, "ph": "X", "name": "ProcessMessages 818", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790094127, "dur": 37, "ph": "X", "name": "ReadAsync 818", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790094171, "dur": 2, "ph": "X", "name": "ProcessMessages 799", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790094177, "dur": 34, "ph": "X", "name": "ReadAsync 799", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790094220, "dur": 11, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790094235, "dur": 57, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790094305, "dur": 7, "ph": "X", "name": "ProcessMessages 1143", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790094315, "dur": 43, "ph": "X", "name": "ReadAsync 1143", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790094366, "dur": 6, "ph": "X", "name": "ProcessMessages 877", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790094378, "dur": 47, "ph": "X", "name": "ReadAsync 877", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790094430, "dur": 6, "ph": "X", "name": "ProcessMessages 1480", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790094440, "dur": 46, "ph": "X", "name": "ReadAsync 1480", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790094492, "dur": 3, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790094500, "dur": 41, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790094547, "dur": 3, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790094554, "dur": 42, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790094603, "dur": 4, "ph": "X", "name": "ProcessMessages 1012", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790094609, "dur": 57, "ph": "X", "name": "ReadAsync 1012", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790094671, "dur": 3, "ph": "X", "name": "ProcessMessages 786", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790094678, "dur": 50, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790094735, "dur": 6, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790094744, "dur": 39, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790094793, "dur": 4, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790094841, "dur": 58, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790094942, "dur": 15, "ph": "X", "name": "ProcessMessages 1760", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790094960, "dur": 86, "ph": "X", "name": "ReadAsync 1760", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790095053, "dur": 18, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790095082, "dur": 104, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790095202, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790095227, "dur": 61, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790095296, "dur": 5, "ph": "X", "name": "ProcessMessages 1407", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790095304, "dur": 45, "ph": "X", "name": "ReadAsync 1407", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790095354, "dur": 10, "ph": "X", "name": "ProcessMessages 971", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790095369, "dur": 32, "ph": "X", "name": "ReadAsync 971", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790095406, "dur": 3, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790095416, "dur": 46, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790095468, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790095474, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790095530, "dur": 3, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790095539, "dur": 47, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790095595, "dur": 3, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790095602, "dur": 36, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790095642, "dur": 2, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790095647, "dur": 33, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790095684, "dur": 4, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790095692, "dur": 38, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790095736, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790095740, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790095785, "dur": 3, "ph": "X", "name": "ProcessMessages 1218", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790095792, "dur": 34, "ph": "X", "name": "ReadAsync 1218", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790095833, "dur": 11, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790095848, "dur": 48, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790095905, "dur": 3, "ph": "X", "name": "ProcessMessages 1116", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790095911, "dur": 30, "ph": "X", "name": "ReadAsync 1116", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790095950, "dur": 3, "ph": "X", "name": "ProcessMessages 686", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790095956, "dur": 28, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790095990, "dur": 1, "ph": "X", "name": "ProcessMessages 175", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790095994, "dur": 47, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790096050, "dur": 3, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790096076, "dur": 67, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790096159, "dur": 7, "ph": "X", "name": "ProcessMessages 1226", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790096171, "dur": 66, "ph": "X", "name": "ReadAsync 1226", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790096243, "dur": 3, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790096259, "dur": 86, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790096365, "dur": 4, "ph": "X", "name": "ProcessMessages 1458", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790096373, "dur": 70, "ph": "X", "name": "ReadAsync 1458", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790096448, "dur": 3, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790096459, "dur": 34, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790096501, "dur": 13, "ph": "X", "name": "ProcessMessages 907", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790096517, "dur": 34, "ph": "X", "name": "ReadAsync 907", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790096559, "dur": 3, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790096564, "dur": 66, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790096642, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790096653, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790096703, "dur": 10, "ph": "X", "name": "ProcessMessages 995", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790096724, "dur": 52, "ph": "X", "name": "ReadAsync 995", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790096783, "dur": 6, "ph": "X", "name": "ProcessMessages 1170", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790096797, "dur": 38, "ph": "X", "name": "ReadAsync 1170", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790096844, "dur": 3, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790096858, "dur": 52, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790096926, "dur": 4, "ph": "X", "name": "ProcessMessages 1242", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790096936, "dur": 48, "ph": "X", "name": "ReadAsync 1242", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790096993, "dur": 4, "ph": "X", "name": "ProcessMessages 1073", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790097001, "dur": 60, "ph": "X", "name": "ReadAsync 1073", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790097067, "dur": 6, "ph": "X", "name": "ProcessMessages 756", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790097077, "dur": 58, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790097140, "dur": 3, "ph": "X", "name": "ProcessMessages 941", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790097146, "dur": 34, "ph": "X", "name": "ReadAsync 941", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790097197, "dur": 3, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790097205, "dur": 26, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790097235, "dur": 1, "ph": "X", "name": "ProcessMessages 152", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790097238, "dur": 33, "ph": "X", "name": "ReadAsync 152", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790097277, "dur": 4, "ph": "X", "name": "ProcessMessages 945", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790097284, "dur": 47, "ph": "X", "name": "ReadAsync 945", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790097338, "dur": 4, "ph": "X", "name": "ProcessMessages 1275", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790097345, "dur": 44, "ph": "X", "name": "ReadAsync 1275", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790097395, "dur": 2, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790097399, "dur": 45, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790097454, "dur": 3, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790097460, "dur": 54, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790097518, "dur": 3, "ph": "X", "name": "ProcessMessages 893", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790097523, "dur": 37, "ph": "X", "name": "ReadAsync 893", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790097567, "dur": 3, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790097573, "dur": 45, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790097623, "dur": 5, "ph": "X", "name": "ProcessMessages 888", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790097632, "dur": 35, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790097670, "dur": 2, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790097675, "dur": 35, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790097716, "dur": 4, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790097723, "dur": 43, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790097770, "dur": 9, "ph": "X", "name": "ProcessMessages 725", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790097787, "dur": 47, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790097839, "dur": 4, "ph": "X", "name": "ProcessMessages 699", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790097846, "dur": 41, "ph": "X", "name": "ReadAsync 699", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790097892, "dur": 4, "ph": "X", "name": "ProcessMessages 1221", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790097902, "dur": 32, "ph": "X", "name": "ReadAsync 1221", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790097938, "dur": 3, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790097945, "dur": 31, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790097981, "dur": 3, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790097987, "dur": 30, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098022, "dur": 5, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098030, "dur": 32, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098068, "dur": 3, "ph": "X", "name": "ProcessMessages 743", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098076, "dur": 37, "ph": "X", "name": "ReadAsync 743", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098118, "dur": 3, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098123, "dur": 26, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098153, "dur": 2, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098158, "dur": 38, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098201, "dur": 3, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098209, "dur": 42, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098256, "dur": 6, "ph": "X", "name": "ProcessMessages 877", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098266, "dur": 37, "ph": "X", "name": "ReadAsync 877", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098307, "dur": 4, "ph": "X", "name": "ProcessMessages 998", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098314, "dur": 45, "ph": "X", "name": "ReadAsync 998", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098364, "dur": 5, "ph": "X", "name": "ProcessMessages 1154", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098373, "dur": 27, "ph": "X", "name": "ReadAsync 1154", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098403, "dur": 2, "ph": "X", "name": "ProcessMessages 821", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098409, "dur": 37, "ph": "X", "name": "ReadAsync 821", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098450, "dur": 2, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098455, "dur": 28, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098485, "dur": 10, "ph": "X", "name": "ProcessMessages 783", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098500, "dur": 30, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098534, "dur": 3, "ph": "X", "name": "ProcessMessages 971", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098540, "dur": 34, "ph": "X", "name": "ReadAsync 971", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098580, "dur": 3, "ph": "X", "name": "ProcessMessages 775", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098586, "dur": 34, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098626, "dur": 3, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098632, "dur": 48, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098685, "dur": 3, "ph": "X", "name": "ProcessMessages 766", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098691, "dur": 45, "ph": "X", "name": "ReadAsync 766", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098742, "dur": 9, "ph": "X", "name": "ProcessMessages 844", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098756, "dur": 32, "ph": "X", "name": "ReadAsync 844", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098792, "dur": 2, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098796, "dur": 44, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098845, "dur": 3, "ph": "X", "name": "ProcessMessages 945", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098851, "dur": 35, "ph": "X", "name": "ReadAsync 945", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098888, "dur": 3, "ph": "X", "name": "ProcessMessages 798", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098894, "dur": 28, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098929, "dur": 3, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098938, "dur": 38, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098979, "dur": 2, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790098984, "dur": 46, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790099035, "dur": 4, "ph": "X", "name": "ProcessMessages 1260", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790099043, "dur": 44, "ph": "X", "name": "ReadAsync 1260", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790099094, "dur": 5, "ph": "X", "name": "ProcessMessages 1002", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790099106, "dur": 43, "ph": "X", "name": "ReadAsync 1002", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790099155, "dur": 4, "ph": "X", "name": "ProcessMessages 1267", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790099162, "dur": 38, "ph": "X", "name": "ReadAsync 1267", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790099204, "dur": 4, "ph": "X", "name": "ProcessMessages 1385", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790099210, "dur": 52, "ph": "X", "name": "ReadAsync 1385", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790099269, "dur": 8, "ph": "X", "name": "ProcessMessages 1241", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790099281, "dur": 37, "ph": "X", "name": "ReadAsync 1241", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790099323, "dur": 3, "ph": "X", "name": "ProcessMessages 808", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790099329, "dur": 30, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790099362, "dur": 2, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790099366, "dur": 30, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790099401, "dur": 3, "ph": "X", "name": "ProcessMessages 721", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790099406, "dur": 43, "ph": "X", "name": "ReadAsync 721", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790099454, "dur": 4, "ph": "X", "name": "ProcessMessages 984", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790099462, "dur": 34, "ph": "X", "name": "ReadAsync 984", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790099500, "dur": 2, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790099504, "dur": 46, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790099556, "dur": 4, "ph": "X", "name": "ProcessMessages 1147", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790099563, "dur": 35, "ph": "X", "name": "ReadAsync 1147", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790099603, "dur": 2, "ph": "X", "name": "ProcessMessages 764", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790099608, "dur": 29, "ph": "X", "name": "ReadAsync 764", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790099640, "dur": 3, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790099646, "dur": 38, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790099687, "dur": 4, "ph": "X", "name": "ProcessMessages 1213", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790099694, "dur": 35, "ph": "X", "name": "ReadAsync 1213", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790099733, "dur": 3, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790099738, "dur": 40, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790099784, "dur": 3, "ph": "X", "name": "ProcessMessages 641", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790099790, "dur": 34, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790099828, "dur": 2, "ph": "X", "name": "ProcessMessages 1018", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790099833, "dur": 29, "ph": "X", "name": "ReadAsync 1018", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790099866, "dur": 3, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790099870, "dur": 32, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790099907, "dur": 2, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790099911, "dur": 38, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790099953, "dur": 3, "ph": "X", "name": "ProcessMessages 1299", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790099959, "dur": 39, "ph": "X", "name": "ReadAsync 1299", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100003, "dur": 3, "ph": "X", "name": "ProcessMessages 920", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100009, "dur": 32, "ph": "X", "name": "ReadAsync 920", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100046, "dur": 2, "ph": "X", "name": "ProcessMessages 232", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100049, "dur": 33, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100087, "dur": 2, "ph": "X", "name": "ProcessMessages 763", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100092, "dur": 35, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100131, "dur": 3, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100137, "dur": 35, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100176, "dur": 3, "ph": "X", "name": "ProcessMessages 817", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100182, "dur": 38, "ph": "X", "name": "ReadAsync 817", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100224, "dur": 3, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100229, "dur": 37, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100270, "dur": 2, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100274, "dur": 40, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100318, "dur": 4, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100326, "dur": 40, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100370, "dur": 6, "ph": "X", "name": "ProcessMessages 893", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100380, "dur": 32, "ph": "X", "name": "ReadAsync 893", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100417, "dur": 3, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100422, "dur": 34, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100460, "dur": 3, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100467, "dur": 36, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100508, "dur": 4, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100517, "dur": 38, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100559, "dur": 7, "ph": "X", "name": "ProcessMessages 1028", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100571, "dur": 40, "ph": "X", "name": "ReadAsync 1028", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100615, "dur": 2, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100619, "dur": 37, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100661, "dur": 2, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100667, "dur": 35, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100708, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100713, "dur": 35, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100752, "dur": 2, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100756, "dur": 32, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100793, "dur": 3, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100798, "dur": 30, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100832, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100837, "dur": 85, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100927, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100932, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100966, "dur": 3, "ph": "X", "name": "ProcessMessages 804", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790100971, "dur": 35, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101011, "dur": 3, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101017, "dur": 33, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101053, "dur": 2, "ph": "X", "name": "ProcessMessages 815", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101062, "dur": 40, "ph": "X", "name": "ReadAsync 815", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101112, "dur": 3, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101119, "dur": 30, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101153, "dur": 2, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101157, "dur": 44, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101208, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101215, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101256, "dur": 2, "ph": "X", "name": "ProcessMessages 766", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101260, "dur": 30, "ph": "X", "name": "ReadAsync 766", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101295, "dur": 2, "ph": "X", "name": "ProcessMessages 414", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101299, "dur": 27, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101329, "dur": 2, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101333, "dur": 32, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101370, "dur": 3, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101375, "dur": 30, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101408, "dur": 2, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101412, "dur": 65, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101483, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101487, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101544, "dur": 3, "ph": "X", "name": "ProcessMessages 1087", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101550, "dur": 30, "ph": "X", "name": "ReadAsync 1087", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101586, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101591, "dur": 44, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101640, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101647, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101696, "dur": 3, "ph": "X", "name": "ProcessMessages 1072", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101702, "dur": 63, "ph": "X", "name": "ReadAsync 1072", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101770, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101773, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101804, "dur": 2, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101809, "dur": 29, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101843, "dur": 2, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101849, "dur": 35, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101890, "dur": 4, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101897, "dur": 51, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101952, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101955, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101991, "dur": 2, "ph": "X", "name": "ProcessMessages 703", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790101998, "dur": 28, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790102030, "dur": 2, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790102035, "dur": 47, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790102086, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790102089, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790102128, "dur": 3, "ph": "X", "name": "ProcessMessages 1014", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790102132, "dur": 20, "ph": "X", "name": "ReadAsync 1014", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790102154, "dur": 3, "ph": "X", "name": "ProcessMessages 58", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790102159, "dur": 52, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790102219, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790102225, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790102268, "dur": 3, "ph": "X", "name": "ProcessMessages 858", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790102273, "dur": 25, "ph": "X", "name": "ReadAsync 858", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790102301, "dur": 1, "ph": "X", "name": "ProcessMessages 138", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790102304, "dur": 54, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790102362, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790102364, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790102396, "dur": 2, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790102399, "dur": 28, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790102431, "dur": 2, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790102436, "dur": 50, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790102491, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790102497, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790102549, "dur": 4, "ph": "X", "name": "ProcessMessages 1114", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790102554, "dur": 45, "ph": "X", "name": "ReadAsync 1114", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790102604, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790102608, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790102650, "dur": 5, "ph": "X", "name": "ProcessMessages 1062", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790102657, "dur": 52, "ph": "X", "name": "ReadAsync 1062", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790102713, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790102717, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790102762, "dur": 3, "ph": "X", "name": "ProcessMessages 1082", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790102769, "dur": 19, "ph": "X", "name": "ReadAsync 1082", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790102791, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790102794, "dur": 53, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790102857, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790102862, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790102908, "dur": 4, "ph": "X", "name": "ProcessMessages 740", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790102915, "dur": 36, "ph": "X", "name": "ReadAsync 740", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790102956, "dur": 3, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790102963, "dur": 63, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790103030, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790103034, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790103072, "dur": 2, "ph": "X", "name": "ProcessMessages 710", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790103076, "dur": 27, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790103106, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790103110, "dur": 45, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790103158, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790103162, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790103201, "dur": 3, "ph": "X", "name": "ProcessMessages 787", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790103206, "dur": 30, "ph": "X", "name": "ReadAsync 787", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790103240, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790103243, "dur": 64, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790103313, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790103322, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790103374, "dur": 5, "ph": "X", "name": "ProcessMessages 1181", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790103382, "dur": 32, "ph": "X", "name": "ReadAsync 1181", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790103419, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790103424, "dur": 37, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790103465, "dur": 3, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790103471, "dur": 36, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790103511, "dur": 3, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790103517, "dur": 29, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790103551, "dur": 2, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790103556, "dur": 58, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790103618, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790103623, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790103665, "dur": 2, "ph": "X", "name": "ProcessMessages 1015", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790103669, "dur": 27, "ph": "X", "name": "ReadAsync 1015", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790103699, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790103704, "dur": 60, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790103768, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790103773, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790103821, "dur": 4, "ph": "X", "name": "ProcessMessages 1148", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790103829, "dur": 30, "ph": "X", "name": "ReadAsync 1148", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790103865, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790103869, "dur": 50, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790103924, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790103929, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790103983, "dur": 5, "ph": "X", "name": "ProcessMessages 1102", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790103991, "dur": 37, "ph": "X", "name": "ReadAsync 1102", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790104033, "dur": 2, "ph": "X", "name": "ProcessMessages 233", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790104037, "dur": 35, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790104078, "dur": 2, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790104083, "dur": 28, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790104115, "dur": 3, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790104120, "dur": 70, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790104196, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790104206, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790104254, "dur": 2, "ph": "X", "name": "ProcessMessages 807", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790104258, "dur": 31, "ph": "X", "name": "ReadAsync 807", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790104294, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790104300, "dur": 52, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790104356, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790104360, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790104403, "dur": 3, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790104410, "dur": 33, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790104447, "dur": 3, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790104452, "dur": 68, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790104524, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790104528, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790104569, "dur": 3, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790104575, "dur": 48, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790104627, "dur": 2, "ph": "X", "name": "ProcessMessages 920", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790104631, "dur": 32, "ph": "X", "name": "ReadAsync 920", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790104668, "dur": 2, "ph": "X", "name": "ProcessMessages 232", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790104673, "dur": 32, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790104710, "dur": 4, "ph": "X", "name": "ProcessMessages 757", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790104716, "dur": 28, "ph": "X", "name": "ReadAsync 757", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790104749, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790104754, "dur": 66, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790104824, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790104828, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790104878, "dur": 4, "ph": "X", "name": "ProcessMessages 808", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790104886, "dur": 27, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790104916, "dur": 2, "ph": "X", "name": "ProcessMessages 143", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790104922, "dur": 65, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790104991, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790104994, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105037, "dur": 4, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105045, "dur": 52, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105102, "dur": 5, "ph": "X", "name": "ProcessMessages 1204", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105110, "dur": 44, "ph": "X", "name": "ReadAsync 1204", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105159, "dur": 3, "ph": "X", "name": "ProcessMessages 789", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105164, "dur": 30, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105198, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105202, "dur": 48, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105255, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105258, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105298, "dur": 3, "ph": "X", "name": "ProcessMessages 909", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105304, "dur": 29, "ph": "X", "name": "ReadAsync 909", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105337, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105343, "dur": 27, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105373, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105379, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105421, "dur": 2, "ph": "X", "name": "ProcessMessages 935", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105425, "dur": 24, "ph": "X", "name": "ReadAsync 935", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105453, "dur": 2, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105457, "dur": 57, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105521, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105525, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105572, "dur": 3, "ph": "X", "name": "ProcessMessages 853", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105577, "dur": 21, "ph": "X", "name": "ReadAsync 853", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105600, "dur": 1, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105604, "dur": 55, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105663, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105667, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105705, "dur": 2, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105709, "dur": 31, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105744, "dur": 3, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105749, "dur": 34, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105787, "dur": 2, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105791, "dur": 51, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105845, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105848, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105885, "dur": 3, "ph": "X", "name": "ProcessMessages 801", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105890, "dur": 39, "ph": "X", "name": "ReadAsync 801", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105936, "dur": 4, "ph": "X", "name": "ProcessMessages 670", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105942, "dur": 34, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105984, "dur": 3, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790105989, "dur": 30, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106022, "dur": 2, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106026, "dur": 22, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106052, "dur": 2, "ph": "X", "name": "ProcessMessages 234", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106057, "dur": 64, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106126, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106130, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106181, "dur": 3, "ph": "X", "name": "ProcessMessages 799", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106186, "dur": 25, "ph": "X", "name": "ReadAsync 799", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106215, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106219, "dur": 38, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106262, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106288, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106291, "dur": 20, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106314, "dur": 2, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106317, "dur": 23, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106343, "dur": 1, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106346, "dur": 21, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106369, "dur": 20, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106391, "dur": 24, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106417, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106421, "dur": 47, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106470, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106473, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106496, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106498, "dur": 20, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106522, "dur": 4, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106528, "dur": 20, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106552, "dur": 2, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106556, "dur": 34, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106594, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106596, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106629, "dur": 2, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106632, "dur": 33, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106670, "dur": 4, "ph": "X", "name": "ProcessMessages 977", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106678, "dur": 38, "ph": "X", "name": "ReadAsync 977", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106721, "dur": 3, "ph": "X", "name": "ProcessMessages 1190", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106727, "dur": 25, "ph": "X", "name": "ReadAsync 1190", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106757, "dur": 3, "ph": "X", "name": "ProcessMessages 103", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106764, "dur": 34, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106804, "dur": 2, "ph": "X", "name": "ProcessMessages 145", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106809, "dur": 35, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106848, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106852, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106899, "dur": 3, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106905, "dur": 30, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106940, "dur": 4, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790106947, "dur": 52, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107006, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107012, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107067, "dur": 5, "ph": "X", "name": "ProcessMessages 1108", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107075, "dur": 24, "ph": "X", "name": "ReadAsync 1108", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107104, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107108, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107154, "dur": 3, "ph": "X", "name": "ProcessMessages 750", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107160, "dur": 21, "ph": "X", "name": "ReadAsync 750", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107184, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107204, "dur": 30, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107236, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107239, "dur": 27, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107269, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107272, "dur": 30, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107307, "dur": 3, "ph": "X", "name": "ProcessMessages 761", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107312, "dur": 39, "ph": "X", "name": "ReadAsync 761", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107355, "dur": 2, "ph": "X", "name": "ProcessMessages 860", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107358, "dur": 29, "ph": "X", "name": "ReadAsync 860", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107390, "dur": 2, "ph": "X", "name": "ProcessMessages 687", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107394, "dur": 25, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107423, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107427, "dur": 65, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107499, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107504, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107543, "dur": 2, "ph": "X", "name": "ProcessMessages 880", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107547, "dur": 23, "ph": "X", "name": "ReadAsync 880", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107573, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107577, "dur": 48, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107629, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107633, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107673, "dur": 3, "ph": "X", "name": "ProcessMessages 767", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107679, "dur": 29, "ph": "X", "name": "ReadAsync 767", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107712, "dur": 2, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107716, "dur": 75, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107796, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107800, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107842, "dur": 4, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107848, "dur": 29, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107880, "dur": 2, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107884, "dur": 74, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107962, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790107967, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108010, "dur": 4, "ph": "X", "name": "ProcessMessages 799", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108017, "dur": 36, "ph": "X", "name": "ReadAsync 799", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108058, "dur": 4, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108065, "dur": 38, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108107, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108112, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108165, "dur": 4, "ph": "X", "name": "ProcessMessages 1060", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108173, "dur": 41, "ph": "X", "name": "ReadAsync 1060", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108218, "dur": 4, "ph": "X", "name": "ProcessMessages 750", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108224, "dur": 33, "ph": "X", "name": "ReadAsync 750", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108261, "dur": 2, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108265, "dur": 30, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108299, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108304, "dur": 68, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108378, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108384, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108427, "dur": 4, "ph": "X", "name": "ProcessMessages 790", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108434, "dur": 30, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108469, "dur": 3, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108475, "dur": 71, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108550, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108553, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108593, "dur": 2, "ph": "X", "name": "ProcessMessages 837", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108597, "dur": 31, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108632, "dur": 3, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108640, "dur": 47, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108691, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108694, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108719, "dur": 2, "ph": "X", "name": "ProcessMessages 830", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108723, "dur": 19, "ph": "X", "name": "ReadAsync 830", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108746, "dur": 2, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108750, "dur": 17, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108769, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108772, "dur": 60, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108837, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108839, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108863, "dur": 2, "ph": "X", "name": "ProcessMessages 901", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108867, "dur": 21, "ph": "X", "name": "ReadAsync 901", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108893, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108916, "dur": 2, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108920, "dur": 48, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108971, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790108974, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109001, "dur": 2, "ph": "X", "name": "ProcessMessages 684", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109004, "dur": 26, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109037, "dur": 3, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109043, "dur": 57, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109104, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109107, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109152, "dur": 2, "ph": "X", "name": "ProcessMessages 802", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109156, "dur": 27, "ph": "X", "name": "ReadAsync 802", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109187, "dur": 2, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109191, "dur": 38, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109234, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109237, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109281, "dur": 2, "ph": "X", "name": "ProcessMessages 811", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109285, "dur": 27, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109316, "dur": 2, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109321, "dur": 40, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109365, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109368, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109404, "dur": 2, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109409, "dur": 25, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109436, "dur": 2, "ph": "X", "name": "ProcessMessages 979", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109439, "dur": 26, "ph": "X", "name": "ReadAsync 979", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109470, "dur": 2, "ph": "X", "name": "ProcessMessages 250", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109475, "dur": 37, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109515, "dur": 1, "ph": "X", "name": "ProcessMessages 735", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109518, "dur": 29, "ph": "X", "name": "ReadAsync 735", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109552, "dur": 3, "ph": "X", "name": "ProcessMessages 114", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109557, "dur": 28, "ph": "X", "name": "ReadAsync 114", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109588, "dur": 2, "ph": "X", "name": "ProcessMessages 193", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109593, "dur": 45, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109642, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109645, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109688, "dur": 2, "ph": "X", "name": "ProcessMessages 935", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109692, "dur": 25, "ph": "X", "name": "ReadAsync 935", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109720, "dur": 2, "ph": "X", "name": "ProcessMessages 146", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109724, "dur": 42, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109770, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109773, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109814, "dur": 3, "ph": "X", "name": "ProcessMessages 968", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109818, "dur": 28, "ph": "X", "name": "ReadAsync 968", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109851, "dur": 2, "ph": "X", "name": "ProcessMessages 234", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109856, "dur": 36, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109898, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109902, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109951, "dur": 3, "ph": "X", "name": "ProcessMessages 1103", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109956, "dur": 18, "ph": "X", "name": "ReadAsync 1103", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109976, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790109979, "dur": 62, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790110046, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790110090, "dur": 2, "ph": "X", "name": "ProcessMessages 835", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790110094, "dur": 25, "ph": "X", "name": "ReadAsync 835", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790110121, "dur": 3, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790110127, "dur": 46, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790110177, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790110180, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790110234, "dur": 4, "ph": "X", "name": "ProcessMessages 1200", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790110241, "dur": 30, "ph": "X", "name": "ReadAsync 1200", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790110275, "dur": 2, "ph": "X", "name": "ProcessMessages 58", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790110279, "dur": 38, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790110321, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790110324, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790110366, "dur": 3, "ph": "X", "name": "ProcessMessages 780", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790110372, "dur": 49, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790110426, "dur": 3, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790110431, "dur": 46, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790110482, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790110492, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790110545, "dur": 4, "ph": "X", "name": "ProcessMessages 1135", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790110552, "dur": 220, "ph": "X", "name": "ReadAsync 1135", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790110778, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790110784, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790110832, "dur": 3, "ph": "X", "name": "ProcessMessages 751", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790110838, "dur": 31, "ph": "X", "name": "ReadAsync 751", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790110872, "dur": 2, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790110877, "dur": 96, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790110978, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790110982, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790111025, "dur": 3, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790111030, "dur": 35, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790111070, "dur": 4, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790111077, "dur": 35, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790111116, "dur": 3, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790111123, "dur": 120, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790111248, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790111252, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790111299, "dur": 3, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790111305, "dur": 30, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790111341, "dur": 2, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790111345, "dur": 37, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790111388, "dur": 4, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790111396, "dur": 128, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790111529, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790111533, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790111582, "dur": 3, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790111588, "dur": 28, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790111620, "dur": 1, "ph": "X", "name": "ProcessMessages 138", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790111624, "dur": 33, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790111662, "dur": 3, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790111668, "dur": 35, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790111710, "dur": 3, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790111715, "dur": 81, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790111803, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790111807, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790111862, "dur": 4, "ph": "X", "name": "ProcessMessages 964", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790111870, "dur": 33, "ph": "X", "name": "ReadAsync 964", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790111909, "dur": 3, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790111915, "dur": 84, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790112004, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790112010, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790112065, "dur": 4, "ph": "X", "name": "ProcessMessages 963", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790112071, "dur": 33, "ph": "X", "name": "ReadAsync 963", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790112110, "dur": 4, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790112116, "dur": 37, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790112162, "dur": 33, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790112200, "dur": 44, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790112250, "dur": 3, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790112257, "dur": 34, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790112295, "dur": 3, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790112303, "dur": 90, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790112408, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790112413, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790112464, "dur": 4, "ph": "X", "name": "ProcessMessages 978", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790112472, "dur": 24, "ph": "X", "name": "ReadAsync 978", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790112499, "dur": 2, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790112504, "dur": 96, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790112605, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790112609, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790112668, "dur": 4, "ph": "X", "name": "ProcessMessages 1159", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790112676, "dur": 34, "ph": "X", "name": "ReadAsync 1159", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790112714, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790112719, "dur": 56, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790112780, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790112785, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790112831, "dur": 3, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790112837, "dur": 36, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790112878, "dur": 2, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790112883, "dur": 30, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790112917, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790112921, "dur": 36, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790112962, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790112966, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113013, "dur": 2, "ph": "X", "name": "ProcessMessages 829", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113017, "dur": 25, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113045, "dur": 2, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113050, "dur": 60, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113114, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113119, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113149, "dur": 2, "ph": "X", "name": "ProcessMessages 933", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113154, "dur": 30, "ph": "X", "name": "ReadAsync 933", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113189, "dur": 3, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113195, "dur": 34, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113232, "dur": 4, "ph": "X", "name": "ProcessMessages 779", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113238, "dur": 30, "ph": "X", "name": "ReadAsync 779", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113275, "dur": 3, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113280, "dur": 29, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113314, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113318, "dur": 62, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113384, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113387, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113425, "dur": 2, "ph": "X", "name": "ProcessMessages 695", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113430, "dur": 27, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113461, "dur": 3, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113465, "dur": 60, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113530, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113535, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113579, "dur": 4, "ph": "X", "name": "ProcessMessages 1050", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113585, "dur": 50, "ph": "X", "name": "ReadAsync 1050", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113641, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113646, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113694, "dur": 3, "ph": "X", "name": "ProcessMessages 1150", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113699, "dur": 15, "ph": "X", "name": "ReadAsync 1150", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113716, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113718, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113771, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113774, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113813, "dur": 2, "ph": "X", "name": "ProcessMessages 799", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113817, "dur": 18, "ph": "X", "name": "ReadAsync 799", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113837, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113840, "dur": 66, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113909, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113911, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113940, "dur": 2, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113943, "dur": 20, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113968, "dur": 2, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790113973, "dur": 50, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114026, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114028, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114052, "dur": 2, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114056, "dur": 17, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114075, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114078, "dur": 63, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114146, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114150, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114186, "dur": 3, "ph": "X", "name": "ProcessMessages 841", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114191, "dur": 30, "ph": "X", "name": "ReadAsync 841", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114226, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114230, "dur": 69, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114304, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114308, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114355, "dur": 3, "ph": "X", "name": "ProcessMessages 848", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114362, "dur": 38, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114403, "dur": 2, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114407, "dur": 55, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114466, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114469, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114509, "dur": 3, "ph": "X", "name": "ProcessMessages 716", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114515, "dur": 30, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114548, "dur": 2, "ph": "X", "name": "ProcessMessages 746", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114552, "dur": 28, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114585, "dur": 4, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114591, "dur": 32, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114628, "dur": 4, "ph": "X", "name": "ProcessMessages 683", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114635, "dur": 25, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114662, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114665, "dur": 55, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114725, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114731, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114779, "dur": 3, "ph": "X", "name": "ProcessMessages 844", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114784, "dur": 28, "ph": "X", "name": "ReadAsync 844", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114816, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114819, "dur": 50, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114876, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114881, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114921, "dur": 3, "ph": "X", "name": "ProcessMessages 619", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114928, "dur": 31, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114964, "dur": 3, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790114970, "dur": 36, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790115009, "dur": 3, "ph": "X", "name": "ProcessMessages 770", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790115014, "dur": 36, "ph": "X", "name": "ReadAsync 770", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790115055, "dur": 3, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790115060, "dur": 32, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790115097, "dur": 3, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790115102, "dur": 30, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790115136, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790115140, "dur": 34, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790115179, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790115184, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790115230, "dur": 2, "ph": "X", "name": "ProcessMessages 673", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790115235, "dur": 30, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790115271, "dur": 3, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790115277, "dur": 37, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790115318, "dur": 3, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790115323, "dur": 38, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790115368, "dur": 3, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790115374, "dur": 31, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790115409, "dur": 2, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790115413, "dur": 26, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790115444, "dur": 2, "ph": "X", "name": "ProcessMessages 136", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790115449, "dur": 85, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790115538, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790115543, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790115586, "dur": 2, "ph": "X", "name": "ProcessMessages 801", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790115591, "dur": 36, "ph": "X", "name": "ReadAsync 801", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790115631, "dur": 4, "ph": "X", "name": "ProcessMessages 849", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790115639, "dur": 54, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790115698, "dur": 4, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790115705, "dur": 40, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790115750, "dur": 3, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790115757, "dur": 51, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790115812, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790115816, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790115849, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790115854, "dur": 61, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790115920, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790115924, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790115951, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790115956, "dur": 132, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116094, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116098, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116133, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116138, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116162, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116165, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116186, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116193, "dur": 45, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116244, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116248, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116277, "dur": 3, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116282, "dur": 18, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116305, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116312, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116344, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116350, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116385, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116392, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116426, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116432, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116455, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116459, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116486, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116491, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116513, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116518, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116537, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116542, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116564, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116568, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116590, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116594, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116614, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116618, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116636, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116642, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116665, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116671, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116702, "dur": 3, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116708, "dur": 20, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116732, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116736, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116771, "dur": 5, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116781, "dur": 50, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116837, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116841, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116882, "dur": 4, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116888, "dur": 26, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116918, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116921, "dur": 16, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116941, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116945, "dur": 14, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116962, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116968, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116992, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790116996, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117016, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117021, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117039, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117044, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117074, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117079, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117116, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117123, "dur": 25, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117153, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117158, "dur": 40, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117202, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117207, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117244, "dur": 4, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117250, "dur": 30, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117286, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117293, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117333, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117341, "dur": 31, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117377, "dur": 4, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117384, "dur": 29, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117419, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117425, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117461, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117468, "dur": 36, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117509, "dur": 5, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117518, "dur": 36, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117560, "dur": 5, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117569, "dur": 26, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117600, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117606, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117640, "dur": 3, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117646, "dur": 26, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117675, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117680, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117708, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117714, "dur": 26, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117745, "dur": 5, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117753, "dur": 31, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117789, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117795, "dur": 31, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117831, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117837, "dur": 36, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117879, "dur": 5, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117886, "dur": 30, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117922, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117927, "dur": 30, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117962, "dur": 4, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790117970, "dur": 29, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118004, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118011, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118051, "dur": 6, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118059, "dur": 31, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118095, "dur": 3, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118103, "dur": 23, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118130, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118134, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118166, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118172, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118210, "dur": 5, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118217, "dur": 39, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118261, "dur": 5, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118269, "dur": 30, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118304, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118312, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118352, "dur": 3, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118366, "dur": 27, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118397, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118400, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118433, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118438, "dur": 32, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118475, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118482, "dur": 22, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118507, "dur": 2, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118513, "dur": 30, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118550, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118555, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118595, "dur": 3, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118602, "dur": 29, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118636, "dur": 4, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118643, "dur": 32, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118679, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118684, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118723, "dur": 4, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118730, "dur": 28, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118763, "dur": 4, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118769, "dur": 25, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118800, "dur": 5, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118809, "dur": 18, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118830, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118835, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118867, "dur": 6, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118876, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118914, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118920, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118948, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790118954, "dur": 39, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119000, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119006, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119041, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119047, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119087, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119094, "dur": 29, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119130, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119136, "dur": 24, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119167, "dur": 3, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119175, "dur": 30, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119210, "dur": 6, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119219, "dur": 32, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119263, "dur": 5, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119277, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119311, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119318, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119354, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119360, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119394, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119399, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119433, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119439, "dur": 29, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119473, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119479, "dur": 41, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119524, "dur": 3, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119529, "dur": 56, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119589, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119592, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119628, "dur": 5, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119635, "dur": 27, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119666, "dur": 4, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119673, "dur": 29, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119709, "dur": 5, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119718, "dur": 27, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119748, "dur": 5, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119756, "dur": 19, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119777, "dur": 3, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119782, "dur": 22, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119809, "dur": 3, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119816, "dur": 24, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119843, "dur": 4, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119849, "dur": 25, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119879, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119885, "dur": 23, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119912, "dur": 4, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119918, "dur": 26, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119950, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119957, "dur": 27, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119987, "dur": 4, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790119996, "dur": 24, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790120023, "dur": 4, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790120031, "dur": 24, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790120060, "dur": 4, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790120066, "dur": 20, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790120090, "dur": 3, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790120094, "dur": 43, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790120142, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790120146, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790120177, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790120182, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790120206, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790120213, "dur": 5101, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790125322, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790125328, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790125358, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790125363, "dur": 4056, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790129429, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790129438, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790129506, "dur": 4, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790129514, "dur": 1186, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790130708, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790130714, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790130747, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790130752, "dur": 143, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790130903, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790130912, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790130948, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790130953, "dur": 359, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790131317, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790131322, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790131344, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790131348, "dur": 203, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790131557, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790131561, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790131610, "dur": 4, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790131616, "dur": 107, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790131729, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790131733, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790131771, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790131777, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790131796, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790131799, "dur": 19, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790131823, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790131827, "dur": 246, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790132077, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790132081, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790132100, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790132105, "dur": 58, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790132168, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790132173, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790132223, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790132228, "dur": 37, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790132270, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790132276, "dur": 138, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790132419, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790132423, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790132456, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790132460, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790132493, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790132498, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790132525, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790132529, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790132602, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790132606, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790132637, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790132643, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790132665, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790132669, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790132732, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790132737, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790132759, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790132763, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790132792, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790132798, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790132856, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790132861, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790132897, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790132902, "dur": 86, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790132993, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790132997, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790133033, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790133038, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790133068, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790133072, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790133128, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790133132, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790133154, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790133158, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790133220, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790133228, "dur": 95, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790133446, "dur": 5, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790133468, "dur": 102, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790133583, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790133589, "dur": 104, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790133798, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790133884, "dur": 73, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790133987, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790133993, "dur": 76, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790134101, "dur": 6, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790134111, "dur": 279, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790134394, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790134399, "dur": 59, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790134531, "dur": 32, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790134567, "dur": 102, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790134702, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790134709, "dur": 68, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790134782, "dur": 6, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790134795, "dur": 153, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790134981, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790134985, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790135046, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790135052, "dur": 92, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790135154, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790135158, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790135258, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790135264, "dur": 79, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790135461, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790135525, "dur": 63, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790135650, "dur": 27, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790135681, "dur": 70, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790135761, "dur": 11, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790135776, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790135831, "dur": 9, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790135847, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790135890, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790135905, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790136054, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790136072, "dur": 98, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790136181, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790136194, "dur": 96, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790136300, "dur": 10, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790136318, "dur": 178, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790136501, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790136504, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790136582, "dur": 7, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790136593, "dur": 94, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790136692, "dur": 12, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790136724, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790136795, "dur": 5, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790136807, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790136867, "dur": 8, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790136878, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790136926, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790136932, "dur": 70, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790137008, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790137013, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790137053, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790137059, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790137113, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790137118, "dur": 27, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790137149, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790137166, "dur": 67, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790137242, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790137246, "dur": 125, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790137384, "dur": 10, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790137406, "dur": 67, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790137479, "dur": 9, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790137490, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790137554, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790137564, "dur": 442, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790138014, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790138021, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790138065, "dur": 5, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790138072, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790138110, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790138119, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790138163, "dur": 6, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790138171, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790138234, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790138241, "dur": 49, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790138298, "dur": 11, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790138312, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790138356, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790138361, "dur": 40, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790138405, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790138409, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790138456, "dur": 8, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790138467, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790138527, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790138532, "dur": 85, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790138627, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790138666, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790138730, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790138762, "dur": 51, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790138818, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790138823, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790138904, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790138909, "dur": 53, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790138970, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790138986, "dur": 103, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790139094, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790139108, "dur": 88, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790139214, "dur": 7, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790139226, "dur": 296, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790139557, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790139563, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790139655, "dur": 82, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790139750, "dur": 60, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790139814, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790139819, "dur": 64, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790139902, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790139916, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790139965, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790139972, "dur": 162, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790140172, "dur": 8, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790140189, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790140243, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790140261, "dur": 169, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790140435, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790140456, "dur": 178, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790140641, "dur": 8, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790140664, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790140706, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790140710, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790140760, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790140765, "dur": 66, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790140837, "dur": 13, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790140860, "dur": 140, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790141012, "dur": 10, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790141039, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790141088, "dur": 20, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790141139, "dur": 459, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790141606, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790141612, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790141785, "dur": 5, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790141821, "dur": 123, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790141959, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790141969, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790142040, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790142047, "dur": 72, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790142127, "dur": 10, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790142140, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790142184, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790142188, "dur": 73, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790142287, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790142293, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790142374, "dur": 13, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790142399, "dur": 258, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790142673, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790142682, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790142740, "dur": 17, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790142760, "dur": 169, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790142943, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790142949, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790143030, "dur": 36, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790143070, "dur": 163, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790143238, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790143242, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790143301, "dur": 12, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790143321, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790143364, "dur": 6, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790143380, "dur": 105, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790143497, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790143502, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790143573, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790143585, "dur": 103, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790143704, "dur": 11, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790143719, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790143794, "dur": 4, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790143802, "dur": 142, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790143962, "dur": 12, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790143986, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790144056, "dur": 9, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790144067, "dur": 89, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790144166, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790144176, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790144223, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790144232, "dur": 273, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790144531, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790144551, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790144616, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790144631, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790144681, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790144735, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790144792, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790144796, "dur": 449, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790145252, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790145279, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790145328, "dur": 11, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790145354, "dur": 52, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790145422, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790145433, "dur": 668, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790146108, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790146115, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790146170, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790146202, "dur": 56, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790146266, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790146270, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790146316, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790146330, "dur": 316, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790146661, "dur": 12, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790146677, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790146738, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790146742, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790146789, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790146794, "dur": 87, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790146890, "dur": 9, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790146909, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790146954, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790146959, "dur": 462, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790147448, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790147459, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790147523, "dur": 21, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790147548, "dur": 511, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790148072, "dur": 13, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790148100, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790148179, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790148186, "dur": 320, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790148539, "dur": 8, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790148550, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790148659, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790148673, "dur": 41469, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790190159, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790190216, "dur": 231, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790190600, "dur": 353, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790191037, "dur": 7620, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790198852, "dur": 92, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790199075, "dur": 434, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790199642, "dur": 48, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790199823, "dur": 137, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790200064, "dur": 95, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790200164, "dur": 855, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790201941, "dur": 143, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790202241, "dur": 702, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790202951, "dur": 5, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790203007, "dur": 283, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790203375, "dur": 5, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790203469, "dur": 270, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790204099, "dur": 56, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790204351, "dur": 694, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790205097, "dur": 61, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790205238, "dur": 325, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790205707, "dur": 4, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790205715, "dur": 126, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790205991, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790205999, "dur": 185, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790206354, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790206406, "dur": 278, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790206689, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790206747, "dur": 265, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790207180, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790207378, "dur": 134, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790207612, "dur": 91, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790207709, "dur": 576, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790208332, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790208394, "dur": 332, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790209302, "dur": 105, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790209412, "dur": 291, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790209757, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790209823, "dur": 290, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790210118, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790210123, "dur": 362, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790210597, "dur": 159, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790211132, "dur": 111, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790211281, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790211290, "dur": 453, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790211906, "dur": 60, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790211970, "dur": 761, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790212738, "dur": 146, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790212893, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790212936, "dur": 6, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790212985, "dur": 118, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790213144, "dur": 8, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790213191, "dur": 547, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790213932, "dur": 18, "ph": "X", "name": "ProcessMessages 1008", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790214064, "dur": 13999, "ph": "X", "name": "ReadAsync 1008", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790228075, "dur": 19, "ph": "X", "name": "ProcessMessages 1412", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790228098, "dur": 191870, "ph": "X", "name": "ReadAsync 1412", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790419978, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790419983, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790420019, "dur": 2, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 2364, "tid": 25769803776, "ts": 1751384790420023, "dur": 33043, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 2364, "tid": 1828, "ts": 1751384790453705, "dur": 9560, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 2364, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 2364, "tid": 21474836480, "ts": 1751384790061636, "dur": 355044, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 2364, "tid": 21474836480, "ts": 1751384790416682, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 2364, "tid": 21474836480, "ts": 1751384790416686, "dur": 58, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 2364, "tid": 1828, "ts": 1751384790463277, "dur": 26, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 2364, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 2364, "tid": 17179869184, "ts": 1751384790059384, "dur": 393731, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 2364, "tid": 17179869184, "ts": 1751384790059556, "dur": 2018, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 2364, "tid": 17179869184, "ts": 1751384790453122, "dur": 74, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 2364, "tid": 17179869184, "ts": 1751384790453139, "dur": 26, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 2364, "tid": 17179869184, "ts": 1751384790453199, "dur": 2, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 2364, "tid": 1828, "ts": 1751384790463308, "dur": 36, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751384790079372, "dur": 1953, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751384790081335, "dur": 2138, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751384790083613, "dur": 181, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751384790083794, "dur": 611, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751384790085897, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_62645ED13E051483.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751384790089416, "dur": 115, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751384790089552, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751384790089684, "dur": 113, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751384790090322, "dur": 93, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751384790090506, "dur": 179, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.ref.dll_2B244DB205431D5A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751384790090789, "dur": 109, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.ref.dll_0D56696115997497.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751384790090964, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751384790091192, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751384790091791, "dur": 160, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Runtime.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751384790092330, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751384790092727, "dur": 118, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751384790092849, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751384790093275, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751384790094144, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751384790094515, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751384790094591, "dur": 109, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751384790094800, "dur": 259, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751384790095241, "dur": 397, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751384790096988, "dur": 311, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751384790097466, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Common.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751384790097531, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Common.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751384790097731, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751384790097804, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751384790098437, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751384790113411, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751384790113674, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751384790084425, "dur": 33515, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751384790117957, "dur": 303658, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751384790421617, "dur": 192, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751384790421849, "dur": 66, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751384790421994, "dur": 64, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751384790422077, "dur": 26474, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751384790084486, "dur": 33477, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790118051, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_E4B698B849B24AEB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751384790118364, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_47EFA976E901FEC1.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751384790118487, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_17E3136A60FE24E1.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751384790118649, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_D35CED970F7F1F3B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751384790118870, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_4C3484A57A05DA64.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751384790119164, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_E43C351DB48DBD9B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751384790119376, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_C359BB03E2AB95AC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751384790119693, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751384790120156, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790120363, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751384790120622, "dur": 413, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751384790121096, "dur": 203, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Psdimporter.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751384790121340, "dur": 177, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751384790121600, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790121725, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790121821, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751384790122073, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9655524186228112463.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751384790122172, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790122486, "dur": 959, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790123445, "dur": 938, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790124384, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790124677, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790124876, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790125396, "dur": 936, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790126332, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790126529, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790126754, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790127074, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790127305, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790127518, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790127716, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790127932, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790128128, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790128331, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790129044, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790129267, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790129513, "dur": 384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790129897, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790130157, "dur": 341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790130498, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790130715, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790130938, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790131151, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790131405, "dur": 731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790132136, "dur": 691, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790132855, "dur": 629, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790133485, "dur": 1860, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751384790135361, "dur": 282, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751384790135644, "dur": 1041, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751384790136723, "dur": 396, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790137124, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751384790137308, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751384790137593, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790137699, "dur": 2730, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751384790140481, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790140601, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790140762, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751384790140949, "dur": 701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751384790141651, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790141800, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751384790141980, "dur": 794, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751384790142838, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751384790143167, "dur": 973, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751384790144232, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751384790144382, "dur": 1032, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751384790145478, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751384790145667, "dur": 596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751384790146319, "dur": 2488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790148807, "dur": 48857, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790197666, "dur": 3193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751384790200895, "dur": 3254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751384790204150, "dur": 984, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790205139, "dur": 3334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751384790208474, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790208582, "dur": 3142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751384790211724, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790212010, "dur": 3360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751384790215678, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790215788, "dur": 370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751384790216170, "dur": 205446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790084509, "dur": 33463, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790117981, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790118131, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_57E453305C48C0D1.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751384790118235, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790118303, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_1237F680F70DA85A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751384790118398, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_A5FFABD953007C60.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751384790118490, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_5AE8CAD8193F1AE4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751384790118551, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790118607, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_94C8A9F1371998FD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751384790118684, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_D3788CF92C3C660B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751384790118771, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_32AC050EF3A70AF5.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751384790118956, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790119027, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_3CE377B30C500CB9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751384790119182, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790119395, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_F395896E028284AF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751384790119468, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_189352823CD57890.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751384790120093, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751384790120170, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790120364, "dur": 383, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751384790120896, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Common.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751384790120956, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790121060, "dur": 158, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751384790121245, "dur": 372, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751384790121618, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790122200, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790123472, "dur": 1555, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.Binder.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751384790122785, "dur": 3689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790126474, "dur": 476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790126950, "dur": 413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790127364, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790127566, "dur": 881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790128447, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790128661, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790128895, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790129128, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790129344, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790129589, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790129822, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790130097, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790130325, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790130541, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790130761, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790130971, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790131179, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790131411, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790131615, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790132262, "dur": 580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790132843, "dur": 638, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790133484, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751384790133678, "dur": 1524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751384790135203, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790135342, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751384790135672, "dur": 778, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790136456, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751384790136700, "dur": 2646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751384790139346, "dur": 953, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790140363, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751384790140503, "dur": 1103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751384790141664, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751384790141823, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790141914, "dur": 442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751384790142402, "dur": 1956, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790144358, "dur": 3882, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790148241, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751384790148413, "dur": 402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751384790148818, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790148878, "dur": 48781, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790197661, "dur": 4043, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751384790201705, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790201774, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751384790201848, "dur": 4951, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp-firstpass.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751384790206800, "dur": 1194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790208000, "dur": 3774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/NativeFilePicker.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751384790211775, "dur": 710, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751384790212490, "dur": 3641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751384790216173, "dur": 205438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790084536, "dur": 33441, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790117979, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_7A744375D0C40DCD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751384790118267, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_7BCDC5BDF664862C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751384790118328, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790118409, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_5A6241C5E4FBF31E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751384790118698, "dur": 123, "ph": "X", "name": "CheckPristineOutputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_5A6241C5E4FBF31E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751384790118821, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790119047, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_5312197D285F8F1E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751384790119402, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751384790119461, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_B364C0B29F0D0D42.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751384790119667, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751384790119746, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790119839, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751384790119958, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790120063, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790120183, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751384790120366, "dur": 182, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751384790120549, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751384790120629, "dur": 275, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751384790120938, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751384790121048, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790121105, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751384790121246, "dur": 228, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751384790121604, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790122058, "dur": 147, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13226667538729057029.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751384790122206, "dur": 421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790122627, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790123274, "dur": 894, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790124168, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790124431, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790124734, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790124930, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790125203, "dur": 381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790125584, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790125887, "dur": 501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790126388, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790126705, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790126994, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790127303, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790127499, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790127695, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790127893, "dur": 326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790128219, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790128541, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790128788, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790129063, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790129280, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790129531, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790129772, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790129967, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790130187, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790130417, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790130637, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790130857, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790131058, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790131309, "dur": 104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790131413, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790131791, "dur": 59, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790131937, "dur": 926, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790132863, "dur": 627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790133492, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751384790133685, "dur": 578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751384790134317, "dur": 387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751384790134724, "dur": 915, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751384790135639, "dur": 429, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790136073, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751384790136327, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790136387, "dur": 815, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751384790137203, "dur": 320, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790137523, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751384790137644, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Common.Runtime.ref.dll_3DB03597EA04E956.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751384790137700, "dur": 394, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790138102, "dur": 549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751384790138654, "dur": 330, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790139039, "dur": 1136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751384790140227, "dur": 396, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790140623, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790140759, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751384790140929, "dur": 670, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751384790141600, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790141688, "dur": 2674, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790144362, "dur": 4455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790148818, "dur": 48847, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790197667, "dur": 3262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751384790200968, "dur": 2773, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751384790203742, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790203988, "dur": 3521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualEffectGraph.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751384790207510, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790207576, "dur": 3454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751384790211030, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790211128, "dur": 3636, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751384790214765, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790215307, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790215435, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/NativeFilePicker.Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751384790215510, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790215792, "dur": 599, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751384790216402, "dur": 205231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790084559, "dur": 33422, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790117984, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_7B22B0BDD3082718.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751384790118241, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790118432, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_692349F0772110A2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751384790118487, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790118807, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_A5513E9150BC6315.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751384790119026, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790119103, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_A1DAD78AF9F61DC7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751384790119398, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_9FD2F8E91B7883F2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751384790119753, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790120028, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEngineBridge.001.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751384790120208, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790120375, "dur": 188, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEditorBridge.001.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1751384790120854, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790121134, "dur": 350, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1751384790121594, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790121785, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751384790122031, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16873770810471600196.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751384790122188, "dur": 679, "ph": "X", "name": "File", "args": {"detail": "Assets\\Resources\\Scripts\\UI\\CharConfUI.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751384790122169, "dur": 875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790123045, "dur": 803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790123848, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790124142, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790124812, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790125315, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790125620, "dur": 337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790125958, "dur": 911, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790126869, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790127152, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790127404, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790127696, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790127904, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790128235, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790128536, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790128766, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790128997, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790129208, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790129856, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790130062, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790130291, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790130509, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790130713, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790131032, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790131386, "dur": 555, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@0a21eb82d95c\\UnityEditor.TestRunner\\TestRun\\Tasks\\Platform\\PlatformSpecificPostBuildTask.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751384790131386, "dur": 883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790132270, "dur": 561, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790132831, "dur": 658, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790133490, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751384790133672, "dur": 957, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790134633, "dur": 1115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751384790135748, "dur": 337, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790136112, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751384790136405, "dur": 1076, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751384790137503, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751384790137605, "dur": 1611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751384790139217, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790139486, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790139577, "dur": 787, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790140364, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751384790140595, "dur": 2116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751384790142769, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751384790142925, "dur": 1382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751384790144308, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790144419, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751384790144793, "dur": 996, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751384790145862, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751384790146025, "dur": 519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751384790146544, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790146638, "dur": 2153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790148791, "dur": 48851, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790197649, "dur": 3054, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751384790200710, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790200769, "dur": 3611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-csharp.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751384790204410, "dur": 3539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751384790207980, "dur": 3078, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751384790211058, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790211126, "dur": 4112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751384790215279, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Utilities.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751384790215434, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790215523, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790215789, "dur": 370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751384790216179, "dur": 205434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790084590, "dur": 33397, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790117990, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_89517EAC2A874601.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751384790118260, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790118534, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_E3AA45DCF433BC79.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751384790118709, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_366576C77F483F0B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751384790118788, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_41563AEB7BF951B8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751384790118979, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_B9FBC03C59DCE731.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751384790119076, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_A0847C1FE8DF0FE2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751384790119148, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790119433, "dur": 177, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751384790119676, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790119997, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEngineBridge.001.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1751384790120132, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751384790120288, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790120373, "dur": 241, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751384790120631, "dur": 263, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1751384790120911, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751384790121023, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790121147, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751384790121615, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790122188, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790123357, "dur": 684, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751384790124041, "dur": 941, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Numerics.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751384790122470, "dur": 3037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790125508, "dur": 930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790126438, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790126771, "dur": 715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790127486, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790127684, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790127883, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790128561, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790128817, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790129036, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790129602, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790129840, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790130058, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790130299, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790130519, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790130736, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790130976, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790131187, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790131399, "dur": 861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790132260, "dur": 572, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790132832, "dur": 654, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790133487, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751384790133680, "dur": 530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751384790134210, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790134303, "dur": 986, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751384790135339, "dur": 829, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751384790136168, "dur": 906, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790137079, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751384790137412, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity-editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751384790137609, "dur": 1476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity-editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751384790139086, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790139211, "dur": 791, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751384790140003, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790140282, "dur": 333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790140615, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790140771, "dur": 2087, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790142859, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751384790143040, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790143132, "dur": 514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751384790143702, "dur": 551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751384790144292, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790144534, "dur": 478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751384790145043, "dur": 503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751384790145608, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790145666, "dur": 3129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790148795, "dur": 48853, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790197649, "dur": 2675, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751384790200325, "dur": 783, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790201115, "dur": 2888, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751384790204046, "dur": 2589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751384790206636, "dur": 435, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790207076, "dur": 2836, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751384790209913, "dur": 1828, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790211746, "dur": 3321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751384790215269, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790215431, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790215594, "dur": 183, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751384790215794, "dur": 710, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751384790216519, "dur": 205101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790084615, "dur": 33379, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790117995, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_3122CFBB2DC47329.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751384790118278, "dur": 53, "ph": "X", "name": "CheckPristineOutputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_3122CFBB2DC47329.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751384790118539, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_B5214F7A6C172998.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751384790118809, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_EFD593047BD2E6A8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751384790119365, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4228628CD1B97490.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751384790119434, "dur": 156, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4228628CD1B97490.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751384790119656, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751384790119712, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790120025, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751384790120180, "dur": 241, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751384790120469, "dur": 158, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751384790120644, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751384790120774, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790120850, "dur": 374, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Path.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751384790121445, "dur": 178, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751384790121624, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790122196, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790122474, "dur": 1338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790123813, "dur": 1860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790125674, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790125892, "dur": 1342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790127362, "dur": 545, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Manipulators\\Move\\MoveItemModeReplace.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751384790127235, "dur": 1008, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790128244, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790128677, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790128917, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790129139, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790129353, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790129579, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790129787, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790130226, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790130434, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790130638, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790130858, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790131066, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790131455, "dur": 691, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@0a21eb82d95c\\UnityEditor.TestRunner\\TestRunner\\Callbacks\\WindowResultUpdaterDataHolder.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751384790131333, "dur": 918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790132251, "dur": 585, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790132836, "dur": 652, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790133489, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751384790133692, "dur": 569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751384790134302, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790134400, "dur": 1038, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751384790135439, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790135627, "dur": 1099, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751384790136726, "dur": 395, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790137133, "dur": 372, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790137505, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InternalAPIEditorBridge.001.ref.dll_D0DDFD189AE4D28B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751384790137705, "dur": 524, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790138233, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751384790138586, "dur": 1755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751384790140420, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751384790140617, "dur": 529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751384790141147, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790141219, "dur": 3055, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790144275, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751384790144426, "dur": 737, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751384790145163, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790145373, "dur": 3425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790148799, "dur": 48844, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790197644, "dur": 2872, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751384790200516, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790200824, "dur": 2819, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751384790203648, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790203744, "dur": 2640, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751384790206384, "dur": 556, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790206947, "dur": 3477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751384790210424, "dur": 327, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790210757, "dur": 2988, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751384790213746, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751384790213928, "dur": 2439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751384790216399, "dur": 205237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790084636, "dur": 33362, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790118001, "dur": 844, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_98CF6329D3D6F9AB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751384790118907, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_099E8E7D5E4735AD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751384790119263, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_39A3DCC912A474A9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751384790119370, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_D9468BE09286D288.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751384790119436, "dur": 190, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_D9468BE09286D288.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751384790119663, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790120042, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790120111, "dur": 302, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751384790120490, "dur": 486, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751384790121006, "dur": 165, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751384790121202, "dur": 188, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751384790121441, "dur": 153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751384790121615, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790121844, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751384790122185, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790122422, "dur": 1622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790124207, "dur": 678, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@24b10291b18f\\Editor\\TMP\\TMP_InputFieldEditor.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751384790124044, "dur": 1389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790125433, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790125804, "dur": 1588, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@ba0fe1b085e9\\Editor\\Models\\Operators\\Implementations\\BitwiseXor.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751384790125630, "dur": 1763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790127393, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790127584, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790127794, "dur": 406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790128201, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790128469, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790128694, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790128923, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790129150, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790129359, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790129593, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790129820, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790130039, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790130259, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790130479, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790130688, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790130914, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790131129, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790131360, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790131558, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790132407, "dur": 430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790132837, "dur": 655, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790133493, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751384790133690, "dur": 881, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790134575, "dur": 1862, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751384790136487, "dur": 532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751384790137020, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790137126, "dur": 1834, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751384790138960, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790139194, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ForUI.ref.dll_CAAF5CDDE611F3DD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751384790139257, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790139440, "dur": 860, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790140346, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790140613, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790140801, "dur": 3556, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790144357, "dur": 1722, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790146080, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751384790146239, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790146315, "dur": 511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751384790146872, "dur": 1922, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790148794, "dur": 49080, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790197875, "dur": 3517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751384790201393, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790201626, "dur": 2914, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751384790204572, "dur": 2756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/OutlineFx.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751384790207329, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751384790207416, "dur": 3347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751384790210798, "dur": 2844, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751384790213667, "dur": 2644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751384790216344, "dur": 205280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790084653, "dur": 33360, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790118015, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_AAD14E71D524EA60.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751384790118301, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_966AC74B818A5F90.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751384790118415, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_B10B806206A4C9C0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751384790118802, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_E951570DA9D21231.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751384790119035, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_E951570DA9D21231.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751384790119355, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790119413, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.dll_A6D8EBBB0C3090A7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751384790119997, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751384790120203, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751384790120505, "dur": 183, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751384790120862, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751384790121012, "dur": 167, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751384790121199, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751384790121342, "dur": 181, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751384790121594, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790121882, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790122193, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790122389, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790122552, "dur": 856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790123408, "dur": 1311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790124719, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790124918, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790125120, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790125394, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790125683, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790125901, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790126538, "dur": 339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790126877, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790127207, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790127426, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790127616, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790127808, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790128022, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790128200, "dur": 360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790128561, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790128813, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790129038, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790129251, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790129529, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790129761, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790130005, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790130221, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790130795, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790130999, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790131211, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790131785, "dur": 68, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790131935, "dur": 902, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790132837, "dur": 838, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790133676, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751384790133903, "dur": 601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751384790134504, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790134578, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751384790134974, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751384790135167, "dur": 1384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751384790136552, "dur": 858, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790137415, "dur": 495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751384790137928, "dur": 1076, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751384790139005, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790139197, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751384790139410, "dur": 813, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751384790140248, "dur": 331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790140622, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790140761, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751384790140957, "dur": 680, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751384790141725, "dur": 2507, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790144233, "dur": 527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751384790144778, "dur": 1217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751384790146075, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751384790146257, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751384790146791, "dur": 2012, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790148803, "dur": 48836, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790197642, "dur": 3225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751384790200900, "dur": 4003, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751384790204904, "dur": 571, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790205480, "dur": 3387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751384790208868, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790209172, "dur": 2815, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751384790211988, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790212064, "dur": 3446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751384790215557, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751384790215828, "dur": 205784, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751384790084679, "dur": 33338, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751384790118020, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_D07FFB3B2770754A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751384790118540, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751384790118601, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_636C45FB8E78E9FC.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751384790118697, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_88313D863DE50351.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751384790118874, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751384790118976, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_C3115380BB649476.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751384790119072, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_BA31E0AD397170B1.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751384790119178, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751384790119259, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_62645ED13E051483.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751384790119708, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751384790119850, "dur": 9544, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751384790129475, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751384790129734, "dur": 3039, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751384790132855, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751384790133013, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751384790133479, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751384790133645, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751384790133925, "dur": 792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751384790134774, "dur": 523, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751384790135337, "dur": 1058, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751384790136467, "dur": 583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751384790137062, "dur": 1486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751384790138588, "dur": 380, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751384790138974, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751384790139352, "dur": 817, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751384790140202, "dur": 391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751384790140593, "dur": 212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751384790140805, "dur": 3561, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751384790144367, "dur": 4444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751384790148812, "dur": 48837, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751384790197650, "dur": 2574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751384790200243, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751384790200314, "dur": 2951, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751384790203266, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751384790203328, "dur": 3559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751384790206888, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751384790207054, "dur": 2904, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751384790209958, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751384790210068, "dur": 3058, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751384790213156, "dur": 2932, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751384790216089, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751384790216168, "dur": 205453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790084699, "dur": 33325, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790118027, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_21E5C8D6C2CA360E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751384790118231, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790118297, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_24CD4430EA38EE97.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751384790118532, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790118848, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_34D7E2BEC34818D5.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751384790118959, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790119054, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_7D6DB1EEB95EE9BC.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751384790119171, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_7D6DB1EEB95EE9BC.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751384790119271, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_AFF4160AB2736538.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751384790119384, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790119685, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751384790120172, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790120316, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751384790120478, "dur": 482, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751384790120962, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790121104, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1751384790121244, "dur": 369, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1751384790121614, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790122198, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790122407, "dur": 1423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790123830, "dur": 152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790123982, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790124186, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790124491, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790124725, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790125016, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790125301, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790125545, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790125734, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790125961, "dur": 590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790126552, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790126755, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790127011, "dur": 407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790127418, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790127619, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790127815, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790128024, "dur": 368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790128392, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790128649, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790128889, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790129111, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790129426, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790129646, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790129896, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790130323, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790130547, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790130771, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790130984, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790131183, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790131448, "dur": 891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790132339, "dur": 490, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790132861, "dur": 633, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790133494, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751384790133683, "dur": 478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751384790134221, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751384790134354, "dur": 1290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751384790135711, "dur": 810, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751384790136521, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790136666, "dur": 680, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751384790137346, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790137612, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790137774, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751384790137962, "dur": 1151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751384790139144, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751384790139393, "dur": 1027, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751384790140472, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790140621, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790140816, "dur": 3718, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790144534, "dur": 4247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790148791, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751384790149010, "dur": 48651, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790197662, "dur": 3373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751384790201083, "dur": 3174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751384790204258, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790204346, "dur": 3382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751384790207729, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790207796, "dur": 4500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751384790212297, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790212390, "dur": 3252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751384790215693, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790215791, "dur": 547, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751384790216359, "dur": 205266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751384790084727, "dur": 33301, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751384790118031, "dur": 388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_D5DC31B4B0DA901E.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751384790118491, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_299E64FBD7A1C295.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751384790118671, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_A01C5835F2B3673A.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751384790118794, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_DA3F53EC74158B55.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751384790118970, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751384790119034, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_DA3F53EC74158B55.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751384790119238, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_5099CB98C03D5F98.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751384790119327, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_0289380923B705C1.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751384790119665, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1751384790119737, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751384790120016, "dur": 683, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751384790120702, "dur": 6680, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751384790127473, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751384790127669, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751384790127869, "dur": 530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751384790128399, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751384790128619, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751384790128877, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751384790129102, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751384790129316, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751384790129552, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751384790129784, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751384790130013, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751384790130230, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751384790130449, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751384790130665, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751384790130886, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751384790131087, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751384790131331, "dur": 886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751384790132217, "dur": 643, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751384790132860, "dur": 642, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751384790133503, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751384790133700, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751384790134209, "dur": 322, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751384790134537, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751384790134764, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751384790135001, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751384790135165, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751384790135435, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751384790135514, "dur": 1035, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751384790136549, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751384790136771, "dur": 1321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751384790138093, "dur": 892, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751384790139103, "dur": 976, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751384790140097, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751384790140579, "dur": 1101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 11, "ts": 1751384790141742, "dur": 166, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751384790142277, "dur": 49840, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 11, "ts": 1751384790197639, "dur": 2186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751384790199826, "dur": 565, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751384790200398, "dur": 2684, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751384790203130, "dur": 2128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751384790205313, "dur": 2208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751384790207555, "dur": 2261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751384790209858, "dur": 2345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751384790212207, "dur": 548, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751384790212760, "dur": 2230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751384790215100, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751384790215634, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.SpriteShape.Editor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751384790215796, "dur": 754, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751384790216567, "dur": 205051, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790084749, "dur": 33284, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790118035, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Apple.Extensions.Common.dll_D4E020400338F5BB.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751384790118290, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_D8154579280ABE30.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751384790118641, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_5C2FAC469D562E01.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751384790118800, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_15728F368AD157F8.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751384790119154, "dur": 247, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_4AAF7BB67CBB0CA5.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751384790119413, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_43BFE21499172947.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751384790119463, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790119883, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751384790120002, "dur": 571, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751384790120597, "dur": 371, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1751384790120969, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790121142, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1751384790121611, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790121822, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751384790122185, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790122467, "dur": 2119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790124586, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790124807, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790125088, "dur": 754, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790125842, "dur": 519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790126361, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790126668, "dur": 579, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.animation@494a3b4e73a9\\Editor\\SkinningModule\\VisibilityTool\\BoneReparentTool.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751384790127362, "dur": 547, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.animation@494a3b4e73a9\\Editor\\SkinningModule\\UI\\PoseToolbar.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751384790126668, "dur": 1295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790127963, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790128258, "dur": 379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790128637, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790128853, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790129110, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790129318, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790129555, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790129773, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790130011, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790130222, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790130451, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790130669, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790130882, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790131093, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790131404, "dur": 722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790132134, "dur": 699, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790132834, "dur": 649, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790133485, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751384790133653, "dur": 943, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790134599, "dur": 1799, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751384790136398, "dur": 357, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790136760, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_F120A7CC9CFB2CF2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751384790136931, "dur": 325, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790137263, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751384790137430, "dur": 161, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751384790137592, "dur": 1230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751384790138823, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790139192, "dur": 420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/ScriptablePacker.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751384790139629, "dur": 777, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/ScriptablePacker.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751384790140443, "dur": 169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790140612, "dur": 167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790140779, "dur": 3581, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790144361, "dur": 4435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790148796, "dur": 48907, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790197704, "dur": 3060, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/NativeFilePicker.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751384790200765, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790200848, "dur": 3120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751384790203969, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790204037, "dur": 2963, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-unity.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751384790207033, "dur": 3188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751384790210222, "dur": 435, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790210662, "dur": 3552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751384790214215, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751384790214275, "dur": 2254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/ScriptablePacker.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751384790216557, "dur": 205073, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790084775, "dur": 33357, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790118133, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_FB049F22F4C5497A.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751384790118232, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790118295, "dur": 544, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_24AD401155842AFB.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751384790118839, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790118959, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_239A276499DBD1EB.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751384790119184, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_26C00F9C5DF6EF10.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751384790119449, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_A474E787AE0D7A75.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751384790119683, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1751384790119815, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790119951, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751384790120020, "dur": 549, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751384790120628, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1751384790120956, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790121283, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751384790121425, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751384790121610, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790121941, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790122104, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1347864975016614161.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751384790122193, "dur": 625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790122818, "dur": 1825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790124644, "dur": 340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790124984, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790125431, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790125830, "dur": 639, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@ba0fe1b085e9\\Editor\\Models\\Operators\\Implementations\\Absolute.cs"}}, {"pid": 12345, "tid": 13, "ts": 1751384790125664, "dur": 885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790126550, "dur": 166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790126722, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790126993, "dur": 398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790127392, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790127620, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790127822, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790128056, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790128240, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790128522, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790128733, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790128962, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790129184, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790129415, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790129651, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790129874, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790130117, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790130330, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790130539, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790130758, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790130985, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790131226, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790131417, "dur": 930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790132347, "dur": 495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790132842, "dur": 830, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790133673, "dur": 1142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751384790134849, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751384790135136, "dur": 1526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751384790136663, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790136842, "dur": 1166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751384790138009, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790138279, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790138363, "dur": 535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751384790138899, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790139202, "dur": 688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751384790139891, "dur": 397, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790140317, "dur": 297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790140614, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790140775, "dur": 2122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790142898, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751384790143114, "dur": 904, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751384790144076, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790144359, "dur": 4431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790148791, "dur": 48853, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790197647, "dur": 2539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/OutlineFx.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751384790200187, "dur": 342, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790200537, "dur": 3978, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751384790204516, "dur": 440, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790204993, "dur": 2884, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751384790207879, "dur": 1401, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790209284, "dur": 3311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751384790212595, "dur": 2103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790214821, "dur": 350, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790215483, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790215738, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751384790215809, "dur": 205805, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790084794, "dur": 33319, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790118115, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_BA35B04A057142E1.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751384790118839, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_B70C9B9527050002.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751384790118957, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790119080, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_4DE7717F601CC2DC.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751384790119266, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790119429, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_4DE7717F601CC2DC.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751384790120105, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1751384790120389, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEditorBridge.001.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751384790120457, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790120599, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751384790120886, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790120998, "dur": 154, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751384790121203, "dur": 325, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1751384790121530, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Psdimporter.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751384790121599, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790121744, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751384790121843, "dur": 417, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1751384790122261, "dur": 400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790123570, "dur": 1172, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll"}}, {"pid": 12345, "tid": 14, "ts": 1751384790122661, "dur": 2490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790125151, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790125951, "dur": 1360, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@ba0fe1b085e9\\Editor\\Models\\Blocks\\Implementations\\Spawn\\VFXSpawnerSetAttribute.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751384790125876, "dur": 1605, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790127482, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790127681, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790127891, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790128103, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790128332, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790128547, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790128764, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790128988, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790129203, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790129523, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790129746, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790129976, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790130187, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790130413, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790130643, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790131075, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790131474, "dur": 757, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@0a21eb82d95c\\UnityEditor.TestRunner\\UnityTestProtocol\\Messages\\PlayerSettingsMessage.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751384790131329, "dur": 1036, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790132365, "dur": 470, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790132836, "dur": 840, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790133677, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751384790133848, "dur": 682, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751384790134541, "dur": 698, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790135266, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751384790135518, "dur": 1230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751384790136793, "dur": 986, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751384790137779, "dur": 879, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790138667, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790138794, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751384790139113, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751384790139368, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790139429, "dur": 668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751384790140154, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790140323, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790140617, "dur": 179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790140796, "dur": 3565, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790144362, "dur": 4417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790148782, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751384790149032, "dur": 48614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790197649, "dur": 3457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751384790201106, "dur": 365, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790201477, "dur": 3573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751384790205051, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790205186, "dur": 2858, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualEffectGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751384790208099, "dur": 2861, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751384790211003, "dur": 2956, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751384790213960, "dur": 1361, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790215327, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790215804, "dur": 203057, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751384790420381, "dur": 189, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 14, "ts": 1751384790420570, "dur": 963, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 14, "ts": 1751384790421533, "dur": 57, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 14, "ts": 1751384790418862, "dur": 2731, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751384790084814, "dur": 33302, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751384790118119, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_8AA31634E5830E2F.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751384790118254, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751384790118311, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_F01F6C04D397C746.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751384790118404, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_07ED47E0EFE03F3D.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751384790118679, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_847E2D6319A6BEA0.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751384790119034, "dur": 192, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_847E2D6319A6BEA0.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751384790119417, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_B682C92DAD489923.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751384790119826, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1751384790119972, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751384790120257, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751384790120458, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751384790120592, "dur": 274, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1751384790120935, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751384790121004, "dur": 270, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/PsdPlugin.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751384790121347, "dur": 190, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751384790121597, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751384790121737, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity-editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1751384790121976, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12044039279616155683.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751384790122174, "dur": 151, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9680343299601325691.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751384790122325, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751384790123363, "dur": 2123, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 15, "ts": 1751384790122621, "dur": 3323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751384790126453, "dur": 1251, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@ba0fe1b085e9\\Editor\\GraphView\\Views\\Properties\\NumericPropertiesRM.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751384790125945, "dur": 1891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751384790127837, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751384790128140, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751384790128443, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751384790128679, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751384790128938, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751384790129170, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751384790129397, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751384790129624, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751384790129842, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751384790130079, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751384790130327, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751384790130545, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751384790130775, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751384790130982, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751384790131413, "dur": 956, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751384790132369, "dur": 471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751384790132840, "dur": 640, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751384790133481, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751384790133669, "dur": 2098, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751384790135835, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751384790135969, "dur": 369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751384790136339, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751384790136657, "dur": 2280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751384790138938, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751384790139101, "dur": 376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751384790139477, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751384790139584, "dur": 1099, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751384790140758, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751384790140959, "dur": 721, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751384790141738, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751384790141900, "dur": 906, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751384790142807, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751384790142887, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751384790143155, "dur": 2420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751384790145579, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751384790145664, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751384790145808, "dur": 1502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751384790147364, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751384790147472, "dur": 708, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751384790148239, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751384790148363, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751384790148779, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751384790149002, "dur": 459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751384790149501, "dur": 602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751384790150143, "dur": 444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751384790150623, "dur": 47034, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751384790197659, "dur": 2981, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751384790200641, "dur": 413, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751384790201062, "dur": 3290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751384790204392, "dur": 3185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751384790207577, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751384790207691, "dur": 3011, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751384790210702, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751384790210787, "dur": 3204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751384790213992, "dur": 332, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751384790214329, "dur": 2153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751384790216511, "dur": 205120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790084845, "dur": 33278, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790118125, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_7D19F4582D6FA60D.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751384790118293, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_385F174668EB898F.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751384790118384, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_05122CAABA8E3B59.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751384790118463, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_F48E27B3AC5122EF.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751384790118635, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_FA0E2521CCF3B353.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751384790118872, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_B72398FA1A1CE63D.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751384790119067, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_9B331AB6767E3EC1.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751384790119149, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_E496E41FECA98925.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751384790119440, "dur": 192, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751384790119635, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751384790119813, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751384790119981, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1751384790120091, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1751384790120226, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751384790120465, "dur": 148, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751384790120761, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751384790120893, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790121015, "dur": 172, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/PsdPlugin.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751384790121206, "dur": 212, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1751384790121450, "dur": 176, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751384790121628, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790121781, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1751384790122167, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790122394, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790123102, "dur": 934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790124036, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790124552, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790124851, "dur": 375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790125226, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790125450, "dur": 572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790126023, "dur": 461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790126484, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790126657, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790126836, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790127003, "dur": 157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790127160, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790127350, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790127544, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790127736, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790128053, "dur": 424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790128477, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790128686, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790128919, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790129140, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790129346, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790129638, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790129867, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790130091, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790130295, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790130510, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790130753, "dur": 773, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@920b46832575\\InputSystem\\Actions\\InputBinding.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751384790130713, "dur": 1230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790131943, "dur": 890, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790132833, "dur": 842, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790133675, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751384790133793, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790133849, "dur": 682, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751384790134585, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751384790134818, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790134896, "dur": 1292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751384790136189, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790136267, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751384790136477, "dur": 1678, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751384790138155, "dur": 932, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790139087, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751384790139265, "dur": 307, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790139631, "dur": 966, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790140597, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790140768, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751384790141037, "dur": 840, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751384790141877, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790142058, "dur": 2313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790144371, "dur": 4436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790148808, "dur": 48854, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790197664, "dur": 2697, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751384790200362, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790200510, "dur": 3635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751384790204145, "dur": 536, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790204687, "dur": 3084, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751384790207771, "dur": 457, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790208233, "dur": 2763, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751384790210997, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790211067, "dur": 3420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-unity-editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751384790214488, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790214757, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790215311, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751384790215536, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.PixelPerfect.Editor.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1751384790215811, "dur": 205808, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751384790452921, "dur": 1871, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 2364, "tid": 1828, "ts": 1751384790463385, "dur": 41, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 2364, "tid": 1828, "ts": 1751384790463488, "dur": 1105, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 2364, "tid": 1828, "ts": 1751384790453627, "dur": 11009, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}