{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 2364, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 2364, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 2364, "tid": 9, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 2364, "tid": 9, "ts": 1751371057450021, "dur": 779, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 2364, "tid": 9, "ts": 1751371057453343, "dur": 698, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 2364, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 2364, "tid": 1, "ts": 1751371056837243, "dur": 10414, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 2364, "tid": 1, "ts": 1751371056847663, "dur": 103748, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 2364, "tid": 1, "ts": 1751371056951427, "dur": 361938, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 2364, "tid": 9, "ts": 1751371057454047, "dur": 970, "ph": "X", "name": "", "args": {}}, {"pid": 2364, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056835403, "dur": 10196, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056845603, "dur": 595336, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056846420, "dur": 2289, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056848717, "dur": 659, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056849383, "dur": 9128, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056858521, "dur": 198, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056858726, "dur": 116, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056858855, "dur": 548, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056859408, "dur": 7703, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056867133, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056867139, "dur": 208, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056867353, "dur": 583, "ph": "X", "name": "ProcessMessages 3051", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056867940, "dur": 306, "ph": "X", "name": "ReadAsync 3051", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056868255, "dur": 20, "ph": "X", "name": "ProcessMessages 16783", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056868277, "dur": 177, "ph": "X", "name": "ReadAsync 16783", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056868460, "dur": 6, "ph": "X", "name": "ProcessMessages 3567", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056868468, "dur": 77, "ph": "X", "name": "ReadAsync 3567", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056868547, "dur": 2, "ph": "X", "name": "ProcessMessages 1361", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056868551, "dur": 79, "ph": "X", "name": "ReadAsync 1361", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056868637, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056868640, "dur": 159, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056868805, "dur": 5, "ph": "X", "name": "ProcessMessages 2977", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056868813, "dur": 47, "ph": "X", "name": "ReadAsync 2977", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056868861, "dur": 3, "ph": "X", "name": "ProcessMessages 2131", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056868867, "dur": 148, "ph": "X", "name": "ReadAsync 2131", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056869021, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056869024, "dur": 194, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056869229, "dur": 6, "ph": "X", "name": "ProcessMessages 3088", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056869237, "dur": 152, "ph": "X", "name": "ReadAsync 3088", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056869395, "dur": 5, "ph": "X", "name": "ProcessMessages 2336", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056869402, "dur": 55, "ph": "X", "name": "ReadAsync 2336", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056869459, "dur": 3, "ph": "X", "name": "ProcessMessages 2430", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056869464, "dur": 89, "ph": "X", "name": "ReadAsync 2430", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056869558, "dur": 283, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056869847, "dur": 4, "ph": "X", "name": "ProcessMessages 1861", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056869853, "dur": 365, "ph": "X", "name": "ReadAsync 1861", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056870222, "dur": 6, "ph": "X", "name": "ProcessMessages 4907", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056870230, "dur": 104, "ph": "X", "name": "ReadAsync 4907", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056870355, "dur": 14, "ph": "X", "name": "ProcessMessages 4967", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056870372, "dur": 101, "ph": "X", "name": "ReadAsync 4967", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056870475, "dur": 2, "ph": "X", "name": "ProcessMessages 999", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056870479, "dur": 41, "ph": "X", "name": "ReadAsync 999", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056870522, "dur": 2, "ph": "X", "name": "ProcessMessages 1675", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056870525, "dur": 56, "ph": "X", "name": "ReadAsync 1675", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056870584, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056870586, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056870659, "dur": 1, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056870661, "dur": 22, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056870692, "dur": 2, "ph": "X", "name": "ProcessMessages 1215", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056870694, "dur": 82, "ph": "X", "name": "ReadAsync 1215", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056870781, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056870826, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056870829, "dur": 63, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056870899, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056870901, "dur": 37, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056870941, "dur": 2, "ph": "X", "name": "ProcessMessages 1495", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056870946, "dur": 27, "ph": "X", "name": "ReadAsync 1495", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056870975, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056870978, "dur": 20, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871000, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871002, "dur": 22, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871029, "dur": 7, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871038, "dur": 17, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871057, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871060, "dur": 25, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871087, "dur": 1, "ph": "X", "name": "ProcessMessages 179", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871090, "dur": 18, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871110, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871113, "dur": 14, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871128, "dur": 1, "ph": "X", "name": "ProcessMessages 251", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871130, "dur": 16, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871148, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871150, "dur": 29, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871182, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871184, "dur": 23, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871210, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871212, "dur": 21, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871235, "dur": 1, "ph": "X", "name": "ProcessMessages 863", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871238, "dur": 95, "ph": "X", "name": "ReadAsync 863", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871342, "dur": 32, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871377, "dur": 2, "ph": "X", "name": "ProcessMessages 1036", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871381, "dur": 79, "ph": "X", "name": "ReadAsync 1036", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871474, "dur": 32, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871509, "dur": 2, "ph": "X", "name": "ProcessMessages 1028", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871512, "dur": 53, "ph": "X", "name": "ReadAsync 1028", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871569, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871599, "dur": 1, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871602, "dur": 66, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871676, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871678, "dur": 36, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871717, "dur": 2, "ph": "X", "name": "ProcessMessages 1748", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871720, "dur": 51, "ph": "X", "name": "ReadAsync 1748", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871776, "dur": 25, "ph": "X", "name": "ReadAsync 106", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871804, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871806, "dur": 23, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871832, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871835, "dur": 28, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871865, "dur": 1, "ph": "X", "name": "ProcessMessages 918", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871868, "dur": 25, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871895, "dur": 1, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871897, "dur": 24, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871923, "dur": 1, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871926, "dur": 24, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871952, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871954, "dur": 25, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871983, "dur": 1, "ph": "X", "name": "ProcessMessages 699", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056871986, "dur": 18, "ph": "X", "name": "ReadAsync 699", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872006, "dur": 1, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872008, "dur": 66, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872078, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872104, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872107, "dur": 21, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872131, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872133, "dur": 19, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872154, "dur": 1, "ph": "X", "name": "ProcessMessages 707", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872156, "dur": 57, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872216, "dur": 2, "ph": "X", "name": "ProcessMessages 905", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872219, "dur": 13, "ph": "X", "name": "ReadAsync 905", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872234, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872237, "dur": 23, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872264, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872285, "dur": 1, "ph": "X", "name": "ProcessMessages 762", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872287, "dur": 15, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872305, "dur": 1, "ph": "X", "name": "ProcessMessages 275", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872307, "dur": 20, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872332, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872334, "dur": 14, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872350, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872353, "dur": 24, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872380, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872383, "dur": 28, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872413, "dur": 2, "ph": "X", "name": "ProcessMessages 869", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872416, "dur": 57, "ph": "X", "name": "ReadAsync 869", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872478, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872499, "dur": 1, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872502, "dur": 14, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872518, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872520, "dur": 32, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872557, "dur": 21, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872580, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872583, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872610, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872613, "dur": 72, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872692, "dur": 2, "ph": "X", "name": "ProcessMessages 747", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872695, "dur": 36, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872736, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872762, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872764, "dur": 21, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872788, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872790, "dur": 30, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872822, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872825, "dur": 28, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872855, "dur": 1, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872857, "dur": 28, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872888, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872891, "dur": 24, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872916, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872919, "dur": 22, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872943, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872945, "dur": 27, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872975, "dur": 1, "ph": "X", "name": "ProcessMessages 894", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056872977, "dur": 27, "ph": "X", "name": "ReadAsync 894", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873007, "dur": 1, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873010, "dur": 25, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873037, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873040, "dur": 124, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873169, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873199, "dur": 1, "ph": "X", "name": "ProcessMessages 789", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873202, "dur": 29, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873233, "dur": 1, "ph": "X", "name": "ProcessMessages 707", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873236, "dur": 26, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873264, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873267, "dur": 29, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873298, "dur": 1, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873301, "dur": 20, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873323, "dur": 1, "ph": "X", "name": "ProcessMessages 734", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873326, "dur": 18, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873346, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873349, "dur": 18, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873369, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873371, "dur": 24, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873398, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873400, "dur": 32, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873434, "dur": 1, "ph": "X", "name": "ProcessMessages 703", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873437, "dur": 29, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873468, "dur": 1, "ph": "X", "name": "ProcessMessages 880", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873471, "dur": 26, "ph": "X", "name": "ReadAsync 880", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873499, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873501, "dur": 20, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873524, "dur": 1, "ph": "X", "name": "ProcessMessages 77", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873526, "dur": 29, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873557, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873560, "dur": 34, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873596, "dur": 1, "ph": "X", "name": "ProcessMessages 804", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873599, "dur": 21, "ph": "X", "name": "ReadAsync 804", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873623, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873625, "dur": 70, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873702, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873705, "dur": 37, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873744, "dur": 3, "ph": "X", "name": "ProcessMessages 1830", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873748, "dur": 31, "ph": "X", "name": "ReadAsync 1830", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873782, "dur": 1, "ph": "X", "name": "ProcessMessages 968", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873785, "dur": 28, "ph": "X", "name": "ReadAsync 968", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873815, "dur": 2, "ph": "X", "name": "ProcessMessages 695", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873819, "dur": 25, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873845, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873848, "dur": 25, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873875, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873878, "dur": 28, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873908, "dur": 1, "ph": "X", "name": "ProcessMessages 784", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873911, "dur": 26, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873939, "dur": 2, "ph": "X", "name": "ProcessMessages 702", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873942, "dur": 23, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873967, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873969, "dur": 25, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873997, "dur": 1, "ph": "X", "name": "ProcessMessages 715", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056873999, "dur": 25, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874027, "dur": 1, "ph": "X", "name": "ProcessMessages 810", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874030, "dur": 26, "ph": "X", "name": "ReadAsync 810", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874058, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874060, "dur": 21, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874083, "dur": 1, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874085, "dur": 35, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874122, "dur": 2, "ph": "X", "name": "ProcessMessages 1151", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874125, "dur": 25, "ph": "X", "name": "ReadAsync 1151", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874152, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874154, "dur": 26, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874183, "dur": 1, "ph": "X", "name": "ProcessMessages 695", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874185, "dur": 14, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874203, "dur": 27, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874232, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874235, "dur": 59, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874302, "dur": 1, "ph": "X", "name": "ProcessMessages 792", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874305, "dur": 23, "ph": "X", "name": "ReadAsync 792", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874331, "dur": 2, "ph": "X", "name": "ProcessMessages 1522", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874334, "dur": 16, "ph": "X", "name": "ReadAsync 1522", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874352, "dur": 1, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874354, "dur": 26, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874383, "dur": 1, "ph": "X", "name": "ProcessMessages 671", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874386, "dur": 16, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874403, "dur": 1, "ph": "X", "name": "ProcessMessages 692", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874406, "dur": 22, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874430, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874432, "dur": 22, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874456, "dur": 1, "ph": "X", "name": "ProcessMessages 945", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874459, "dur": 14, "ph": "X", "name": "ReadAsync 945", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874475, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874477, "dur": 18, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874497, "dur": 1, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874500, "dur": 15, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874516, "dur": 1, "ph": "X", "name": "ProcessMessages 83", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874518, "dur": 22, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874542, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874545, "dur": 26, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874573, "dur": 1, "ph": "X", "name": "ProcessMessages 949", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874576, "dur": 20, "ph": "X", "name": "ReadAsync 949", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874598, "dur": 1, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874601, "dur": 16, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874619, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874621, "dur": 15, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874639, "dur": 20, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874661, "dur": 1, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874664, "dur": 18, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874684, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874686, "dur": 17, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874705, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874707, "dur": 15, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874724, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874726, "dur": 14, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874742, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874744, "dur": 13, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874762, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874764, "dur": 16, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874782, "dur": 1, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874784, "dur": 17, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874803, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874805, "dur": 15, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874822, "dur": 1, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874825, "dur": 48, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874875, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874878, "dur": 28, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874908, "dur": 2, "ph": "X", "name": "ProcessMessages 1053", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874911, "dur": 26, "ph": "X", "name": "ReadAsync 1053", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874939, "dur": 1, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874942, "dur": 18, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874962, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874964, "dur": 13, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874979, "dur": 1, "ph": "X", "name": "ProcessMessages 195", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874981, "dur": 14, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056874998, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875000, "dur": 18, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875019, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875021, "dur": 22, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875046, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875048, "dur": 55, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875110, "dur": 2, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875113, "dur": 23, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875138, "dur": 2, "ph": "X", "name": "ProcessMessages 1290", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875142, "dur": 23, "ph": "X", "name": "ReadAsync 1290", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875167, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875170, "dur": 14, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875185, "dur": 1, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875187, "dur": 15, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875205, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875207, "dur": 14, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875223, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875225, "dur": 22, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875249, "dur": 1, "ph": "X", "name": "ProcessMessages 692", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875252, "dur": 30, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875284, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875286, "dur": 18, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875306, "dur": 1, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875309, "dur": 14, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875325, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875327, "dur": 61, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875395, "dur": 1, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875397, "dur": 26, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875425, "dur": 2, "ph": "X", "name": "ProcessMessages 1464", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875429, "dur": 22, "ph": "X", "name": "ReadAsync 1464", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875453, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875455, "dur": 27, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875485, "dur": 1, "ph": "X", "name": "ProcessMessages 684", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875487, "dur": 26, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875516, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875519, "dur": 15, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875536, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875538, "dur": 23, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875563, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875565, "dur": 25, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875635, "dur": 26, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875663, "dur": 139, "ph": "X", "name": "ProcessMessages 1510", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875808, "dur": 48, "ph": "X", "name": "ReadAsync 1510", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875858, "dur": 5, "ph": "X", "name": "ProcessMessages 4080", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875864, "dur": 22, "ph": "X", "name": "ReadAsync 4080", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875888, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056875891, "dur": 193, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876089, "dur": 8, "ph": "X", "name": "ProcessMessages 790", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876101, "dur": 58, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876161, "dur": 6, "ph": "X", "name": "ProcessMessages 5186", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876169, "dur": 45, "ph": "X", "name": "ReadAsync 5186", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876217, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876220, "dur": 30, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876253, "dur": 2, "ph": "X", "name": "ProcessMessages 1085", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876256, "dur": 13, "ph": "X", "name": "ReadAsync 1085", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876270, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876272, "dur": 18, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876296, "dur": 5, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876304, "dur": 22, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876328, "dur": 1, "ph": "X", "name": "ProcessMessages 895", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876331, "dur": 16, "ph": "X", "name": "ReadAsync 895", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876349, "dur": 1, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876351, "dur": 62, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876415, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876418, "dur": 34, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876454, "dur": 2, "ph": "X", "name": "ProcessMessages 1555", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876457, "dur": 25, "ph": "X", "name": "ReadAsync 1555", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876485, "dur": 1, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876487, "dur": 19, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876508, "dur": 1, "ph": "X", "name": "ProcessMessages 739", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876511, "dur": 20, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876533, "dur": 1, "ph": "X", "name": "ProcessMessages 619", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876535, "dur": 23, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876560, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876563, "dur": 20, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876590, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876593, "dur": 16, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876611, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876613, "dur": 15, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876630, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876633, "dur": 15, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876650, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876652, "dur": 25, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876679, "dur": 1, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876682, "dur": 67, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876755, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876758, "dur": 41, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876801, "dur": 3, "ph": "X", "name": "ProcessMessages 2291", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876806, "dur": 24, "ph": "X", "name": "ReadAsync 2291", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876832, "dur": 1, "ph": "X", "name": "ProcessMessages 207", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876834, "dur": 31, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876870, "dur": 5, "ph": "X", "name": "ProcessMessages 797", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876877, "dur": 20, "ph": "X", "name": "ReadAsync 797", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876899, "dur": 1, "ph": "X", "name": "ProcessMessages 762", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876902, "dur": 26, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876930, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876933, "dur": 25, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876960, "dur": 1, "ph": "X", "name": "ProcessMessages 579", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876962, "dur": 28, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876993, "dur": 1, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056876996, "dur": 34, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056877032, "dur": 1, "ph": "X", "name": "ProcessMessages 853", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056877035, "dur": 16, "ph": "X", "name": "ReadAsync 853", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056877053, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056877055, "dur": 23, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056877081, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056877083, "dur": 18, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056877103, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056877106, "dur": 17, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056877126, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056877129, "dur": 23, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056877153, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056877156, "dur": 20, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056877178, "dur": 1, "ph": "X", "name": "ProcessMessages 909", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056877180, "dur": 15, "ph": "X", "name": "ReadAsync 909", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056877198, "dur": 2, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056877201, "dur": 19, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056877222, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056877224, "dur": 20, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056877246, "dur": 1, "ph": "X", "name": "ProcessMessages 75", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056877248, "dur": 26, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056877275, "dur": 1, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056877278, "dur": 62, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056877342, "dur": 2, "ph": "X", "name": "ProcessMessages 1522", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056877347, "dur": 17, "ph": "X", "name": "ReadAsync 1522", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056877366, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056877369, "dur": 27, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056877398, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056877401, "dur": 28, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056877431, "dur": 1, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056877434, "dur": 25, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056877462, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056877464, "dur": 27, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056877494, "dur": 1, "ph": "X", "name": "ProcessMessages 692", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056877497, "dur": 30, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056877530, "dur": 2, "ph": "X", "name": "ProcessMessages 847", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056877533, "dur": 17, "ph": "X", "name": "ReadAsync 847", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056877552, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056877554, "dur": 1502, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879063, "dur": 3, "ph": "X", "name": "ProcessMessages 679", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879068, "dur": 181, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879252, "dur": 21, "ph": "X", "name": "ProcessMessages 20482", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879274, "dur": 48, "ph": "X", "name": "ReadAsync 20482", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879331, "dur": 2, "ph": "X", "name": "ProcessMessages 1021", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879337, "dur": 26, "ph": "X", "name": "ReadAsync 1021", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879367, "dur": 8, "ph": "X", "name": "ProcessMessages 1223", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879377, "dur": 20, "ph": "X", "name": "ReadAsync 1223", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879400, "dur": 2, "ph": "X", "name": "ProcessMessages 900", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879403, "dur": 16, "ph": "X", "name": "ReadAsync 900", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879421, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879424, "dur": 21, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879447, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879450, "dur": 28, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879479, "dur": 4, "ph": "X", "name": "ProcessMessages 753", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879485, "dur": 20, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879507, "dur": 1, "ph": "X", "name": "ProcessMessages 922", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879510, "dur": 15, "ph": "X", "name": "ReadAsync 922", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879527, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879529, "dur": 22, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879554, "dur": 1, "ph": "X", "name": "ProcessMessages 195", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879556, "dur": 19, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879577, "dur": 1, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879580, "dur": 32, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879615, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879617, "dur": 28, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879648, "dur": 1, "ph": "X", "name": "ProcessMessages 784", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879651, "dur": 18, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879671, "dur": 1, "ph": "X", "name": "ProcessMessages 254", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879674, "dur": 17, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879693, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879696, "dur": 15, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879713, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879715, "dur": 20, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879741, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879745, "dur": 21, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879768, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879771, "dur": 19, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879792, "dur": 2, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879795, "dur": 23, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879820, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879823, "dur": 21, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879847, "dur": 3, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879851, "dur": 16, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879869, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879871, "dur": 15, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879888, "dur": 1, "ph": "X", "name": "ProcessMessages 71", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879890, "dur": 18, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879911, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879913, "dur": 23, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879938, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879940, "dur": 26, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879969, "dur": 1, "ph": "X", "name": "ProcessMessages 691", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879971, "dur": 24, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056879997, "dur": 1, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880000, "dur": 23, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880025, "dur": 1, "ph": "X", "name": "ProcessMessages 299", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880027, "dur": 17, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880046, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880048, "dur": 34, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880087, "dur": 107, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880198, "dur": 2, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880201, "dur": 17, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880220, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880223, "dur": 14, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880239, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880242, "dur": 31, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880276, "dur": 2, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880280, "dur": 27, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880309, "dur": 2, "ph": "X", "name": "ProcessMessages 735", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880312, "dur": 26, "ph": "X", "name": "ReadAsync 735", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880340, "dur": 1, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880343, "dur": 17, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880362, "dur": 2, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880367, "dur": 26, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880397, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880401, "dur": 42, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880474, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880482, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880523, "dur": 2, "ph": "X", "name": "ProcessMessages 1049", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880527, "dur": 25, "ph": "X", "name": "ReadAsync 1049", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880557, "dur": 5, "ph": "X", "name": "ProcessMessages 131", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880565, "dur": 38, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880605, "dur": 1, "ph": "X", "name": "ProcessMessages 800", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880608, "dur": 19, "ph": "X", "name": "ReadAsync 800", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880629, "dur": 1, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880632, "dur": 15, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880650, "dur": 6, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880659, "dur": 54, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880717, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880745, "dur": 1, "ph": "X", "name": "ProcessMessages 784", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880748, "dur": 19, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880780, "dur": 3, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880786, "dur": 42, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880833, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880862, "dur": 6, "ph": "X", "name": "ProcessMessages 832", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880871, "dur": 16, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880888, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880891, "dur": 45, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880941, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880960, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880963, "dur": 20, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880986, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056880988, "dur": 12, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881002, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881005, "dur": 49, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881058, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881078, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881081, "dur": 22, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881107, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881110, "dur": 53, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881167, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881187, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881190, "dur": 25, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881218, "dur": 5, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881225, "dur": 22, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881250, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881252, "dur": 21, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881277, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881284, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881308, "dur": 1, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881310, "dur": 14, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881326, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881329, "dur": 67, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881407, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881410, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881440, "dur": 1, "ph": "X", "name": "ProcessMessages 947", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881443, "dur": 13, "ph": "X", "name": "ReadAsync 947", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881458, "dur": 1, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881460, "dur": 45, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881510, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881536, "dur": 1, "ph": "X", "name": "ProcessMessages 794", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881539, "dur": 15, "ph": "X", "name": "ReadAsync 794", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881556, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881558, "dur": 59, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881622, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881643, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881646, "dur": 20, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881668, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881670, "dur": 20, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881692, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881694, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881737, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881763, "dur": 5, "ph": "X", "name": "ProcessMessages 729", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881771, "dur": 16, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881789, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881792, "dur": 59, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881864, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881892, "dur": 2, "ph": "X", "name": "ProcessMessages 1102", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881896, "dur": 46, "ph": "X", "name": "ReadAsync 1102", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881947, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881968, "dur": 2, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056881972, "dur": 29, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882004, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882007, "dur": 43, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882054, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882076, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882078, "dur": 22, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882102, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882105, "dur": 67, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882176, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882200, "dur": 1, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882203, "dur": 17, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882222, "dur": 1, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882224, "dur": 55, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882284, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882306, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882309, "dur": 26, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882337, "dur": 1, "ph": "X", "name": "ProcessMessages 693", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882340, "dur": 44, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882388, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882410, "dur": 1, "ph": "X", "name": "ProcessMessages 746", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882413, "dur": 14, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882429, "dur": 1, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882431, "dur": 69, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882514, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882541, "dur": 2, "ph": "X", "name": "ProcessMessages 1200", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882544, "dur": 40, "ph": "X", "name": "ReadAsync 1200", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882589, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882617, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882619, "dur": 25, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882647, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882649, "dur": 43, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882696, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882715, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882718, "dur": 15, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882735, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882737, "dur": 20, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882760, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882762, "dur": 43, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882810, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882831, "dur": 1, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882834, "dur": 20, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882856, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882858, "dur": 19, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882880, "dur": 1, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882882, "dur": 38, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882925, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882944, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882947, "dur": 25, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882975, "dur": 1, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882978, "dur": 12, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882992, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056882994, "dur": 40, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883037, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883039, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883066, "dur": 5, "ph": "X", "name": "ProcessMessages 719", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883073, "dur": 39, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883115, "dur": 1, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883118, "dur": 22, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883144, "dur": 3, "ph": "X", "name": "ProcessMessages 726", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883153, "dur": 18, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883173, "dur": 1, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883175, "dur": 14, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883192, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883195, "dur": 49, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883247, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883271, "dur": 1, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883274, "dur": 16, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883294, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883297, "dur": 53, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883355, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883377, "dur": 1, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883380, "dur": 23, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883406, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883408, "dur": 30, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883441, "dur": 2, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883444, "dur": 25, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883471, "dur": 1, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883474, "dur": 13, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883489, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883491, "dur": 22, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883515, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883518, "dur": 44, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883566, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883592, "dur": 1, "ph": "X", "name": "ProcessMessages 691", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883595, "dur": 17, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883614, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883617, "dur": 56, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883678, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883703, "dur": 1, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883706, "dur": 15, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883723, "dur": 5, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883730, "dur": 49, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883783, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883807, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883810, "dur": 16, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883829, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883831, "dur": 22, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883855, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883858, "dur": 37, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883899, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883935, "dur": 2, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883939, "dur": 27, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883968, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883972, "dur": 21, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883995, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056883999, "dur": 45, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884046, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884048, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884073, "dur": 1, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884076, "dur": 16, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884094, "dur": 2, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884097, "dur": 24, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884127, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884130, "dur": 21, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884154, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884157, "dur": 16, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884175, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884178, "dur": 14, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884194, "dur": 1, "ph": "X", "name": "ProcessMessages 234", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884196, "dur": 52, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884253, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884297, "dur": 2, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884301, "dur": 17, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884320, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884322, "dur": 29, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884356, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884376, "dur": 1, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884379, "dur": 30, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884410, "dur": 1, "ph": "X", "name": "ProcessMessages 728", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884413, "dur": 27, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884442, "dur": 1, "ph": "X", "name": "ProcessMessages 859", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884445, "dur": 26, "ph": "X", "name": "ReadAsync 859", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884474, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884477, "dur": 21, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884500, "dur": 1, "ph": "X", "name": "ProcessMessages 90", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884502, "dur": 17, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884522, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884524, "dur": 42, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884570, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884589, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884592, "dur": 17, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884611, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884613, "dur": 15, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884630, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884632, "dur": 49, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884686, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884707, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056884710, "dur": 770, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056885483, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056885486, "dur": 57, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056885545, "dur": 6, "ph": "X", "name": "ProcessMessages 5126", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056885572, "dur": 65, "ph": "X", "name": "ReadAsync 5126", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056885640, "dur": 14, "ph": "X", "name": "ProcessMessages 6267", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056885657, "dur": 16, "ph": "X", "name": "ReadAsync 6267", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056885674, "dur": 1, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056885677, "dur": 34, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056885714, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056885716, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056885739, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056885742, "dur": 28, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056885772, "dur": 1, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056885775, "dur": 22, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056885799, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056885802, "dur": 31, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056885837, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056885869, "dur": 1, "ph": "X", "name": "ProcessMessages 711", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056885872, "dur": 27, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056885901, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056885904, "dur": 21, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056885927, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056885930, "dur": 19, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056885950, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056885953, "dur": 16, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056885971, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056885973, "dur": 59, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886037, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886066, "dur": 1, "ph": "X", "name": "ProcessMessages 790", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886069, "dur": 20, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886091, "dur": 4, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886098, "dur": 38, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886141, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886170, "dur": 2, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886173, "dur": 16, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886191, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886194, "dur": 54, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886250, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886254, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886278, "dur": 2, "ph": "X", "name": "ProcessMessages 732", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886281, "dur": 26, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886310, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886312, "dur": 18, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886333, "dur": 5, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886340, "dur": 43, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886385, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886387, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886421, "dur": 2, "ph": "X", "name": "ProcessMessages 901", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886425, "dur": 21, "ph": "X", "name": "ReadAsync 901", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886448, "dur": 1, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886450, "dur": 14, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886466, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886468, "dur": 45, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886518, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886540, "dur": 1, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886543, "dur": 15, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886560, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886562, "dur": 21, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886585, "dur": 1, "ph": "X", "name": "ProcessMessages 58", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886587, "dur": 42, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886632, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886634, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886654, "dur": 1, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886656, "dur": 16, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886674, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886676, "dur": 20, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886699, "dur": 1, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886701, "dur": 43, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886748, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886771, "dur": 1, "ph": "X", "name": "ProcessMessages 642", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886774, "dur": 20, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886796, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886799, "dur": 17, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886817, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886824, "dur": 40, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886868, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886888, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886891, "dur": 14, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886907, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886909, "dur": 19, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886929, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886932, "dur": 22, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886956, "dur": 2, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886960, "dur": 16, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886977, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886980, "dur": 15, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886997, "dur": 1, "ph": "X", "name": "ProcessMessages 71", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056886999, "dur": 15, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887015, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887018, "dur": 56, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887078, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887102, "dur": 1, "ph": "X", "name": "ProcessMessages 766", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887105, "dur": 18, "ph": "X", "name": "ReadAsync 766", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887125, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887127, "dur": 54, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887186, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887213, "dur": 1, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887216, "dur": 18, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887236, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887239, "dur": 14, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887255, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887257, "dur": 45, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887306, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887334, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887338, "dur": 20, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887360, "dur": 1, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887363, "dur": 46, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887412, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887442, "dur": 1, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887445, "dur": 20, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887467, "dur": 1, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887470, "dur": 47, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887519, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887521, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887541, "dur": 1, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887544, "dur": 18, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887564, "dur": 1, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887566, "dur": 22, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887590, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887593, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887637, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887656, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887659, "dur": 17, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887678, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887680, "dur": 13, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887695, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887697, "dur": 49, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887748, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887750, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887771, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887774, "dur": 21, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887797, "dur": 5, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887804, "dur": 23, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887829, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887832, "dur": 26, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887862, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887888, "dur": 1, "ph": "X", "name": "ProcessMessages 756", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887890, "dur": 23, "ph": "X", "name": "ReadAsync 756", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887914, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887917, "dur": 50, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887974, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056887977, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888011, "dur": 2, "ph": "X", "name": "ProcessMessages 904", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888014, "dur": 26, "ph": "X", "name": "ReadAsync 904", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888042, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888044, "dur": 24, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888072, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888103, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888106, "dur": 21, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888130, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888132, "dur": 44, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888181, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888210, "dur": 1, "ph": "X", "name": "ProcessMessages 726", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888212, "dur": 17, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888231, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888234, "dur": 48, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888286, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888313, "dur": 1, "ph": "X", "name": "ProcessMessages 944", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888316, "dur": 26, "ph": "X", "name": "ReadAsync 944", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888345, "dur": 1, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888350, "dur": 37, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888392, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888428, "dur": 1, "ph": "X", "name": "ProcessMessages 725", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888431, "dur": 27, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888461, "dur": 1, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888463, "dur": 26, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888491, "dur": 1, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888494, "dur": 27, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888524, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888526, "dur": 19, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888547, "dur": 1, "ph": "X", "name": "ProcessMessages 239", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888550, "dur": 13, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888565, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888567, "dur": 47, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888619, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888648, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888651, "dur": 16, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888669, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888671, "dur": 50, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888726, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888772, "dur": 2, "ph": "X", "name": "ProcessMessages 1179", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888776, "dur": 44, "ph": "X", "name": "ReadAsync 1179", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888825, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888857, "dur": 1, "ph": "X", "name": "ProcessMessages 868", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888860, "dur": 15, "ph": "X", "name": "ReadAsync 868", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888878, "dur": 1, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888881, "dur": 72, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888964, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888967, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056888995, "dur": 6, "ph": "X", "name": "ProcessMessages 1147", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889003, "dur": 29, "ph": "X", "name": "ReadAsync 1147", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889038, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889059, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889062, "dur": 19, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889087, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889090, "dur": 21, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889113, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889116, "dur": 25, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889143, "dur": 1, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889146, "dur": 34, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889190, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889194, "dur": 23, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889219, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889222, "dur": 19, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889244, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889280, "dur": 2, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889283, "dur": 17, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889303, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889306, "dur": 46, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889355, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889384, "dur": 1, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889387, "dur": 25, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889414, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889416, "dur": 38, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889458, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889465, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889487, "dur": 1, "ph": "X", "name": "ProcessMessages 790", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889490, "dur": 23, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889516, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889518, "dur": 20, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889540, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889542, "dur": 30, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889576, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889617, "dur": 2, "ph": "X", "name": "ProcessMessages 854", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889620, "dur": 25, "ph": "X", "name": "ReadAsync 854", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889647, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889650, "dur": 37, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889692, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889727, "dur": 1, "ph": "X", "name": "ProcessMessages 1033", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889730, "dur": 24, "ph": "X", "name": "ReadAsync 1033", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889757, "dur": 2, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889761, "dur": 34, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889797, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889800, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889828, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889831, "dur": 24, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889857, "dur": 3, "ph": "X", "name": "ProcessMessages 471", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889862, "dur": 39, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889905, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889926, "dur": 1, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889929, "dur": 20, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889951, "dur": 2, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056889954, "dur": 58, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890016, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890063, "dur": 1, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890066, "dur": 24, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890091, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890094, "dur": 29, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890127, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890149, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890152, "dur": 23, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890177, "dur": 1, "ph": "X", "name": "ProcessMessages 579", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890180, "dur": 16, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890198, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890200, "dur": 16, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890219, "dur": 1, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890221, "dur": 28, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890251, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890254, "dur": 15, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890272, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890275, "dur": 43, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890322, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890354, "dur": 2, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890357, "dur": 26, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890385, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890388, "dur": 39, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890431, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890462, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890466, "dur": 25, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890497, "dur": 1, "ph": "X", "name": "ProcessMessages 961", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890500, "dur": 27, "ph": "X", "name": "ReadAsync 961", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890529, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890532, "dur": 20, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890555, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890557, "dur": 17, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890577, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890579, "dur": 54, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890637, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890660, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890663, "dur": 19, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890684, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890686, "dur": 15, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890704, "dur": 1, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890706, "dur": 23, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890731, "dur": 1, "ph": "X", "name": "ProcessMessages 139", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890733, "dur": 22, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890757, "dur": 1, "ph": "X", "name": "ProcessMessages 714", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890760, "dur": 12, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890774, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890777, "dur": 17, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890796, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890798, "dur": 47, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890850, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890868, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890871, "dur": 27, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890901, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890903, "dur": 18, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890924, "dur": 1, "ph": "X", "name": "ProcessMessages 726", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890926, "dur": 24, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890953, "dur": 1, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890955, "dur": 28, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890985, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056890988, "dur": 21, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056891011, "dur": 1, "ph": "X", "name": "ProcessMessages 184", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056891014, "dur": 11, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056891026, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056891028, "dur": 42, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056891075, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056891093, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056891095, "dur": 90, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056891191, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056891195, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056891241, "dur": 322, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056891568, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056891601, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056891606, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056891644, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056891649, "dur": 36, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056891691, "dur": 5, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056891699, "dur": 32, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056891735, "dur": 4, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056891742, "dur": 24, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056891771, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056891777, "dur": 24, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056891804, "dur": 3, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056891811, "dur": 27, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056891843, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056891849, "dur": 25, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056891878, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056891883, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056891915, "dur": 4, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056891923, "dur": 32, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056891959, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056891962, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056891990, "dur": 3, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056891995, "dur": 28, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056892026, "dur": 4, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056892033, "dur": 27, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056892065, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056892073, "dur": 24, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056892100, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056892104, "dur": 30, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056892138, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056892143, "dur": 22, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056892168, "dur": 5, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056892176, "dur": 28, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056892207, "dur": 3, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056892213, "dur": 24, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056892241, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056892245, "dur": 28, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056892276, "dur": 2, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056892279, "dur": 16, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056892298, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056892302, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056892325, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056892329, "dur": 30, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056892363, "dur": 4, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056892369, "dur": 30, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056892403, "dur": 4, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056892409, "dur": 27, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056892439, "dur": 3, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056892444, "dur": 29, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056892476, "dur": 3, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056892481, "dur": 30, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056892516, "dur": 4, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056892522, "dur": 33, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056892559, "dur": 4, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056892566, "dur": 31, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056892600, "dur": 5, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056892608, "dur": 1124, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056893741, "dur": 5, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056893749, "dur": 166, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056893921, "dur": 94, "ph": "X", "name": "ProcessMessages 5716", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056894019, "dur": 8149, "ph": "X", "name": "ReadAsync 5716", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056902182, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056902190, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056902237, "dur": 3, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056902243, "dur": 216, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056902464, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056902468, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056902501, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056902505, "dur": 1179, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056903689, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056903693, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056903729, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056903732, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056903759, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056903761, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056903797, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056903820, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056903822, "dur": 300, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056904138, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056904141, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056904173, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056904179, "dur": 190, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056904373, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056904377, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056904400, "dur": 3, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056904404, "dur": 22, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056904432, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056904438, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056904461, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056904466, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056904485, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056904488, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056904519, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056904522, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056904550, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056904554, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056904578, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056904581, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056904610, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056904614, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056904631, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056904634, "dur": 425, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905062, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905065, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905084, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905087, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905108, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905112, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905158, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905161, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905177, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905180, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905206, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905211, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905234, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905238, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905274, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905277, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905303, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905307, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905341, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905344, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905365, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905368, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905386, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905389, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905407, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905410, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905456, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905459, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905474, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905477, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905491, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905497, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905559, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905563, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905580, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905583, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905602, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905606, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905625, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905628, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905653, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905671, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905674, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905711, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905714, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905732, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905735, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905753, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905756, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905780, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905784, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905802, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905805, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905827, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905832, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905851, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905870, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905872, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905910, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905913, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905944, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905948, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905967, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056905971, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906004, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906007, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906024, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906027, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906041, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906044, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906075, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906098, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906103, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906126, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906129, "dur": 156, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906287, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906290, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906307, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906310, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906350, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906353, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906369, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906372, "dur": 105, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906481, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906484, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906499, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906502, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906551, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906554, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906570, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906572, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906604, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906608, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906624, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906628, "dur": 12, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906643, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906645, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906660, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906663, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906682, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906686, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906722, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906725, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906742, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906744, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906796, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906799, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906815, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906817, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906835, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906839, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906905, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906908, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906924, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906926, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906952, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906954, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906969, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056906971, "dur": 93, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056907068, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056907073, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056907090, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056907092, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056907108, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056907111, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056907144, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056907147, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056907161, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056907164, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056907180, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056907183, "dur": 85, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056907272, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056907275, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056907291, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056907293, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056907342, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056907346, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056907362, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056907365, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056907386, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056907390, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056907407, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056907410, "dur": 17, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056907429, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056907432, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056907473, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056907476, "dur": 2733, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056910220, "dur": 6, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056910230, "dur": 89, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056910327, "dur": 606, "ph": "X", "name": "ProcessMessages 1556", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056910938, "dur": 40, "ph": "X", "name": "ReadAsync 1556", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056910984, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056910991, "dur": 77, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056911072, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056911077, "dur": 90, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056911173, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056911178, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056911211, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056911215, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056911244, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056911248, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056911275, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056911279, "dur": 200, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056911486, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056911490, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056911512, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056911514, "dur": 163, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056911684, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056911688, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056911780, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056911785, "dur": 88, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056911879, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056911885, "dur": 87, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056911981, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056911986, "dur": 84, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056912079, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056912084, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056912134, "dur": 9, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056912148, "dur": 114, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056912269, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056912273, "dur": 124, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056912403, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056912407, "dur": 123, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056912537, "dur": 7, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056912550, "dur": 161, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056912717, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056912721, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056912783, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056912786, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056912804, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056912806, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056912834, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056912836, "dur": 95, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056912942, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056912967, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056912970, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056913045, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056913069, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056913071, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056913093, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056913095, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056913160, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056913168, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056913185, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056913188, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056913242, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056913258, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056913260, "dur": 72, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056913339, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056913341, "dur": 180, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056913523, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056913526, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056913556, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056913559, "dur": 119, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056913686, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056913688, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056913715, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056913718, "dur": 82, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056913804, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056913807, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056913867, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056913870, "dur": 198, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056914072, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056914075, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056914108, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056914115, "dur": 88, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056914207, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056914211, "dur": 93, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056914312, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056914314, "dur": 85, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056914402, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056914405, "dur": 121, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056914530, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056914533, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056914561, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056914566, "dur": 301, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056914875, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056914878, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056914908, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056914911, "dur": 245, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056915160, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056915163, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056915193, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371056915195, "dur": 471971, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057387183, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057387191, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057387247, "dur": 1514, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057388771, "dur": 7546, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057396331, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057396342, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057396421, "dur": 6, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057396430, "dur": 159, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057396593, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057396597, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057396617, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057396621, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057396640, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057396644, "dur": 138, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057396789, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057396794, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057396816, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057396820, "dur": 632, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057397459, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057397462, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057397479, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057397482, "dur": 958, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057398445, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057398448, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057398464, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057398467, "dur": 310, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057398780, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057398784, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057398828, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057398832, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057398849, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057398852, "dur": 71, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057398926, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057398929, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057398943, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057398946, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057399007, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057399010, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057399025, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057399029, "dur": 211, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057399247, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057399252, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057399276, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057399279, "dur": 322, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057399606, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057399609, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057399629, "dur": 8, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057399645, "dur": 193, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057399842, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057399845, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057399863, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057399866, "dur": 304, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057400174, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057400177, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057400207, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057400210, "dur": 794, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057401008, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057401011, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057401027, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057401030, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057401110, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057401114, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057401142, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057401145, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057401194, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057401197, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057401213, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057401216, "dur": 261, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057401480, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057401483, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057401500, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057401503, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057401528, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057401533, "dur": 108, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057401644, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057401647, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057401662, "dur": 11, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057401677, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057401717, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057401719, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057401736, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057401739, "dur": 489, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057402233, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057402236, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057402262, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057402266, "dur": 122, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057402391, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057402394, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057402408, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057402411, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057402428, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057402446, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057402450, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057402467, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057402470, "dur": 420, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057402894, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057402899, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057402915, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057402917, "dur": 150, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057403070, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057403072, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057403087, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057403090, "dur": 661, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057403757, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057403761, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057403779, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057403782, "dur": 232, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057404018, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057404021, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057404037, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057404040, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057404060, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057404064, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057404080, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057404083, "dur": 490, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057404579, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057404597, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057404600, "dur": 90, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057404696, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057404701, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057404724, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057404727, "dur": 194, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057404925, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057404928, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057404944, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057404947, "dur": 170, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057405122, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057405143, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057405146, "dur": 106, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057405256, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057405259, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057405274, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057405277, "dur": 344, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057405625, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057405628, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057405643, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057405646, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057405681, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057405700, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057405703, "dur": 340, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057406047, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057406051, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057406067, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057406070, "dur": 470, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057406545, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057406548, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057406564, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057406566, "dur": 471, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057407041, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057407044, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057407058, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057407061, "dur": 177, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057407242, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057407245, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057407262, "dur": 8, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057407275, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057407304, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057407307, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057407323, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057407326, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057407373, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057407376, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057407390, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057407393, "dur": 142, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057407542, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057407555, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057407558, "dur": 151, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057407714, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057407717, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057407735, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057407738, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057407774, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057407777, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057407791, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057407794, "dur": 404, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057408201, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057408204, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057408221, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057408224, "dur": 208, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057408443, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057408446, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057408463, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057408466, "dur": 173, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057408645, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057408649, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057408665, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057408669, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057408715, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057408718, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057408740, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057408743, "dur": 162, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057408911, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057408915, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057408937, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057408940, "dur": 84, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409029, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409042, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409045, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409098, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409102, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409119, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409123, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409151, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409154, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409174, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409177, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409210, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409212, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409236, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409240, "dur": 56, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409300, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409303, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409320, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409325, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409340, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409344, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409363, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409367, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409389, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409399, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409423, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409427, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409442, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409445, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409464, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409467, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409484, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409486, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409504, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409511, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409528, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409532, "dur": 18, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409553, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409557, "dur": 14, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409573, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409576, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409597, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409601, "dur": 14, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409618, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409620, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409642, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409646, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409663, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409666, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409686, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409691, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409708, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409711, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409733, "dur": 7, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409743, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409771, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409775, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409795, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409799, "dur": 17, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409821, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409825, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409841, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409844, "dur": 13, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409859, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409862, "dur": 13, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409879, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409882, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409896, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409899, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409913, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409916, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409937, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409941, "dur": 12, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409955, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409959, "dur": 12, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409973, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409976, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409994, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057409998, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057410019, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057410024, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057410044, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057410050, "dur": 101, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057410157, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057410160, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057410193, "dur": 6, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057410201, "dur": 18, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057410222, "dur": 8, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057410235, "dur": 27, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057410265, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057410269, "dur": 16, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057410288, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057410292, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057410314, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057410319, "dur": 23, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057410346, "dur": 3, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057410351, "dur": 34, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057410391, "dur": 4, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057410397, "dur": 118, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057410522, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057410527, "dur": 126, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057410659, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057410664, "dur": 133, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057410808, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057410812, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057410848, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057410851, "dur": 52, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057410909, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057410913, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057410943, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057410948, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057410997, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057411001, "dur": 761, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057411768, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057411771, "dur": 102, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057411879, "dur": 301, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751371057412185, "dur": 28615, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 2364, "tid": 9, "ts": 1751371057455021, "dur": 4485, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 2364, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 2364, "tid": 8589934592, "ts": 1751371056833026, "dur": 480413, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 2364, "tid": 8589934592, "ts": 1751371057313443, "dur": 7, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 2364, "tid": 8589934592, "ts": 1751371057313451, "dur": 1148, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 2364, "tid": 9, "ts": 1751371057459509, "dur": 11, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 2364, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 2364, "tid": 4294967296, "ts": 1751371056807532, "dur": 634344, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 2364, "tid": 4294967296, "ts": 1751371056812485, "dur": 14069, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 2364, "tid": 4294967296, "ts": 1751371057442112, "dur": 5777, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 2364, "tid": 4294967296, "ts": 1751371057444785, "dur": 1700, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 2364, "tid": 4294967296, "ts": 1751371057447970, "dur": 13, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 2364, "tid": 9, "ts": 1751371057459523, "dur": 13, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751371056839624, "dur": 24695, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751371056864327, "dur": 1670, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751371056866099, "dur": 138, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751371056866237, "dur": 337, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751371056866777, "dur": 111, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_C0823A9ADF385DE2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751371056867594, "dur": 231, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_5099CB98C03D5F98.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751371056867913, "dur": 140, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_7B22B0BDD3082718.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751371056868135, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_3713E40F818C5856.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751371056868289, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_5A6241C5E4FBF31E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751371056868452, "dur": 94, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_1237F680F70DA85A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751371056868666, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_FB049F22F4C5497A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751371056868876, "dur": 103, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_21E5C8D6C2CA360E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751371056869042, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_C359BB03E2AB95AC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751371056869206, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751371056869486, "dur": 95, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751371056869861, "dur": 100, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751371056870120, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751371056870297, "dur": 106, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751371056870842, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751371056870974, "dur": 100, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ref.dll_C2E5A21D32AE5F0E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751371056871109, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751371056871310, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751371056871617, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751371056872018, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751371056872252, "dur": 107, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751371056877944, "dur": 937, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751371056885125, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751371056885206, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6307440961136846775.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751371056866585, "dur": 24110, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751371056890712, "dur": 519869, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751371057410584, "dur": 153, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751371057410754, "dur": 302, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751371057411249, "dur": 56, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751371057411321, "dur": 24077, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751371056867341, "dur": 23398, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371056890741, "dur": 541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_7B22B0BDD3082718.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751371056891370, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371056891482, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_CD971E07FD450EB6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751371056892313, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751371056892873, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371056893046, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6307440961136846775.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751371056893358, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371056893602, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371056894396, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371056895127, "dur": 330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371056895458, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371056895749, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371056896010, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371056896546, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371056896799, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371056897037, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371056897266, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371056897473, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371056897724, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371056897996, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371056898311, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371056898590, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371056898906, "dur": 595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371056899501, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371056899731, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371056899990, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371056900223, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371056900490, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371056900809, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371056901078, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371056901331, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371056901578, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371056901985, "dur": 1326, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371056903311, "dur": 448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371056903760, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751371056903934, "dur": 766, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371056904704, "dur": 769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751371056905474, "dur": 428, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371056905971, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751371056906227, "dur": 700, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751371056906927, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371056907004, "dur": 550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751371056907617, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751371056907790, "dur": 412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751371056908239, "dur": 484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1751371056908774, "dur": 96, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371056909045, "dur": 477654, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1751371057393427, "dur": 2410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751371057395838, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371057395981, "dur": 2457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751371057398438, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371057398548, "dur": 2367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751371057400916, "dur": 342, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371057401264, "dur": 2430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751371057403695, "dur": 609, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371057404310, "dur": 2306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751371057406660, "dur": 2314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751371057408974, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371057409552, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371057409967, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751371057410106, "dur": 474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751371056867919, "dur": 22854, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751371056890775, "dur": 487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_AAD14E71D524EA60.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751371056891522, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_24D87271FC85A7AF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751371056891643, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_7D6DB1EEB95EE9BC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751371056892018, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751371056892162, "dur": 9165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751371056901328, "dur": 450, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751371056901825, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751371056902063, "dur": 1156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751371056903219, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751371056903310, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751371056903410, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751371056903756, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751371056903923, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751371056904083, "dur": 773, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751371056904912, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751371056904979, "dur": 620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751371056905627, "dur": 499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751371056906175, "dur": 611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751371056906787, "dur": 922, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751371056907730, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751371056907899, "dur": 531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751371056908430, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751371056908491, "dur": 655, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751371056909151, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751371056909332, "dur": 441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751371056909813, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751371056909909, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751371056909971, "dur": 356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751371056910365, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751371056910466, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751371056910797, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751371056910873, "dur": 427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751371056911300, "dur": 2429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751371056913729, "dur": 479724, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751371057393455, "dur": 2418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751371057395915, "dur": 2456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751371057398372, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751371057398458, "dur": 2623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751371057401081, "dur": 760, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751371057401847, "dur": 2444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751371057404340, "dur": 2401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751371057406742, "dur": 412, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751371057407160, "dur": 2861, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751371057410076, "dur": 535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371056868400, "dur": 22404, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371056890807, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_BA35B04A057142E1.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751371056891528, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371056892698, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751371056892856, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371056893277, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7908171937365489455.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751371056893370, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371056893632, "dur": 1014, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371056894647, "dur": 337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371056894985, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371056895294, "dur": 336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371056895630, "dur": 340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371056895971, "dur": 493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371056896464, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371056896798, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371056897009, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371056897225, "dur": 367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371056897592, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371056897822, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371056898055, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371056898298, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371056898562, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371056898839, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371056899139, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371056899450, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371056899775, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371056900015, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371056900258, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371056900528, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371056900819, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371056901081, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371056901379, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371056901666, "dur": 360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371056902026, "dur": 1276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371056903303, "dur": 455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371056903759, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751371056903997, "dur": 1353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751371056905351, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371056905434, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751371056905649, "dur": 1648, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751371056907297, "dur": 642, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371056907961, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751371056908099, "dur": 369, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371056908471, "dur": 1255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751371056909784, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751371056909975, "dur": 615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751371056910661, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751371056910837, "dur": 598, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751371056911491, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751371056911657, "dur": 577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751371056912307, "dur": 1446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371056913753, "dur": 479683, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371057393449, "dur": 2430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751371057395918, "dur": 2597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751371057398515, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371057398806, "dur": 2481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-unity.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751371057401336, "dur": 2782, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751371057404119, "dur": 421, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371057404546, "dur": 2254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751371057406801, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751371057406865, "dur": 2876, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751371057409988, "dur": 615, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751371056867181, "dur": 23547, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751371056890736, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751371056890808, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_8AA31634E5830E2F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751371056891531, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751371056892021, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751371056892205, "dur": 9497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751371056901841, "dur": 1019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751371056902860, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751371056903337, "dur": 447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751371056903787, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751371056904007, "dur": 1596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751371056905604, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751371056905716, "dur": 1283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751371056907000, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751371056907270, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751371056907472, "dur": 504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751371056907976, "dur": 476, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751371056908459, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751371056908558, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751371056908696, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751371056909135, "dur": 414, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751371056909552, "dur": 1723, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751371056911276, "dur": 2037, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751371056913315, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751371056913439, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751371056913728, "dur": 479702, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751371057393432, "dur": 2801, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751371057396234, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751371057396399, "dur": 3339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751371057399827, "dur": 2651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751371057402512, "dur": 3001, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751371057405513, "dur": 649, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751371057406167, "dur": 2700, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751371057408962, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751371057409210, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751371057409349, "dur": 376, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751371057409758, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751371057409971, "dur": 368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751371057410339, "dur": 240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371056867302, "dur": 23431, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371056890735, "dur": 498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_7A744375D0C40DCD.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751371056891406, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_FA0E2521CCF3B353.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751371056891518, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_8AA30A6C8DDCA8A3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751371056892535, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751371056892891, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371056893354, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371056893610, "dur": 800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371056894410, "dur": 1310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371056895720, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371056896017, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371056896492, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371056896739, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371056896969, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371056897187, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371056897395, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371056897609, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371056897889, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371056898157, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371056898412, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371056898707, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371056898989, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371056899281, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371056899553, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371056899787, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371056900100, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371056900347, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371056900590, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371056900861, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371056901110, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371056901378, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371056901687, "dur": 459, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371056902146, "dur": 1174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371056903321, "dur": 447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371056903772, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751371056904040, "dur": 1063, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751371056905103, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371056905280, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371056905369, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751371056905621, "dur": 555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751371056906243, "dur": 612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751371056906856, "dur": 359, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371056907277, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751371056907428, "dur": 537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751371056907965, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371056908037, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751371056908244, "dur": 1013, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751371056909316, "dur": 1965, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371056911281, "dur": 2442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371056913724, "dur": 400431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371057315842, "dur": 163, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 5, "ts": 1751371057316006, "dur": 957, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 5, "ts": 1751371057314157, "dur": 2853, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371057317010, "dur": 78940, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371057395951, "dur": 2647, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp-firstpass.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751371057398627, "dur": 3253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751371057401880, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371057402049, "dur": 2572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751371057404622, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371057404756, "dur": 3003, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751371057407760, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751371057407819, "dur": 2458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751371057410337, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056867042, "dur": 23676, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056890736, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056890804, "dur": 458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_E4B698B849B24AEB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751371056891513, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_EFD593047BD2E6A8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751371056892011, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751371056892247, "dur": 213, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751371056892858, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056893019, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6091109579032960000.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751371056893339, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056893566, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056893734, "dur": 807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056894542, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056894727, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056894990, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056895296, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056895619, "dur": 330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056895949, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056896435, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056896720, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056896927, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056897179, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056897373, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056897587, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056897803, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056898058, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056898353, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056898634, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056898869, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056899163, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056899384, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056899622, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056899892, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056900132, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056900392, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056900664, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056900932, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056901211, "dur": 573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056901824, "dur": 880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056902704, "dur": 595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056903300, "dur": 492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056903794, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751371056904047, "dur": 894, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751371056904941, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056905226, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751371056905472, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751371056905712, "dur": 406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751371056906119, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056906337, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity-editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751371056906575, "dur": 383, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056906963, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751371056907129, "dur": 576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751371056907706, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056907954, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751371056908127, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751371056908551, "dur": 358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056908910, "dur": 1474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056910422, "dur": 852, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056911274, "dur": 1169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056912444, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751371056912551, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751371056912881, "dur": 871, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371056913752, "dur": 479700, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371057393453, "dur": 3552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751371057397006, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371057397073, "dur": 2579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751371057399653, "dur": 1155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371057400815, "dur": 2511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualEffectGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751371057403373, "dur": 2367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/NativeFilePicker.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751371057405741, "dur": 1586, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751371057407333, "dur": 2592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751371057410003, "dur": 580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056867616, "dur": 23140, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056890763, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_3122CFBB2DC47329.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751371056891205, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_7BCDC5BDF664862C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751371056891328, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_68F0E05FD8235E34.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751371056891529, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056892296, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751371056892610, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751371056892856, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056893023, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10316626440694862930.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751371056893349, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056893596, "dur": 963, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056894560, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056894881, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056895186, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056895494, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056895805, "dur": 526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056896331, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056896603, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056896844, "dur": 507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056897351, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056897544, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056897838, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056898078, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056898323, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056898585, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056898883, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056899180, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056899461, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056899667, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056899917, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056900197, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056900461, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056900708, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056900969, "dur": 430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056901400, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056901928, "dur": 93, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056902022, "dur": 1275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056903297, "dur": 531, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056903828, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751371056904165, "dur": 605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751371056904770, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056904835, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056905076, "dur": 1208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751371056906285, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056906572, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751371056906763, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751371056907245, "dur": 392, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056907640, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751371056907821, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056907989, "dur": 559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751371056908548, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056908617, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056908904, "dur": 793, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056909698, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751371056909814, "dur": 471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751371056910346, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751371056910448, "dur": 1028, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751371056911476, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371056911543, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751371056911647, "dur": 1003, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751371056912685, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751371056912779, "dur": 474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751371056913310, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751371056913415, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751371056913720, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751371056913854, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751371056914143, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751371056914452, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751371056914769, "dur": 478692, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371057393462, "dur": 2442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751371057395905, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371057395992, "dur": 2608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751371057398601, "dur": 2124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371057400763, "dur": 3209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751371057403972, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371057404215, "dur": 2612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751371057406863, "dur": 2608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751371057409822, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751371057409979, "dur": 607, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056867474, "dur": 23270, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056890746, "dur": 454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_89517EAC2A874601.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751371056891218, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_24CD4430EA38EE97.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751371056891336, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_5AE8CAD8193F1AE4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751371056891535, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056892060, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751371056892119, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056892214, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751371056892423, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751371056892694, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751371056892746, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751371056892877, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056893029, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14507123614329245381.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751371056893344, "dur": 526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056893870, "dur": 970, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056894840, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056895125, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056895400, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056895711, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056895978, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056896459, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056896740, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056896941, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056897164, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056897413, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056897623, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056897855, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056898060, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056898300, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056898590, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056898859, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056899130, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056899424, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056899733, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056900034, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056900270, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056900500, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056900763, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056901019, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056901295, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056901592, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056901991, "dur": 1304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056903331, "dur": 431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056903766, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751371056904011, "dur": 612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751371056904624, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056904711, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751371056904985, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751371056905185, "dur": 341, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056905530, "dur": 608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751371056906139, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056906257, "dur": 828, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751371056907086, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056907276, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751371056907409, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056907470, "dur": 422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751371056907892, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056908121, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056908254, "dur": 647, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056908902, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751371056909060, "dur": 454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751371056909573, "dur": 1757, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056911330, "dur": 2395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371056913726, "dur": 479706, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371057393434, "dur": 2428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751371057395863, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371057395946, "dur": 2596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751371057398543, "dur": 674, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371057399225, "dur": 2218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751371057401444, "dur": 562, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371057402010, "dur": 2686, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751371057404696, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371057404876, "dur": 2317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751371057407193, "dur": 629, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371057407826, "dur": 2481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/ScriptablePacker.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751371057410310, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751371057410449, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056867763, "dur": 23004, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056890769, "dur": 388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_98CF6329D3D6F9AB.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751371056891211, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056891331, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_F48E27B3AC5122EF.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751371056891536, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056892193, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751371056892852, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056893345, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056893569, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056895132, "dur": 765, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.36f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.DataProtection.Abstractions.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751371056894250, "dur": 1768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056896018, "dur": 523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056896541, "dur": 382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056896923, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056897120, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056897361, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056897582, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056897807, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056898064, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056898318, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056898545, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056898801, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056899131, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056899423, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056899705, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056899935, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056900179, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056900413, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056900659, "dur": 396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056901055, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056901296, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056901553, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056901806, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056902298, "dur": 1019, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056903318, "dur": 457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056903778, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751371056904004, "dur": 710, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751371056904715, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056904801, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751371056905210, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056905485, "dur": 1463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751371056906948, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056907160, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056907242, "dur": 811, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ForUI.ref.dll_CAAF5CDDE611F3DD.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751371056908072, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056908274, "dur": 633, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056908908, "dur": 912, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056909844, "dur": 1436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056911280, "dur": 2451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371056913732, "dur": 479696, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371057393430, "dur": 2400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751371057395832, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371057395904, "dur": 2443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751371057398397, "dur": 2653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751371057401051, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371057401115, "dur": 3374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751371057404490, "dur": 804, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371057405301, "dur": 2274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751371057407576, "dur": 681, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371057408324, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371057408558, "dur": 383, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371057408978, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371057409256, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371057409427, "dur": 342, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371057409826, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371057409973, "dur": 446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751371057410457, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371056868057, "dur": 22722, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371056890781, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_D07FFB3B2770754A.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751371056891208, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_D8154579280ABE30.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751371056891293, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_D8154579280ABE30.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751371056891508, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_DA3F53EC74158B55.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751371056891999, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751371056892165, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1751371056892248, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1751371056892300, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751371056892859, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371056893042, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2524390107958066875.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751371056893376, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371056893640, "dur": 919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371056894560, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371056894838, "dur": 347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371056895186, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371056895447, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371056895708, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371056895984, "dur": 540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371056896524, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371056896801, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371056897012, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371056897243, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371056897449, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371056897852, "dur": 1076, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@7bffd5bb179a\\Editor\\RenderGraph\\RenderGraphViewer.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751371056897852, "dur": 1296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371056899238, "dur": 1295, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.Core\\Assignment\\Assignment.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751371056899148, "dur": 1550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371056900698, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371056901244, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371056901463, "dur": 125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371056901589, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371056901848, "dur": 861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371056902709, "dur": 625, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371056903334, "dur": 454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371056903791, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751371056904016, "dur": 490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751371056904506, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371056904830, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751371056905031, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371056905178, "dur": 699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751371056905929, "dur": 541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751371056906471, "dur": 492, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371056906966, "dur": 758, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity-editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751371056907725, "dur": 551, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371056908279, "dur": 618, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371056908898, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751371056909031, "dur": 424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751371056909455, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371056909572, "dur": 1699, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371056911272, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751371056911457, "dur": 899, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751371056912357, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371056912441, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751371056912614, "dur": 463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751371056913078, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371056913162, "dur": 570, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371056913732, "dur": 479725, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371057393459, "dur": 2392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751371057395852, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371057395942, "dur": 2949, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751371057398891, "dur": 562, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371057399461, "dur": 2505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/OutlineFx.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751371057401967, "dur": 718, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371057402692, "dur": 2475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751371057405168, "dur": 1817, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371057406991, "dur": 2808, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751371057409941, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751371057410026, "dur": 558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056868258, "dur": 22533, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056890793, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_D5DC31B4B0DA901E.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751371056891373, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_60DEBCA51ABA1468.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751371056891506, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_41563AEB7BF951B8.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751371056892057, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1751371056892291, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751371056892512, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Path.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751371056892631, "dur": 147, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1751371056892867, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056893037, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10054408285822421624.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751371056893231, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056893337, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056893571, "dur": 770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056894341, "dur": 792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056895134, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056895422, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056895750, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056896034, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056896559, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056896785, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056897006, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056897235, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056897660, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056897922, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056898166, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056898463, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056898697, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056898948, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056899250, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056899470, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056899760, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056899989, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056900273, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056900520, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056900794, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056901073, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056901329, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056901572, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056901851, "dur": 597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056902448, "dur": 882, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056903330, "dur": 449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056903783, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751371056904030, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751371056904139, "dur": 642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751371056904845, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751371056905099, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751371056905330, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751371056905584, "dur": 631, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751371056906216, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056906271, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751371056906522, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751371056906722, "dur": 520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751371056907243, "dur": 437, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056907717, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751371056907902, "dur": 488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751371056908433, "dur": 466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056908900, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751371056909062, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751371056909535, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056909635, "dur": 1637, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056911273, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751371056911438, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751371056911789, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056911873, "dur": 1847, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371056913721, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751371056913873, "dur": 479589, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371057393464, "dur": 2397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751371057395861, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371057395939, "dur": 2651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-csharp.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751371057398644, "dur": 2400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualEffectGraph.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751371057401100, "dur": 3055, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751371057404195, "dur": 2693, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751371057406889, "dur": 498, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751371057407393, "dur": 2685, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751371057410123, "dur": 486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056868185, "dur": 22600, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056890787, "dur": 397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_21E5C8D6C2CA360E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751371056891215, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_24AD401155842AFB.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751371056891504, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_15728F368AD157F8.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751371056892061, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1751371056892405, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056892709, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751371056892862, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056893040, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18127688178268093734.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751371056893379, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056893570, "dur": 891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056894462, "dur": 557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056895019, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056895325, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056895645, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056895965, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056896604, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056896848, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056897069, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056897332, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056897533, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056897802, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056898022, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056898230, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056898503, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056898794, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056899082, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056899442, "dur": 544, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@7bffd5bb179a\\Runtime\\RenderGraph\\RenderGraph.Compiler.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751371056899378, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056900214, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056900441, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056900716, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056900981, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056901233, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056901506, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056901810, "dur": 903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056902713, "dur": 600, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056903314, "dur": 442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056903757, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751371056904195, "dur": 510, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056904708, "dur": 987, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751371056905696, "dur": 715, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056906455, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751371056906639, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056906791, "dur": 660, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751371056907452, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056907616, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751371056907804, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056907951, "dur": 804, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751371056908756, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056908896, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751371056909054, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056909159, "dur": 465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751371056909695, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751371056909845, "dur": 699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751371056910612, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751371056910783, "dur": 431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751371056911270, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751371056911391, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751371056911825, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751371056911948, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751371056912366, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056912425, "dur": 1297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371056913723, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751371056913881, "dur": 479578, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371057393460, "dur": 2402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751371057395863, "dur": 375, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371057396243, "dur": 2532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751371057398779, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371057398849, "dur": 3170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751371057402056, "dur": 2548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751371057404605, "dur": 1053, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371057405665, "dur": 2753, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-unity-editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751371057408419, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371057408712, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-unity-editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751371057408829, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371057409133, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371057409658, "dur": 301, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751371057409977, "dur": 605, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056868525, "dur": 22286, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056890814, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_7D19F4582D6FA60D.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751371056891211, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_385F174668EB898F.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751371056891297, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_385F174668EB898F.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751371056891418, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056891472, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_878D99D4DAFDE3CC.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751371056891542, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_B70C9B9527050002.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751371056891636, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_5312197D285F8F1E.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751371056892522, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751371056892708, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751371056892858, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056893018, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2894064572437044024.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751371056893371, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056893639, "dur": 940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056894580, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056894847, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056895152, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056895480, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056895719, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056896003, "dur": 500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056896503, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056896814, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056897013, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056897279, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056897509, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056897771, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056897993, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056898242, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056898499, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056898773, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056899008, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056899283, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056899516, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056899744, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056900015, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056900257, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056900480, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056900744, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056901030, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056901310, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056901568, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056901992, "dur": 1312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056903305, "dur": 488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056903794, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751371056904001, "dur": 659, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751371056904721, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056904911, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751371056905181, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751371056905378, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751371056905698, "dur": 399, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056906103, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751371056906293, "dur": 1326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751371056907619, "dur": 476, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056908117, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056908240, "dur": 680, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056908920, "dur": 1745, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056910667, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751371056910851, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751371056911229, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056911330, "dur": 2395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371056913725, "dur": 403316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371057317042, "dur": 76408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371057393453, "dur": 3487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751371057396941, "dur": 1114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371057398062, "dur": 2525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751371057400625, "dur": 2436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751371057403062, "dur": 622, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371057403690, "dur": 2553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751371057406244, "dur": 673, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371057406923, "dur": 2486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751371057409410, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751371057410008, "dur": 577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371056868650, "dur": 22195, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371056890847, "dur": 361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_57E453305C48C0D1.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751371056891509, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_E951570DA9D21231.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751371056892123, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751371056892609, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1751371056892841, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371056893050, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5934182079298241566.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751371056893347, "dur": 417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371056893764, "dur": 887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371056894652, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371056895122, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371056895443, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371056895767, "dur": 553, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@ba0fe1b085e9\\Editor\\Models\\Operators\\Implementations\\CrossProduct.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751371056895685, "dur": 801, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371056896486, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371056896767, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371056896984, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371056897202, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371056897426, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371056897703, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371056897938, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371056898183, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371056898458, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371056898751, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371056898995, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371056899255, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371056899562, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371056899824, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371056900105, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371056900365, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371056900584, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371056900828, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371056901077, "dur": 393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371056901504, "dur": 85, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371056901589, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371056901983, "dur": 1320, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371056903304, "dur": 468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371056903773, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751371056903972, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371056904052, "dur": 564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751371056904616, "dur": 771, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371056905429, "dur": 929, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751371056906359, "dur": 749, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371056907109, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751371056907244, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371056907337, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751371056907522, "dur": 516, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751371056908115, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371056908240, "dur": 681, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371056908921, "dur": 2361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371056911282, "dur": 2448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371056913730, "dur": 479704, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371057393435, "dur": 3145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/OutlineFx.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751371057396580, "dur": 2224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371057398819, "dur": 2540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751371057401359, "dur": 709, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371057402074, "dur": 2786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751371057404861, "dur": 376, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751371057405244, "dur": 2766, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751371057408057, "dur": 2458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751371056868325, "dur": 22471, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371056890798, "dur": 457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Apple.Extensions.Common.dll_D4E020400338F5BB.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751371056891256, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371056891317, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_5A6241C5E4FBF31E.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751371056891443, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371056891511, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_A5513E9150BC6315.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751371056892552, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371056892700, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751371056892854, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371056893014, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14324468880911302389.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751371056893341, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371056893636, "dur": 974, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371056894610, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371056894880, "dur": 374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371056895255, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371056895560, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371056895832, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371056896690, "dur": 992, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.animation@494a3b4e73a9\\Editor\\SkinningModule\\UI\\GenerateGeometryPanel.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751371056896479, "dur": 1248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371056897727, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371056897963, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371056898197, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371056898467, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371056898731, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371056898998, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371056899232, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371056899482, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371056899749, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371056900007, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371056900264, "dur": 623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371056900887, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371056901315, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371056901588, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371056901824, "dur": 1069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371056902893, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371056903326, "dur": 434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371056903761, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751371056903987, "dur": 555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751371056904542, "dur": 455, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371056905023, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751371056905207, "dur": 702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751371056905910, "dur": 354, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371056906268, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751371056906486, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371056906688, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751371056906892, "dur": 715, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751371056907608, "dur": 800, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371056908431, "dur": 480, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371056908912, "dur": 1703, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371056910616, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751371056910731, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751371056911054, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751371056911411, "dur": 2317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371056913729, "dur": 479751, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371057393483, "dur": 2417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751371057395902, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371057395995, "dur": 2585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751371057398626, "dur": 2458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751371057401128, "dur": 2479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751371057403636, "dur": 2581, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751371057406218, "dur": 2491, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371057408735, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371057408826, "dur": 284, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371057409206, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371057409299, "dur": 658, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751371057409978, "dur": 634, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056868822, "dur": 22045, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056890867, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_FB049F22F4C5497A.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751371056891521, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056892710, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751371056892860, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056893356, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056893635, "dur": 966, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056894602, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056894900, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056895161, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056895434, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056895789, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056896314, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056896536, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056896813, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056897044, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056897269, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056897484, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056897725, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056897941, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056898140, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056898384, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056898656, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056898916, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056899124, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056899362, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056899667, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056899910, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056900164, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056900399, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056900690, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056900980, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056901255, "dur": 802, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056902058, "dur": 1265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056903324, "dur": 701, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056904025, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751371056904226, "dur": 543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751371056904770, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056904960, "dur": 466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751371056905426, "dur": 915, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056906345, "dur": 608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751371056906954, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056907154, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056907283, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/ScriptablePacker.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751371056907451, "dur": 552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/ScriptablePacker.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751371056908003, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056908186, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056908255, "dur": 643, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056908899, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751371056909081, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751371056909554, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056909643, "dur": 1634, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056911277, "dur": 2455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371056913732, "dur": 479942, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371057393675, "dur": 2476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/NativeFilePicker.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751371057396207, "dur": 2520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751371057398728, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371057398857, "dur": 2949, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751371057401806, "dur": 1865, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371057403675, "dur": 2450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751371057406161, "dur": 2430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751371057408591, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371057408652, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371057408970, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371057409163, "dur": 474, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371057409881, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1751371057409969, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751371057410159, "dur": 423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751371057439024, "dur": 1110, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 2364, "tid": 9, "ts": 1751371057459929, "dur": 1780, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 2364, "tid": 9, "ts": 1751371057461837, "dur": 1481, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 2364, "tid": 9, "ts": 1751371057451985, "dur": 12083, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}