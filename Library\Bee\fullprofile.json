{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 14916, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 14916, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 14916, "tid": 706, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 14916, "tid": 706, "ts": 1751362732749118, "dur": 1000, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 14916, "tid": 706, "ts": 1751362732753256, "dur": 894, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 14916, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 14916, "tid": 1, "ts": 1751362723759093, "dur": 9242, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 14916, "tid": 1, "ts": 1751362723768339, "dur": 168154, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 14916, "tid": 1, "ts": 1751362723936509, "dur": 159326, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 14916, "tid": 706, "ts": 1751362732754157, "dur": 27, "ph": "X", "name": "", "args": {}}, {"pid": 14916, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723757042, "dur": 89400, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723846445, "dur": 8892389, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723848053, "dur": 2747, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723850808, "dur": 581, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723851397, "dur": 15094, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723866538, "dur": 274, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723866931, "dur": 146, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723867085, "dur": 1496, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723868588, "dur": 14078, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723882798, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723882816, "dur": 182, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723883039, "dur": 879, "ph": "X", "name": "ProcessMessages 8764", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723884036, "dur": 283, "ph": "X", "name": "ReadAsync 8764", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723884325, "dur": 24, "ph": "X", "name": "ProcessMessages 20482", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723884351, "dur": 138, "ph": "X", "name": "ReadAsync 20482", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723884495, "dur": 7, "ph": "X", "name": "ProcessMessages 4310", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723884504, "dur": 124, "ph": "X", "name": "ReadAsync 4310", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723884638, "dur": 12, "ph": "X", "name": "ProcessMessages 3876", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723884653, "dur": 55, "ph": "X", "name": "ReadAsync 3876", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723884711, "dur": 2, "ph": "X", "name": "ProcessMessages 1570", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723884715, "dur": 92, "ph": "X", "name": "ReadAsync 1570", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723885438, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723885442, "dur": 416, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723885865, "dur": 38, "ph": "X", "name": "ProcessMessages 15468", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723885905, "dur": 441, "ph": "X", "name": "ReadAsync 15468", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723886619, "dur": 14, "ph": "X", "name": "ProcessMessages 5592", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723886636, "dur": 1067, "ph": "X", "name": "ReadAsync 5592", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723887710, "dur": 18, "ph": "X", "name": "ProcessMessages 14528", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723887730, "dur": 186, "ph": "X", "name": "ReadAsync 14528", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723887919, "dur": 22, "ph": "X", "name": "ProcessMessages 19792", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723887944, "dur": 18, "ph": "X", "name": "ReadAsync 19792", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723887964, "dur": 1, "ph": "X", "name": "ProcessMessages 200", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723887967, "dur": 33, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723888010, "dur": 3, "ph": "X", "name": "ProcessMessages 752", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723888016, "dur": 117, "ph": "X", "name": "ReadAsync 752", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723888136, "dur": 2, "ph": "X", "name": "ProcessMessages 864", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723888140, "dur": 878, "ph": "X", "name": "ReadAsync 864", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723889028, "dur": 3, "ph": "X", "name": "ProcessMessages 1127", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723889033, "dur": 180, "ph": "X", "name": "ReadAsync 1127", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723889221, "dur": 19, "ph": "X", "name": "ProcessMessages 20492", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723889242, "dur": 15, "ph": "X", "name": "ReadAsync 20492", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723889259, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723889261, "dur": 226, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723889494, "dur": 3, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723889499, "dur": 135, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723889638, "dur": 7, "ph": "X", "name": "ProcessMessages 5185", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723889646, "dur": 44, "ph": "X", "name": "ReadAsync 5185", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723889693, "dur": 2, "ph": "X", "name": "ProcessMessages 1494", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723889697, "dur": 93, "ph": "X", "name": "ReadAsync 1494", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723889793, "dur": 87, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723889886, "dur": 4, "ph": "X", "name": "ProcessMessages 880", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723889895, "dur": 77, "ph": "X", "name": "ReadAsync 880", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723889976, "dur": 2, "ph": "X", "name": "ProcessMessages 1398", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723890259, "dur": 138, "ph": "X", "name": "ReadAsync 1398", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723890402, "dur": 6, "ph": "X", "name": "ProcessMessages 3390", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723890485, "dur": 190, "ph": "X", "name": "ReadAsync 3390", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723890681, "dur": 211, "ph": "X", "name": "ProcessMessages 9226", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723890897, "dur": 141, "ph": "X", "name": "ReadAsync 9226", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723891044, "dur": 10, "ph": "X", "name": "ProcessMessages 6059", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723891056, "dur": 53, "ph": "X", "name": "ReadAsync 6059", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723891111, "dur": 3, "ph": "X", "name": "ProcessMessages 2126", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723891115, "dur": 111, "ph": "X", "name": "ReadAsync 2126", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723891235, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723891239, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723891280, "dur": 2, "ph": "X", "name": "ProcessMessages 1291", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723891283, "dur": 82, "ph": "X", "name": "ReadAsync 1291", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723891371, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723891376, "dur": 44, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723891422, "dur": 10, "ph": "X", "name": "ProcessMessages 2040", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723891434, "dur": 134, "ph": "X", "name": "ReadAsync 2040", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723891847, "dur": 8, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723891857, "dur": 179, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723892042, "dur": 13, "ph": "X", "name": "ProcessMessages 9863", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723892078, "dur": 45, "ph": "X", "name": "ReadAsync 9863", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723892125, "dur": 4, "ph": "X", "name": "ProcessMessages 2977", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723892131, "dur": 61, "ph": "X", "name": "ReadAsync 2977", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723892197, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723892201, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723892234, "dur": 2, "ph": "X", "name": "ProcessMessages 827", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723892237, "dur": 87, "ph": "X", "name": "ReadAsync 827", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723892334, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723892339, "dur": 94, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723892437, "dur": 3, "ph": "X", "name": "ProcessMessages 1034", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723892442, "dur": 32, "ph": "X", "name": "ReadAsync 1034", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723892477, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723892480, "dur": 29, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723892516, "dur": 3, "ph": "X", "name": "ProcessMessages 829", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723892521, "dur": 30, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723892553, "dur": 1, "ph": "X", "name": "ProcessMessages 662", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723892556, "dur": 47, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723892613, "dur": 3, "ph": "X", "name": "ProcessMessages 998", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723892619, "dur": 30, "ph": "X", "name": "ReadAsync 998", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723892651, "dur": 2, "ph": "X", "name": "ProcessMessages 1154", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723892654, "dur": 19, "ph": "X", "name": "ReadAsync 1154", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723892675, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723892677, "dur": 17, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723892696, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723892698, "dur": 35, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723892736, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723892741, "dur": 117, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723892864, "dur": 3, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723892874, "dur": 94, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723892974, "dur": 6, "ph": "X", "name": "ProcessMessages 3359", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723892986, "dur": 35, "ph": "X", "name": "ReadAsync 3359", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723893032, "dur": 2, "ph": "X", "name": "ProcessMessages 1119", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723893035, "dur": 58, "ph": "X", "name": "ReadAsync 1119", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723893099, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723893175, "dur": 20, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723893198, "dur": 68, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723893269, "dur": 3, "ph": "X", "name": "ProcessMessages 2559", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723893274, "dur": 91, "ph": "X", "name": "ReadAsync 2559", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723893367, "dur": 1, "ph": "X", "name": "ProcessMessages 103", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723893370, "dur": 27, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723893398, "dur": 2, "ph": "X", "name": "ProcessMessages 2152", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723893402, "dur": 69, "ph": "X", "name": "ReadAsync 2152", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723893489, "dur": 411, "ph": "X", "name": "ReadAsync 114", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723893903, "dur": 12, "ph": "X", "name": "ProcessMessages 8399", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723893916, "dur": 17, "ph": "X", "name": "ReadAsync 8399", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723893935, "dur": 1, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723893938, "dur": 55, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723894003, "dur": 3, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723894008, "dur": 30, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723894043, "dur": 2, "ph": "X", "name": "ProcessMessages 1327", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723894046, "dur": 55, "ph": "X", "name": "ReadAsync 1327", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723894111, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723894517, "dur": 4, "ph": "X", "name": "ProcessMessages 826", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723894523, "dur": 158, "ph": "X", "name": "ReadAsync 826", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723894691, "dur": 20, "ph": "X", "name": "ProcessMessages 9911", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723894713, "dur": 66, "ph": "X", "name": "ReadAsync 9911", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723894782, "dur": 3, "ph": "X", "name": "ProcessMessages 1810", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723894787, "dur": 27, "ph": "X", "name": "ReadAsync 1810", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723894815, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723894818, "dur": 15, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723894835, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723894838, "dur": 77, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723894921, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723894925, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723894958, "dur": 2, "ph": "X", "name": "ProcessMessages 791", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723894962, "dur": 16, "ph": "X", "name": "ReadAsync 791", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723894985, "dur": 1, "ph": "X", "name": "ProcessMessages 200", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723894987, "dur": 71, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723895064, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723895067, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723895107, "dur": 2, "ph": "X", "name": "ProcessMessages 1009", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723895111, "dur": 68, "ph": "X", "name": "ReadAsync 1009", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723895471, "dur": 7, "ph": "X", "name": "ProcessMessages 438", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723895485, "dur": 84, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723895575, "dur": 10, "ph": "X", "name": "ProcessMessages 5353", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723895586, "dur": 289, "ph": "X", "name": "ReadAsync 5353", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723895883, "dur": 3, "ph": "X", "name": "ProcessMessages 1084", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723895893, "dur": 44, "ph": "X", "name": "ReadAsync 1084", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723895939, "dur": 4, "ph": "X", "name": "ProcessMessages 3233", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723895944, "dur": 349, "ph": "X", "name": "ReadAsync 3233", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723896302, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723896304, "dur": 49, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723896355, "dur": 5, "ph": "X", "name": "ProcessMessages 3778", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723896361, "dur": 20, "ph": "X", "name": "ReadAsync 3778", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723896384, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723896387, "dur": 28, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723896422, "dur": 3, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723896427, "dur": 31, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723896463, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723896488, "dur": 10, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723896505, "dur": 20, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723896536, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723896541, "dur": 21, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723896565, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723896598, "dur": 2, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723896601, "dur": 19, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723896622, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723896625, "dur": 57, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723896689, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723896720, "dur": 2, "ph": "X", "name": "ProcessMessages 736", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723896725, "dur": 21, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723896748, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723896751, "dur": 40, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723896798, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723896801, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723896844, "dur": 2, "ph": "X", "name": "ProcessMessages 1132", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723896849, "dur": 15, "ph": "X", "name": "ReadAsync 1132", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723896866, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723896869, "dur": 34, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723896908, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723896936, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723896939, "dur": 16, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723896959, "dur": 1, "ph": "X", "name": "ProcessMessages 693", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723896961, "dur": 47, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723897013, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723897033, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723897035, "dur": 23, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723897064, "dur": 2, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723897069, "dur": 19, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723897093, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723897097, "dur": 26, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723897127, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723897155, "dur": 2, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723897158, "dur": 20, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723897181, "dur": 1, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723897183, "dur": 352, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723897544, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723897550, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723897622, "dur": 6, "ph": "X", "name": "ProcessMessages 4264", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723897651, "dur": 27, "ph": "X", "name": "ReadAsync 4264", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723897679, "dur": 2, "ph": "X", "name": "ProcessMessages 1269", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723897683, "dur": 17, "ph": "X", "name": "ReadAsync 1269", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723897702, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723897705, "dur": 18, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723897728, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723897740, "dur": 20, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723897762, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723897785, "dur": 55, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723897843, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723897845, "dur": 24, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723897871, "dur": 2, "ph": "X", "name": "ProcessMessages 726", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723897874, "dur": 16, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723897893, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723897896, "dur": 41, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723897941, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723897974, "dur": 2, "ph": "X", "name": "ProcessMessages 748", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723897977, "dur": 56, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723898035, "dur": 1, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723898038, "dur": 33, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723898073, "dur": 2, "ph": "X", "name": "ProcessMessages 1112", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723898077, "dur": 20, "ph": "X", "name": "ReadAsync 1112", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723898100, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723898104, "dur": 36, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723898144, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723898177, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723898179, "dur": 13, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723898194, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723898196, "dur": 48, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723898247, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723898250, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723898273, "dur": 1, "ph": "X", "name": "ProcessMessages 710", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723898276, "dur": 18, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723898297, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723898299, "dur": 44, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723898347, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723898374, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723898376, "dur": 18, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723898397, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723898400, "dur": 45, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723898451, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723898458, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723898486, "dur": 2, "ph": "X", "name": "ProcessMessages 875", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723898490, "dur": 16, "ph": "X", "name": "ReadAsync 875", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723898508, "dur": 1, "ph": "X", "name": "ProcessMessages 402", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723898510, "dur": 17, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723898529, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723898532, "dur": 45, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723898579, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723898584, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723898609, "dur": 1, "ph": "X", "name": "ProcessMessages 701", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723898612, "dur": 22, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723898637, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723898641, "dur": 21, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723898664, "dur": 1, "ph": "X", "name": "ProcessMessages 794", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723898667, "dur": 28, "ph": "X", "name": "ReadAsync 794", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723898698, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723898700, "dur": 349, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723899061, "dur": 8, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723899071, "dur": 54, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723899127, "dur": 5, "ph": "X", "name": "ProcessMessages 4587", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723899136, "dur": 21, "ph": "X", "name": "ReadAsync 4587", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723899161, "dur": 5, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723899169, "dur": 20, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723899190, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723899193, "dur": 37, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723899258, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723899262, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723899291, "dur": 2, "ph": "X", "name": "ProcessMessages 1511", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723899296, "dur": 22, "ph": "X", "name": "ReadAsync 1511", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723899323, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723899325, "dur": 40, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723899368, "dur": 2, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723899371, "dur": 35, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723899409, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723899411, "dur": 26, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723899450, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723899453, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723899471, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723899474, "dur": 26, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723899503, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723899506, "dur": 49, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723899559, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723899562, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723899583, "dur": 1, "ph": "X", "name": "ProcessMessages 801", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723899586, "dur": 17, "ph": "X", "name": "ReadAsync 801", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723899605, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723899608, "dur": 49, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723899660, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723899702, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723899705, "dur": 327, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723900044, "dur": 1, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723900096, "dur": 66, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723900164, "dur": 7, "ph": "X", "name": "ProcessMessages 5797", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723900173, "dur": 16, "ph": "X", "name": "ReadAsync 5797", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723900191, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723900193, "dur": 15, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723900212, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723900215, "dur": 42, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723900271, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723900298, "dur": 2, "ph": "X", "name": "ProcessMessages 972", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723900311, "dur": 15, "ph": "X", "name": "ReadAsync 972", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723900329, "dur": 1, "ph": "X", "name": "ProcessMessages 138", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723900331, "dur": 31, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723900387, "dur": 309, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723900714, "dur": 9, "ph": "X", "name": "ProcessMessages 1275", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723900725, "dur": 117, "ph": "X", "name": "ReadAsync 1275", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723900844, "dur": 6, "ph": "X", "name": "ProcessMessages 3980", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723900852, "dur": 24, "ph": "X", "name": "ReadAsync 3980", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723900882, "dur": 2, "ph": "X", "name": "ProcessMessages 1052", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723900895, "dur": 30, "ph": "X", "name": "ReadAsync 1052", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723900927, "dur": 2, "ph": "X", "name": "ProcessMessages 913", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723900930, "dur": 316, "ph": "X", "name": "ReadAsync 913", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723901276, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723901320, "dur": 5, "ph": "X", "name": "ProcessMessages 3752", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723901327, "dur": 16, "ph": "X", "name": "ReadAsync 3752", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723901345, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723901347, "dur": 15, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723901366, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723901369, "dur": 28, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723901400, "dur": 1, "ph": "X", "name": "ProcessMessages 743", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723901403, "dur": 22, "ph": "X", "name": "ReadAsync 743", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723901427, "dur": 2, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723901430, "dur": 14, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723901446, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723901448, "dur": 404, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723901853, "dur": 4, "ph": "X", "name": "ProcessMessages 3799", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723901859, "dur": 25, "ph": "X", "name": "ReadAsync 3799", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723901888, "dur": 2, "ph": "X", "name": "ProcessMessages 1230", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723901892, "dur": 40, "ph": "X", "name": "ReadAsync 1230", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723901934, "dur": 1, "ph": "X", "name": "ProcessMessages 743", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723901942, "dur": 25, "ph": "X", "name": "ReadAsync 743", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723901972, "dur": 2, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723901979, "dur": 30, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902013, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902037, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902039, "dur": 19, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902061, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902066, "dur": 56, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902125, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902157, "dur": 2, "ph": "X", "name": "ProcessMessages 994", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902160, "dur": 15, "ph": "X", "name": "ReadAsync 994", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902178, "dur": 1, "ph": "X", "name": "ProcessMessages 141", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902180, "dur": 37, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902221, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902223, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902267, "dur": 2, "ph": "X", "name": "ProcessMessages 1126", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902270, "dur": 39, "ph": "X", "name": "ReadAsync 1126", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902314, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902339, "dur": 1, "ph": "X", "name": "ProcessMessages 642", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902342, "dur": 18, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902362, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902365, "dur": 46, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902418, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902446, "dur": 1, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902448, "dur": 19, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902469, "dur": 1, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902471, "dur": 13, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902487, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902494, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902531, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902558, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902561, "dur": 19, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902583, "dur": 1, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902587, "dur": 44, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902634, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902663, "dur": 2, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902679, "dur": 29, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902710, "dur": 1, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902719, "dur": 15, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902738, "dur": 21, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902760, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902763, "dur": 15, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902780, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902782, "dur": 15, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902802, "dur": 1, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902804, "dur": 33, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902840, "dur": 2, "ph": "X", "name": "ProcessMessages 703", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902844, "dur": 19, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902866, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902869, "dur": 16, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902888, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902897, "dur": 32, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902933, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902952, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902956, "dur": 15, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902980, "dur": 7, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723902988, "dur": 12, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723903002, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723903004, "dur": 34, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723903042, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723903075, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723903078, "dur": 31, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723903111, "dur": 1, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723903114, "dur": 18, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723903137, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723903159, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723903162, "dur": 18, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723903181, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723903185, "dur": 11, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723903201, "dur": 1, "ph": "X", "name": "ProcessMessages 139", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723903203, "dur": 45, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723903253, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723903277, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723903288, "dur": 29, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723903322, "dur": 2, "ph": "X", "name": "ProcessMessages 642", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723903326, "dur": 28, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723903360, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723903382, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723903385, "dur": 29, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723903416, "dur": 2, "ph": "X", "name": "ProcessMessages 877", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723903420, "dur": 25, "ph": "X", "name": "ReadAsync 877", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723903447, "dur": 1, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723903450, "dur": 38, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723903491, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723903494, "dur": 13, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723903509, "dur": 1, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723903511, "dur": 54, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904114, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904117, "dur": 103, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904222, "dur": 9, "ph": "X", "name": "ProcessMessages 7507", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904233, "dur": 13, "ph": "X", "name": "ReadAsync 7507", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904248, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904251, "dur": 28, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904283, "dur": 26, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904311, "dur": 2, "ph": "X", "name": "ProcessMessages 844", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904314, "dur": 26, "ph": "X", "name": "ReadAsync 844", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904342, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904345, "dur": 46, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904394, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904397, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904428, "dur": 1, "ph": "X", "name": "ProcessMessages 717", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904431, "dur": 21, "ph": "X", "name": "ReadAsync 717", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904454, "dur": 1, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904457, "dur": 16, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904474, "dur": 1, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904477, "dur": 16, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904496, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904498, "dur": 17, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904517, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904520, "dur": 14, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904538, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904555, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904557, "dur": 45, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904606, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904636, "dur": 1, "ph": "X", "name": "ProcessMessages 709", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904641, "dur": 16, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904659, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904662, "dur": 41, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904718, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904744, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904747, "dur": 37, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904786, "dur": 2, "ph": "X", "name": "ProcessMessages 1184", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904789, "dur": 16, "ph": "X", "name": "ReadAsync 1184", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904807, "dur": 1, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904810, "dur": 15, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904827, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904829, "dur": 21, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904852, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904854, "dur": 15, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904871, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904874, "dur": 41, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904919, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904939, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904942, "dur": 19, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723904967, "dur": 262, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723905237, "dur": 99, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723905340, "dur": 7, "ph": "X", "name": "ProcessMessages 4569", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723905349, "dur": 35, "ph": "X", "name": "ReadAsync 4569", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723905386, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723905389, "dur": 67, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723905464, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723905469, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723905502, "dur": 424, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723906048, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723906095, "dur": 5, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723906104, "dur": 25, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723906132, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723906137, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723906179, "dur": 5, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723906186, "dur": 25, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723906216, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723906221, "dur": 24, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723906258, "dur": 6, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723906267, "dur": 51, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723906323, "dur": 4, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723906329, "dur": 30, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723906365, "dur": 4, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723906371, "dur": 34, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723906410, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723906416, "dur": 36, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723906457, "dur": 3, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723906462, "dur": 40, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723906507, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723906511, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723906545, "dur": 3, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723906571, "dur": 30, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723906607, "dur": 4, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723906616, "dur": 39, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723906660, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723906666, "dur": 34, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723907377, "dur": 25, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723907407, "dur": 74, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723907488, "dur": 29, "ph": "X", "name": "ProcessMessages 1792", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723907519, "dur": 41, "ph": "X", "name": "ReadAsync 1792", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723907567, "dur": 5, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723907575, "dur": 59, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723907640, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723907647, "dur": 40, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723907693, "dur": 5, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723907701, "dur": 40, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723907748, "dur": 12, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723907772, "dur": 26, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723907801, "dur": 10, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723907813, "dur": 78, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723907897, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723907902, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723907937, "dur": 4, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723907943, "dur": 55, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723908003, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723908009, "dur": 30, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723908043, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723908048, "dur": 58, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723908115, "dur": 5, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723908123, "dur": 49, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723908177, "dur": 4, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723908184, "dur": 32, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723908219, "dur": 3, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723908225, "dur": 32, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723908262, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723908267, "dur": 38, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723908310, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723908316, "dur": 146, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723908470, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723908474, "dur": 83, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723908561, "dur": 6, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723908570, "dur": 367, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723908941, "dur": 6, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723908950, "dur": 42, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723908998, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909003, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909036, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909041, "dur": 19, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909061, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909063, "dur": 22, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909091, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909095, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909129, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909133, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909160, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909163, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909182, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909186, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909206, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909210, "dur": 82, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909296, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909300, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909326, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909330, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909388, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909392, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909441, "dur": 2, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909446, "dur": 26, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909479, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909483, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909509, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909512, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909539, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909544, "dur": 96, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909644, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909648, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909712, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909716, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909763, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909769, "dur": 64, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909838, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909844, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909892, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909897, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909932, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909937, "dur": 30, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909973, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723909980, "dur": 43, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723910028, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723910035, "dur": 26, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723910064, "dur": 3, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723910069, "dur": 27, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723910102, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723910107, "dur": 38, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723910153, "dur": 4, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723910161, "dur": 31, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723910195, "dur": 4, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723910202, "dur": 35, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723910277, "dur": 4, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723910285, "dur": 36, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723910329, "dur": 5, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723910336, "dur": 34, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723910374, "dur": 3, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723910379, "dur": 33, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723910416, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723910420, "dur": 26, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723910449, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723910454, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723910493, "dur": 3, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723910499, "dur": 35, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723910539, "dur": 4, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723910545, "dur": 22, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723910574, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723910579, "dur": 33, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723910618, "dur": 435, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723911058, "dur": 55, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723911116, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723911120, "dur": 5707, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723916839, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723916847, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723916888, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723916893, "dur": 311, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723917212, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723917218, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723917254, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723917259, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723917293, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723917298, "dur": 190, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723917493, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723917496, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723917548, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723917553, "dur": 4518, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723922082, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723922087, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723922141, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723922146, "dur": 38, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723922188, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723922191, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723922216, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723922218, "dur": 722, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723922948, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723922952, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723922979, "dur": 3, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723922984, "dur": 30, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723923017, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723923020, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723923034, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723923037, "dur": 65, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723923107, "dur": 462, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723923574, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723923620, "dur": 4, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723923627, "dur": 26, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723923659, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723923663, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723923687, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723923690, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723923750, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723923780, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723923783, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723923805, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723923809, "dur": 47, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723923860, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723923863, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723923881, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723923884, "dur": 15, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723923904, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723923925, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723923928, "dur": 90, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924024, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924027, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924055, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924060, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924081, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924085, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924127, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924131, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924147, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924150, "dur": 97, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924251, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924254, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924274, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924277, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924300, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924306, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924327, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924330, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924349, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924352, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924380, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924383, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924404, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924407, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924443, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924463, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924467, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924487, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924490, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924510, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924514, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924539, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924542, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924588, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924590, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924604, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924607, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924645, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924664, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924667, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924703, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924706, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924725, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924728, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924763, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924766, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924791, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924799, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924853, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924874, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924878, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924903, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924907, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924926, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723924930, "dur": 192, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723925125, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723925128, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723925149, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723925153, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723925195, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723925198, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723925221, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723925226, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723925254, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723925258, "dur": 220, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723925482, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723925485, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723925514, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723925517, "dur": 63, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723925585, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723925589, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723925615, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723925622, "dur": 64, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723925690, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723925704, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723925735, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723925740, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723925767, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723925771, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723925793, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723925796, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723925816, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723925820, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723925839, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723925842, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723925863, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723925866, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723925886, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723925890, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723925907, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723925910, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723925945, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723925949, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723925995, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723925999, "dur": 20, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723926024, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723926031, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723926053, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723926056, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723926096, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723926100, "dur": 112, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723926216, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723926219, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723926236, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723926239, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723926266, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723926271, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723926300, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723926304, "dur": 59, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723926368, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723926389, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723926415, "dur": 3, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723926421, "dur": 16, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723926439, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723926443, "dur": 168, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723926615, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723926619, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723926647, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723926652, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723926684, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723926688, "dur": 14, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723926705, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723926707, "dur": 151, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723926863, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723926867, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723926889, "dur": 4, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723926894, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723926921, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723926924, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723926957, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723926961, "dur": 45, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723927011, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723927030, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723927034, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723927057, "dur": 13, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723927073, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723927102, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723927105, "dur": 17, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723927621, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723927627, "dur": 102, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723927735, "dur": 5, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723927741, "dur": 35, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723927781, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723927784, "dur": 93, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723927887, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723927891, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723927977, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723927981, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723928016, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723928019, "dur": 109, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723928136, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723928140, "dur": 106, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723928253, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723928258, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723928292, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723928294, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723928318, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723928323, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723928359, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723928364, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723928392, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723928394, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723928430, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723928435, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723928470, "dur": 1571, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723930053, "dur": 102, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723930159, "dur": 9, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723930171, "dur": 31, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723930204, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723930208, "dur": 88, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723930301, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723930307, "dur": 71, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723930385, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723930390, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723930426, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723930430, "dur": 238, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723930675, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723930680, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723930765, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723930770, "dur": 98, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723930880, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723930885, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723930928, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723930934, "dur": 67, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723931006, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723931010, "dur": 76, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723931088, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723931090, "dur": 103, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723931198, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723931202, "dur": 116, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723931324, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723931328, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723931371, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723931377, "dur": 114, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723931498, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723931502, "dur": 190, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723931699, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723931702, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723931777, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723931781, "dur": 112, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723931899, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723931903, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723931929, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723931932, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723931958, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723931986, "dur": 11, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723932001, "dur": 93, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723932098, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723932124, "dur": 523, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723932652, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723932655, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723932690, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723932694, "dur": 127, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723932827, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723932830, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723932854, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723932856, "dur": 208, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723933071, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723933075, "dur": 102, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723933183, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723933188, "dur": 63, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723933256, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723933260, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723933293, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723933297, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723933338, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723933340, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723933357, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723933360, "dur": 1196, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723934562, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723934567, "dur": 84, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723934658, "dur": 12, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723934672, "dur": 351, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723935027, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723935030, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723935119, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723935125, "dur": 47897, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723983033, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723983039, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723983089, "dur": 1952, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723985050, "dur": 6602, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723991663, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723991670, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723991702, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723991706, "dur": 94, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723991814, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723991820, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723991844, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723991848, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723991866, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723991869, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723991899, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723991906, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723991949, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723991955, "dur": 233, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723992193, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723992196, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723992218, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723992221, "dur": 167, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723992391, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723992394, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723992408, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723992411, "dur": 235, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723992651, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723992655, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723992673, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723992677, "dur": 424, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723993106, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723993110, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723993129, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723993132, "dur": 1023, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723994161, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723994165, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723994189, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723994193, "dur": 183, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723994380, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723994383, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723994403, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723994406, "dur": 286, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723994697, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723994707, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723994728, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723994731, "dur": 300, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723995035, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723995038, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723995053, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723995056, "dur": 343, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723995404, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723995410, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723995428, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723995431, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723995487, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723995505, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723995509, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723995539, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723995543, "dur": 666, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723996214, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723996219, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723996237, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723996240, "dur": 390, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723996634, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723996637, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723996654, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723996662, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723996684, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723996688, "dur": 95, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723996787, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723996790, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723996809, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723996812, "dur": 148, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723996971, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723996977, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723997003, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723997006, "dur": 277, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723997288, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723997293, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723997313, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723997316, "dur": 80, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723997400, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723997403, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723997419, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723997422, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723997466, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723997482, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723997485, "dur": 396, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723997885, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723997889, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723997905, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723997908, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723997957, "dur": 14, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723997974, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723997998, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723998002, "dur": 569, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723998579, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723998583, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723998601, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723998605, "dur": 483, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723999092, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723999095, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723999115, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723999121, "dur": 213, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723999337, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723999340, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723999355, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723999357, "dur": 180, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723999542, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723999545, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723999562, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723999565, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723999602, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723999605, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723999622, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723999625, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723999646, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723999648, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723999683, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723999686, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723999701, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723999703, "dur": 221, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723999929, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723999932, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723999977, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362723999982, "dur": 126, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724000112, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724000116, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724000146, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724000151, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724000168, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724000171, "dur": 493, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724000668, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724000672, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724000689, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724000692, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724000710, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724000730, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724000733, "dur": 902, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724001640, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724001643, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724001662, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724001665, "dur": 93, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724001766, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724001772, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724001793, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724001795, "dur": 416, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724002218, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724002222, "dur": 131, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724002358, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724002361, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724002395, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724002399, "dur": 1082, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724003485, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724003488, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724003507, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724003510, "dur": 149, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724003664, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724003668, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724003690, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724003693, "dur": 81, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724003778, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724003781, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724003798, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724003801, "dur": 243, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004048, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004051, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004075, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004079, "dur": 120, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004202, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004205, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004221, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004224, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004240, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004243, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004259, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004287, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004290, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004310, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004313, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004332, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004335, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004357, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004361, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004386, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004389, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004409, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004412, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004428, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004431, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004457, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004461, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004539, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004542, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004557, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004560, "dur": 81, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004646, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004650, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004675, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004695, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004714, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004718, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004742, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004745, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004775, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004778, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004801, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004804, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004822, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004825, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004840, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004843, "dur": 73, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004925, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004929, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004947, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004950, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004973, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724004976, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005000, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005006, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005035, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005040, "dur": 16, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005059, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005062, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005084, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005089, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005114, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005118, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005132, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005135, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005153, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005156, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005174, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005178, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005194, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005198, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005213, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005216, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005239, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005242, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005257, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005260, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005296, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005299, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005316, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005320, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005343, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005347, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005364, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005368, "dur": 12, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005382, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005384, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005401, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005404, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005418, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005421, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005438, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005441, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005464, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005466, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005491, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005511, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005514, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005532, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005535, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005558, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005561, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005577, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005580, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005602, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005605, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005622, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005625, "dur": 52, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005682, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005686, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005708, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005712, "dur": 23, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005739, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005744, "dur": 18, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005764, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005768, "dur": 16, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005787, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005791, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005815, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005820, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005841, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005845, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005869, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005873, "dur": 19, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005895, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005898, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005939, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005943, "dur": 19, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005966, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724005991, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724006023, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724006028, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724006051, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724006055, "dur": 18, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724006077, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724006080, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724006105, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724006109, "dur": 14, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724006126, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724006129, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724006150, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724006154, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724006189, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724006193, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724006238, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724006245, "dur": 147, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724006399, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724006404, "dur": 35, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724006445, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724006450, "dur": 120, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724006576, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724006580, "dur": 115, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724006702, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724006718, "dur": 62, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724006791, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362724006794, "dur": 8320197, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732327003, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732327010, "dur": 113, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732327129, "dur": 25, "ph": "X", "name": "ProcessMessages 1632", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732327157, "dur": 6102, "ph": "X", "name": "ReadAsync 1632", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732333269, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732333275, "dur": 118, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732333399, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732333404, "dur": 73029, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732406446, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732406453, "dur": 120, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732406579, "dur": 5, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732406586, "dur": 198063, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732604686, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732604694, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732604786, "dur": 27, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732604815, "dur": 16804, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732621634, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732621641, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732621703, "dur": 5, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732621710, "dur": 1173, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732622892, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732622897, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732622932, "dur": 33, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732622989, "dur": 14967, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732637982, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732637988, "dur": 134, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732638129, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732638138, "dur": 1239, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732639384, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732639387, "dur": 120, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732639514, "dur": 26, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732639542, "dur": 69848, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732709415, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732709422, "dur": 211, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732709653, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732709660, "dur": 555, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732710222, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732710226, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732710272, "dur": 32, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732710307, "dur": 941, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732711267, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732711270, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732711308, "dur": 461, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751362732712013, "dur": 26452, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 14916, "tid": 706, "ts": 1751362732754187, "dur": 4350, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 14916, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 14916, "tid": 8589934592, "ts": 1751362723754272, "dur": 341630, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 14916, "tid": 8589934592, "ts": 1751362724095906, "dur": 8, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 14916, "tid": 8589934592, "ts": 1751362724095915, "dur": 1205, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 14916, "tid": 706, "ts": 1751362732758540, "dur": 28, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 14916, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 14916, "tid": 4294967296, "ts": 1751362723729479, "dur": 9010644, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 14916, "tid": 4294967296, "ts": 1751362723734398, "dur": 10341, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 14916, "tid": 4294967296, "ts": 1751362732740417, "dur": 5048, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 14916, "tid": 4294967296, "ts": 1751362732743504, "dur": 125, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 14916, "tid": 4294967296, "ts": 1751362732745554, "dur": 16, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 14916, "tid": 706, "ts": 1751362732758571, "dur": 27, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751362723841959, "dur": 37938, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751362723879905, "dur": 1599, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751362723881603, "dur": 140, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751362723881743, "dur": 336, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751362723882576, "dur": 103, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_3EA836CAC6D1710A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751362723883151, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_2E3E35D488CA3F33.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751362723883599, "dur": 378, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_50CFC6D0BF59D948.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751362723884271, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751362723884412, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_DE5514ADFF521C81.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751362723885211, "dur": 222, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Tilemap.Editor.ref.dll_09A2B486E6034DFE.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751362723885665, "dur": 99, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PlasticSCM.Editor.ref.dll_EBB1919501649DC3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751362723886398, "dur": 183, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.ref.dll_2B244DB205431D5A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751362723887484, "dur": 211, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751362723887788, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.GPUDriven.Runtime.ref.dll_94DF45657C08BC0C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751362723887889, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751362723888704, "dur": 278, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751362723889277, "dur": 92, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ShaderGraph.Utilities.ref.dll_E95AACB3AB3AB34A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751362723889399, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751362723889654, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751362723890250, "dur": 128, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751362723890654, "dur": 97, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751362723890828, "dur": 97, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751362723891000, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751362723891148, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualEffectGraph.Editor.ref.dll_DAF69767FB93CC0F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751362723891612, "dur": 115, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751362723891849, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Psdimporter.Editor.ref.dll_9BA16E20606C1C49.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751362723891960, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751362723892093, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751362723892633, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.ForUI.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751362723892752, "dur": 92, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751362723892951, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751362723893123, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751362723893549, "dur": 111, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_EEDCF50BDD4614C9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751362723893760, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.SettingsProvider.Editor.ref.dll_160037ECA11C30F5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751362723894287, "dur": 128, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751362723899848, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualEffectGraph.Runtime.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751362723903876, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15226007567190505770.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751362723882105, "dur": 22937, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751362723905059, "dur": 8804901, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751362732709962, "dur": 530, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751362732710517, "dur": 63, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751362732710587, "dur": 172, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751362732710965, "dur": 21960, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751362723882932, "dur": 22165, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723905100, "dur": 560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_7B22B0BDD3082718.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751362723905662, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723906019, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_5C2FAC469D562E01.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751362723906176, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_B70C9B9527050002.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751362723906425, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723906710, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723906848, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723906919, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723907011, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723907102, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723907184, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751362723907249, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723907425, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723907668, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723908007, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723908126, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723908245, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723908413, "dur": 327, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723908748, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723908914, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723909172, "dur": 301, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723909499, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723910104, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723910222, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723910290, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723910558, "dur": 1140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723911698, "dur": 992, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723912690, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723912955, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723913169, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723913398, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723913901, "dur": 1891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723915792, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723916021, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723916254, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723916540, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723916772, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723917001, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723917272, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723917527, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723917795, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723918034, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723918272, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723918538, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723918760, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723918982, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723919218, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723919531, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723919785, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723920053, "dur": 120, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723920173, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723920528, "dur": 1281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723921809, "dur": 486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723922296, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751362723922502, "dur": 508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751362723923011, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723923135, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751362723923292, "dur": 323, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723923619, "dur": 1217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751362723924836, "dur": 477, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723925319, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751362723925491, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751362723925675, "dur": 640, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751362723926315, "dur": 764, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723927091, "dur": 744, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1751362723927886, "dur": 129, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723928182, "dur": 54514, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1751362723989247, "dur": 2360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751362723991608, "dur": 780, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723992392, "dur": 2184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751362723994577, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723994775, "dur": 1878, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751362723996695, "dur": 2616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751362723999312, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362723999377, "dur": 2846, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/ScriptablePacker.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751362724002224, "dur": 680, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362724002910, "dur": 2386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751362724005499, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362724005642, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.IK.Runtime.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751362724005761, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362724005984, "dur": 90858, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362724098236, "dur": 167, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 1, "ts": 1751362724098403, "dur": 1043, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 1, "ts": 1751362724099446, "dur": 81, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 1, "ts": 1751362724096843, "dur": 2688, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751362724099531, "dur": 8610411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723882502, "dur": 22563, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723905082, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723905197, "dur": 612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Apple.Extensions.Common.dll_D4E020400338F5BB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751362723905810, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723905989, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723906154, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_E951570DA9D21231.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751362723906333, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_634E261A11766DA3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751362723906730, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723906914, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723907015, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723907190, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723907251, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723907380, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723907651, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723907750, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723907811, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723907930, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723907992, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723908210, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723908386, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723908485, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723908734, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723908882, "dur": 324, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723909243, "dur": 412, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723909735, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723910143, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723910288, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723910558, "dur": 1734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723912292, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723912496, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723912704, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723912941, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723913231, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723913777, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723914031, "dur": 2007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723916038, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723916291, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723916524, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723916739, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723917085, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723917329, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723917566, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723917835, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723918054, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723918258, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723918492, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723918715, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723918951, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723919216, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723919482, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723919696, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723919913, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723920168, "dur": 387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723920556, "dur": 1200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723921808, "dur": 529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723922338, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751362723922656, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723922756, "dur": 683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751362723923440, "dur": 644, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723924089, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751362723924265, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723924328, "dur": 836, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751362723925213, "dur": 989, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751362723926203, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723926438, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/ScriptablePacker.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751362723926618, "dur": 440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/ScriptablePacker.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751362723927093, "dur": 158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723927251, "dur": 2531, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723929782, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723929941, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751362723930134, "dur": 477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751362723930612, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723930695, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723930755, "dur": 2068, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723932825, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751362723933017, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723933083, "dur": 56168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723989252, "dur": 2615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751362723991868, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723991927, "dur": 5067, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751362723997046, "dur": 2162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751362723999208, "dur": 452, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362723999660, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751362723999713, "dur": 3955, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751362724003669, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362724004174, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362724004533, "dur": 402, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362724004980, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362724005111, "dur": 441, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362724005603, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362724005830, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362724006074, "dur": 8615103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751362732621209, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751362732621183, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751362732621305, "dur": 88644, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723883524, "dur": 21667, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723905192, "dur": 586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_E4B698B849B24AEB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751362723905779, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723905875, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_3713E40F818C5856.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751362723905943, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723906025, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_502EE2D33971F102.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751362723906150, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723906299, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_5312197D285F8F1E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751362723906730, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723906930, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723907019, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723907107, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723907239, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723907322, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723907425, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723907510, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723907604, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723907811, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723908005, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723908147, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723908282, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723908544, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723908718, "dur": 403, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723909126, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723909255, "dur": 333, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723909601, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723910139, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723910274, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723910348, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723910659, "dur": 1433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723912093, "dur": 793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723912886, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723913153, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723913461, "dur": 487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723913948, "dur": 2798, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723916746, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723917109, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723917444, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723917669, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723917901, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723918238, "dur": 609, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@7bffd5bb179a\\Runtime\\Debugging\\Prefabs\\Scripts\\DebugUIHandlerToggle.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751362723918137, "dur": 831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723918968, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723919189, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723919491, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723919707, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723919954, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723920193, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723920863, "dur": 902, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723921765, "dur": 715, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723922481, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751362723922680, "dur": 575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751362723923255, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723923540, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751362723924216, "dur": 668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751362723924885, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723924959, "dur": 931, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751362723925891, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723926178, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723926365, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751362723926507, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723926658, "dur": 466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751362723927167, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723927246, "dur": 2049, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723929296, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751362723929415, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751362723929741, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723929892, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751362723930391, "dur": 2443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723932835, "dur": 56446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723989282, "dur": 2276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751362723991559, "dur": 564, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723992130, "dur": 3686, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751362723995817, "dur": 549, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362723996373, "dur": 3428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751362723999802, "dur": 1289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362724001097, "dur": 2229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualEffectGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751362724003327, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362724003394, "dur": 2492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751362724005941, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751362724006174, "dur": 8703787, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723882883, "dur": 22204, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723905094, "dur": 571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_7A744375D0C40DCD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751362723905665, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723905803, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_966AC74B818A5F90.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751362723905917, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_A1C14A714FD44069.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751362723906151, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_DA3F53EC74158B55.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751362723906271, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_C3115380BB649476.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751362723906731, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723907029, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723907099, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723907227, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751362723907279, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723907436, "dur": 332, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723907800, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723907859, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723907917, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723908114, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723908230, "dur": 413, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723908669, "dur": 469, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723909142, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723909410, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723909667, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723910115, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723910275, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723910602, "dur": 1355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723911958, "dur": 2039, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723913997, "dur": 1991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723915988, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723916228, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723916467, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723916690, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723917195, "dur": 551, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@ba0fe1b085e9\\Runtime\\Utilities\\Playables\\VisualEffectControl\\VisualEffectControlTrack.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751362723916917, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723917789, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723918096, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723918397, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723918643, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723918896, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723919137, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723919376, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723919627, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723919928, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723920645, "dur": 1113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723921758, "dur": 535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723922294, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751362723922508, "dur": 650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751362723923159, "dur": 364, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723923531, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723923597, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751362723923817, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751362723924002, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723924061, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751362723924310, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723924446, "dur": 858, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751362723925342, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751362723925502, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723925623, "dur": 1574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751362723927197, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723927455, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751362723927617, "dur": 1180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751362723928798, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723928905, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751362723929102, "dur": 480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751362723929583, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723929784, "dur": 1116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723930900, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751362723931454, "dur": 1383, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723932837, "dur": 58727, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723991565, "dur": 2301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751362723993897, "dur": 1980, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751362723995878, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723995947, "dur": 2326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751362723998274, "dur": 796, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362723999076, "dur": 4022, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751362724003101, "dur": 686, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751362724003792, "dur": 2398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751362724006249, "dur": 8703685, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751362723882711, "dur": 22363, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751362723905082, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751362723905207, "dur": 523, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_7D19F4582D6FA60D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751362723905762, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_24CD4430EA38EE97.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751362723905900, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_68F0E05FD8235E34.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751362723906112, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751362723906175, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_24D87271FC85A7AF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751362723906434, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751362723906752, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751362723906828, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751362723906923, "dur": 9977, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751362723917002, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751362723917221, "dur": 4488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751362723921804, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751362723921922, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751362723922287, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751362723922477, "dur": 586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751362723923115, "dur": 865, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751362723923988, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751362723924180, "dur": 1114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751362723925334, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751362723925486, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751362723925703, "dur": 1277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751362723926980, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751362723927194, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751362723927426, "dur": 539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751362723927966, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751362723928050, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751362723928162, "dur": 489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751362723928745, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751362723928862, "dur": 1142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751362723930004, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751362723930085, "dur": 451, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751362723930541, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751362723930687, "dur": 966, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751362723931731, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751362723931837, "dur": 507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751362723932404, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751362723932503, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751362723932824, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751362723932984, "dur": 56298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751362723989283, "dur": 2216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751362723991500, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751362723991622, "dur": 2242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751362723993918, "dur": 2266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751362723996188, "dur": 832, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751362723997026, "dur": 2298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751362723999325, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751362723999425, "dur": 2036, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751362724001462, "dur": 1170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751362724002639, "dur": 2406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751362724005046, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751362724005383, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751362724005657, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751362724005935, "dur": 200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751362724006135, "dur": 8703828, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723883058, "dur": 22086, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723905147, "dur": 677, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_89517EAC2A874601.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751362723905825, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723905970, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_EB030C3D9E9F35E6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751362723906028, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723906091, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_3EA836CAC6D1710A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751362723906260, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_B72398FA1A1CE63D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751362723906449, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723906683, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723906855, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723907087, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723907231, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723907323, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723907429, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751362723907481, "dur": 288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723907865, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723908077, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723908190, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723908288, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723908421, "dur": 378, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723908810, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723908995, "dur": 765, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723909815, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11908194702949990099.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751362723910015, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723910081, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3925285597375108267.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751362723910192, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723910285, "dur": 576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723910862, "dur": 1418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723912280, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723912523, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723912756, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723913005, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723913263, "dur": 489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723913752, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723914021, "dur": 1782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723915803, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723916019, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723916269, "dur": 417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723916686, "dur": 684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723917370, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723917874, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723918100, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723918463, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723918742, "dur": 590, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\Unity\\LayerMask_DirectConverter.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751362723918676, "dur": 820, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723919497, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723919748, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723920003, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723920312, "dur": 998, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723921310, "dur": 501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723921811, "dur": 505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723922330, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751362723922480, "dur": 650, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723923132, "dur": 623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751362723923756, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723923850, "dur": 515, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723924371, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751362723924570, "dur": 361, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723924934, "dur": 666, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751362723925601, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723925782, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723925956, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751362723926241, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723926404, "dur": 700, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751362723927135, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723927247, "dur": 2547, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723929794, "dur": 3032, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723932827, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751362723933025, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723933080, "dur": 56199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723989281, "dur": 2300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751362723991633, "dur": 2260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751362723993894, "dur": 1380, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723995279, "dur": 2479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751362723997759, "dur": 1515, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362723999280, "dur": 2187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751362724001498, "dur": 2613, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-unity-editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751362724004112, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362724004302, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362724004576, "dur": 327, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362724004989, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362724005095, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362724005338, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362724005416, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362724005501, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362724005592, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362724005910, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362724005988, "dur": 8324698, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362732330711, "dur": 72913, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751362732330689, "dur": 74255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751362732405983, "dur": 192, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751362732406670, "dur": 215863, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751362732637421, "dur": 71554, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751362732637414, "dur": 71563, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751362732708994, "dur": 899, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751362723883224, "dur": 21932, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723905159, "dur": 513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_98CF6329D3D6F9AB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751362723905673, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723905743, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_7BCDC5BDF664862C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751362723905923, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_F48E27B3AC5122EF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751362723906006, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_C11B61CB0C885270.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751362723906165, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723906279, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_B9FBC03C59DCE731.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751362723906428, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_E2C1B1DA45896D6E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751362723906738, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723907092, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723907222, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723907321, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723907425, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723907485, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723907745, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723907824, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723907887, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723907974, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723908187, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723908299, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723908580, "dur": 386, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723909004, "dur": 407, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723909414, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751362723909494, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723909734, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723910217, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723910283, "dur": 444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723910727, "dur": 1485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723912212, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723912404, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723912664, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723912873, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723913094, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723913314, "dur": 544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723913858, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723914092, "dur": 188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723914280, "dur": 156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723914436, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723914639, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723914832, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723915022, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723915207, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723915475, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723915681, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723915894, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723916147, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723916410, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723916620, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723916862, "dur": 358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723917221, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723917525, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723917784, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723918233, "dur": 523, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@24b10291b18f\\Runtime\\TMP\\TMP_StyleSheet.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751362723918015, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723918771, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723919023, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723919258, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723919494, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723919789, "dur": 607, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.tilemap@91020cbfae56\\Editor\\PaintableSceneViewGrid.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751362723919741, "dur": 929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723920670, "dur": 1092, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723921762, "dur": 529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723922292, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751362723922480, "dur": 518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751362723922999, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723923176, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751362723923358, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751362723923540, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723923628, "dur": 903, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751362723924532, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723924663, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751362723924868, "dur": 643, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751362723925511, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723925590, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723925701, "dur": 648, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751362723926376, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751362723926580, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723926662, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751362723926743, "dur": 569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751362723927313, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723927417, "dur": 2377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723929794, "dur": 3038, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723932832, "dur": 56422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723989254, "dur": 2338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751362723991594, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723991671, "dur": 2270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751362723993948, "dur": 475, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723994430, "dur": 2558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-unity.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751362723996988, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362723997206, "dur": 2635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/NativeFilePicker.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751362723999885, "dur": 2761, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751362724002647, "dur": 1419, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362724004074, "dur": 627, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362724004731, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362724004936, "dur": 398, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362724005401, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362724005597, "dur": 624, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751362724006252, "dur": 8703716, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723883298, "dur": 21864, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723905165, "dur": 662, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_AAD14E71D524EA60.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751362723906107, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723906170, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_EFD593047BD2E6A8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751362723906282, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_3CE377B30C500CB9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751362723906345, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723906419, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_173B2236195846C7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751362723906723, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723906847, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723907029, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_AEBFCF2A2C8F2407.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751362723907235, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723907312, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723907422, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723907588, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723907804, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723907918, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723908053, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723908225, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723908377, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723908613, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723909008, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723909128, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723909284, "dur": 442, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723909759, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723909858, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723909995, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723910114, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723910194, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723910291, "dur": 339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723910630, "dur": 1520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723912150, "dur": 343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723912493, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723912694, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723912991, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723913213, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723913460, "dur": 484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723913945, "dur": 2007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723915952, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723916173, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723916778, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723917144, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723917404, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723917647, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723917881, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723918108, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723918327, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723918567, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723918778, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723918990, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723919234, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723919476, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723919715, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723920100, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723920415, "dur": 113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723920528, "dur": 1279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723921807, "dur": 670, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723922478, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751362723922703, "dur": 601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751362723923304, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723923424, "dur": 416, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723923844, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751362723924036, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751362723924236, "dur": 772, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751362723925009, "dur": 727, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723925745, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723925990, "dur": 666, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751362723926657, "dur": 424, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723927098, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723927198, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751362723927425, "dur": 455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751362723927881, "dur": 341, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723928247, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751362723928347, "dur": 517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751362723928865, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723928980, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723929256, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751362723929379, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751362723929767, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751362723929953, "dur": 558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751362723930566, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751362723930680, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751362723931081, "dur": 1754, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723932835, "dur": 56421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723989273, "dur": 2266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/OutlineFx.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751362723991543, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723991604, "dur": 2258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751362723993901, "dur": 2474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp-firstpass.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751362723996408, "dur": 2222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751362723998630, "dur": 455, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362723999092, "dur": 2268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751362724001395, "dur": 2210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751362724003606, "dur": 458, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362724004095, "dur": 588, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362724004855, "dur": 519, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362724005688, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362724005959, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751362724006420, "dur": 8703527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723883427, "dur": 21750, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723905178, "dur": 679, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_21E5C8D6C2CA360E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751362723905895, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_B10B806206A4C9C0.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751362723906011, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_33DE0D3A006A6424.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751362723906172, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_8AA30A6C8DDCA8A3.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751362723906457, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723906697, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723906930, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723907015, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723907236, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723907464, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723907698, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723907771, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723907894, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723908087, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723908227, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723908369, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723908478, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723908806, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723908996, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723909188, "dur": 299, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723909490, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723909686, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723910004, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12140925242746971147.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751362723910157, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723910312, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723910841, "dur": 1367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723912209, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723912460, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723912664, "dur": 513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723913178, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723913488, "dur": 509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723913997, "dur": 664, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.animation@494a3b4e73a9\\Editor\\SkinningModule\\ShortcutUtility.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751362723913997, "dur": 2129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723916126, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723916354, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723916623, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723916904, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723917206, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723917441, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723917710, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723917930, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723918161, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723918420, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723918670, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723918916, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723919176, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723919427, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723919651, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723919925, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723920178, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723920519, "dur": 1242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723921761, "dur": 579, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723922340, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751362723922529, "dur": 509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751362723923076, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723923336, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751362723923500, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723923657, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751362723923866, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751362723923997, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723924262, "dur": 769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751362723925032, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723925242, "dur": 696, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751362723925938, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723926142, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723926348, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751362723926528, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723926597, "dur": 511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751362723927171, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751362723927357, "dur": 466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751362723927856, "dur": 1912, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723929783, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751362723929929, "dur": 464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751362723930426, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723930517, "dur": 2318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723932835, "dur": 56418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723989254, "dur": 2014, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751362723991269, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723991362, "dur": 2415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-csharp.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751362723993778, "dur": 1442, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723995228, "dur": 2526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751362723997755, "dur": 553, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362723998313, "dur": 2043, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751362724000402, "dur": 2411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751362724002814, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362724003070, "dur": 2727, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/OutlineFx.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751362724005797, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362724005985, "dur": 93622, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751362724099607, "dur": 8610331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723883168, "dur": 21982, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723905152, "dur": 683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_3122CFBB2DC47329.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751362723905836, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723905893, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_5A6241C5E4FBF31E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751362723906075, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_366576C77F483F0B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751362723906168, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_A5513E9150BC6315.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751362723906280, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_D5FB1AB832332DD5.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751362723906355, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723906452, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_ACB0EC32CF196063.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751362723906694, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723906840, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751362723906894, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723907083, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723907184, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723907263, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723907443, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723907611, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723907724, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723907840, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723907947, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723908078, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723908186, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723908290, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723908419, "dur": 324, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723908755, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723908992, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723909165, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723909476, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723909627, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723910196, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723910255, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723910539, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723910744, "dur": 1619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723912364, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723912657, "dur": 594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723913251, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723913473, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723913981, "dur": 1967, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723915949, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723916219, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723916470, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723916743, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723917005, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723917333, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723917572, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723917829, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723918090, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723918312, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723918566, "dur": 284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723918850, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723919058, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723919290, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723919586, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723919879, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723920184, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723920601, "dur": 1153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723921779, "dur": 508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723922288, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751362723922505, "dur": 663, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751362723923169, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723923357, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751362723923523, "dur": 981, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751362723924505, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723924647, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751362723924787, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723924857, "dur": 753, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751362723925610, "dur": 489, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723926108, "dur": 624, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723926732, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.SpriteShape.Runtime.ref.dll_42C04488B87F7BCB.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751362723926869, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751362723927064, "dur": 563, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751362723927628, "dur": 426, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723928103, "dur": 1683, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723929787, "dur": 2622, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723932410, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751362723932572, "dur": 457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751362723933070, "dur": 486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751362723933600, "dur": 514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751362723934631, "dur": 86, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362723935337, "dur": 8391304, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751362732331113, "dur": 1698, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751362732330650, "dur": 2250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751362732333468, "dur": 70160, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751362732332937, "dur": 71572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751362732405657, "dur": 196, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751362732406666, "dur": 197635, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751362732621174, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1751362732621166, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1751362732621302, "dur": 88648, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751362723883348, "dur": 21822, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751362723905172, "dur": 660, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_D07FFB3B2770754A.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751362723905892, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_07ED47E0EFE03F3D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751362723906142, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_41563AEB7BF951B8.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751362723906427, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751362723906687, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751362723906793, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751362723906894, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751362723906952, "dur": 9540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751362723916545, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751362723916620, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751362723916877, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751362723917142, "dur": 360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751362723917503, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751362723917758, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751362723917995, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751362723918230, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751362723918545, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751362723918816, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751362723919052, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751362723919298, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751362723919524, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751362723919748, "dur": 376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751362723920125, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751362723920403, "dur": 126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751362723920529, "dur": 1281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751362723921810, "dur": 478, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751362723922289, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751362723922450, "dur": 388, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751362723922845, "dur": 1463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751362723924309, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751362723924597, "dur": 729, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751362723925334, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751362723925538, "dur": 753, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751362723926292, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751362723926414, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751362723926593, "dur": 449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751362723927042, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751362723927196, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751362723927419, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751362723927562, "dur": 360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751362723927922, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751362723928085, "dur": 1704, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751362723929789, "dur": 3044, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751362723932833, "dur": 56440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751362723989273, "dur": 2266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751362723991541, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751362723991602, "dur": 2260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751362723993907, "dur": 2355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751362723996263, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751362723996406, "dur": 2966, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751362723999373, "dur": 1049, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751362724000426, "dur": 2652, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751362724003079, "dur": 857, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751362724003943, "dur": 2382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751362724006374, "dur": 8703584, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723883509, "dur": 21674, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723905185, "dur": 533, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_D5DC31B4B0DA901E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751362723905751, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_D8154579280ABE30.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751362723905837, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723905896, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_692349F0772110A2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751362723905985, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_60DEBCA51ABA1468.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751362723906105, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723906307, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_A0847C1FE8DF0FE2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751362723906436, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723906684, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723906829, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723907024, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723907094, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723907224, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723907315, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723907442, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751362723907499, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723907759, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723907814, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723907910, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723908100, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723908194, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723908418, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723908645, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723908865, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723909150, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723909421, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751362723909502, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723909742, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723910294, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723910601, "dur": 1631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723912232, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723912425, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723912631, "dur": 500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723913131, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723913351, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723915964, "dur": 770, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.animation@494a3b4e73a9\\Editor\\SpriteLib\\SpriteSwapOverlay\\SpriteResolverSelector.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751362723913922, "dur": 3168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723917091, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723917587, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723917826, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723918060, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723918332, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723918542, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723918802, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723919085, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723919295, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723919521, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723919770, "dur": 360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723920130, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723920520, "dur": 1237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723921757, "dur": 537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723922295, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751362723922451, "dur": 655, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723923112, "dur": 670, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751362723923862, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751362723924238, "dur": 1001, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751362723925239, "dur": 316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723925569, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723925705, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity-editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751362723926058, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723926115, "dur": 718, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity-editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751362723926834, "dur": 340, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723927178, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723927246, "dur": 1502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723928750, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751362723928885, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723928963, "dur": 451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751362723929479, "dur": 309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723929789, "dur": 3040, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723932829, "dur": 56455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723989285, "dur": 3073, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751362723992359, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723992532, "dur": 2206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751362723994782, "dur": 2314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751362723997140, "dur": 2539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751362723999680, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362723999848, "dur": 2212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751362724002090, "dur": 2279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751362724004370, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362724004576, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362724004663, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362724004857, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362724005302, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362724005414, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362724005480, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362724005602, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1751362724005895, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362724005945, "dur": 324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751362724006269, "dur": 8703688, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723883571, "dur": 21685, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723905256, "dur": 467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_FB049F22F4C5497A.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751362723905753, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_385F174668EB898F.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751362723906013, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_FA0E2521CCF3B353.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751362723906146, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723906320, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_4DE7717F601CC2DC.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751362723906736, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723907115, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723907270, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723907433, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723907551, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723907763, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723907898, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723908039, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723908176, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723908300, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723908500, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723908875, "dur": 337, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723909215, "dur": 535, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723910073, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9655524186228112463.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751362723910127, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723910266, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723910330, "dur": 576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723910906, "dur": 1489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723912395, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723912618, "dur": 390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723913008, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723913341, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723913873, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723914107, "dur": 163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723914271, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723914472, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723914664, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723914837, "dur": 142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723914980, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723915147, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723915317, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723915513, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723915714, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723915951, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723916215, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723916479, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723916742, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723916986, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723917301, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723917543, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723917812, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723918040, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723918298, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723918526, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723918763, "dur": 727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723919490, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723919744, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723920060, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723920364, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723920682, "dur": 1076, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723921759, "dur": 576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723922337, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751362723922578, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723922770, "dur": 658, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751362723923513, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751362723923679, "dur": 333, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723924015, "dur": 1859, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751362723925874, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723925959, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723926035, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723926170, "dur": 686, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723926858, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723927108, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723927197, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751362723927405, "dur": 434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751362723927912, "dur": 1878, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723929790, "dur": 3038, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723932828, "dur": 56418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723989248, "dur": 2218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751362723991466, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723991613, "dur": 2471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751362723994122, "dur": 3145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualEffectGraph.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751362723997268, "dur": 408, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362723997682, "dur": 2272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751362723999955, "dur": 1419, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362724001380, "dur": 2072, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751362724003453, "dur": 351, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751362724003809, "dur": 2315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751362724006160, "dur": 8703785, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723883555, "dur": 21643, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723905200, "dur": 634, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_BA35B04A057142E1.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751362723905834, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723905898, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_D0FAA40E40B36FD7.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751362723905996, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_C0823A9ADF385DE2.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751362723906118, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723906479, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723906688, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723906854, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723906930, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723907097, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723907192, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1751362723907285, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723907449, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723907598, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723907908, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723907973, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723908144, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723908321, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723908452, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723908680, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723908960, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723909167, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723909483, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723909717, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723909954, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12044039279616155683.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751362723910124, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723910311, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723910601, "dur": 1497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723912099, "dur": 824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723912923, "dur": 887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723913810, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723914047, "dur": 1081, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723915128, "dur": 167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723915295, "dur": 160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723915456, "dur": 152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723915608, "dur": 147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723915755, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723915994, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723916243, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723916484, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723916746, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723917001, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723917312, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723917561, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723917789, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723918046, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723918310, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723918546, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723918900, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723919182, "dur": 711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723919893, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723920161, "dur": 350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723920523, "dur": 1238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723921761, "dur": 529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723922294, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751362723922450, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723922526, "dur": 548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751362723923075, "dur": 539, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723923622, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723923771, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751362723923989, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751362723924422, "dur": 689, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751362723925112, "dur": 395, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723925523, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751362723925800, "dur": 848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751362723926648, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723926825, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723927117, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723927199, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751362723927424, "dur": 578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751362723928057, "dur": 1709, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723929767, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751362723929960, "dur": 717, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751362723930723, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723930898, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751362723931032, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751362723931385, "dur": 1451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723932836, "dur": 56451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723989287, "dur": 2701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751362723991989, "dur": 388, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723992383, "dur": 2443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751362723994827, "dur": 4506, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362723999339, "dur": 2219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751362724001559, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751362724001634, "dur": 2098, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751362724003787, "dur": 2555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751362724006393, "dur": 8703571, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723883701, "dur": 21547, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723905252, "dur": 554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_57E453305C48C0D1.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751362723905997, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_636C45FB8E78E9FC.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751362723906074, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_88313D863DE50351.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751362723906143, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723906258, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_4C3484A57A05DA64.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751362723906363, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_4AAF7BB67CBB0CA5.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751362723906718, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723906915, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723907031, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723907233, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723907335, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723907534, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723907749, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723907956, "dur": 299, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723908258, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723908394, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723908599, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723908757, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723908919, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723909142, "dur": 485, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723909687, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723910042, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723910150, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723910249, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723910777, "dur": 1409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723912187, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723912605, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723912818, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723913095, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723913343, "dur": 518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723913861, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723914068, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723914272, "dur": 179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723914451, "dur": 181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723914632, "dur": 171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723914803, "dur": 132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723914935, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723915159, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723915352, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723915567, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723915769, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723915973, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723917292, "dur": 843, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.animation@494a3b4e73a9\\Runtime\\BatchedDeformation\\DeformationJobs.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751362723916205, "dur": 1930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723918136, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723918364, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723918566, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723918787, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723919029, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723919302, "dur": 788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723920090, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723920325, "dur": 894, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723921219, "dur": 589, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723921808, "dur": 530, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723922339, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751362723922510, "dur": 610, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723923123, "dur": 949, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751362723924072, "dur": 381, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723924504, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751362723924736, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723924948, "dur": 610, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751362723925596, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751362723925791, "dur": 597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751362723926388, "dur": 297, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723926686, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751362723926806, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723926872, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723927092, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723927249, "dur": 2542, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723929792, "dur": 3038, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723932831, "dur": 56418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723989251, "dur": 3573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751362723992860, "dur": 2242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751362723995152, "dur": 2341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751362723997493, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362723997622, "dur": 2781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751362724000454, "dur": 2213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751362724002668, "dur": 373, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362724003046, "dur": 2873, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751362724005956, "dur": 445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751362724006401, "dur": 8703539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723883816, "dur": 21384, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723905202, "dur": 529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_8AA31634E5830E2F.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751362723905759, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_24AD401155842AFB.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751362723905871, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_50CFC6D0BF59D948.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751362723905961, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_B5214F7A6C172998.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751362723906043, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_D35CED970F7F1F3B.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751362723906139, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_15728F368AD157F8.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751362723906234, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723906300, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_06CB192F9C3927A4.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751362723906481, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723906693, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723906824, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723906925, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723907030, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723907100, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723907189, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1751362723907265, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723907422, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723907501, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723907617, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723907816, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723907895, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723908051, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723908198, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723908387, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723908491, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723908687, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723908851, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723909141, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723909292, "dur": 335, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723909651, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723909808, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751362723910069, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13473629827552830373.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751362723910119, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723910270, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723910605, "dur": 1496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723912101, "dur": 596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723912697, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723912969, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723913240, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723913466, "dur": 502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723913968, "dur": 1955, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723915923, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723916142, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723916363, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723916568, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723916824, "dur": 358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723917182, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723917442, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723917731, "dur": 519, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.Core\\Product\\LudiqProduct.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751362723917660, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723918369, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723918615, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723918853, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723919097, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723919308, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723919548, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723919791, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723920071, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723920324, "dur": 1073, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723921397, "dur": 368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723921766, "dur": 564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723922331, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751362723922480, "dur": 662, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723923146, "dur": 619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751362723923766, "dur": 348, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723924122, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723924267, "dur": 550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751362723924818, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723924980, "dur": 414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751362723925457, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751362723925733, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723925993, "dur": 633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751362723926626, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723926862, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723927097, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723927245, "dur": 247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723927493, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751362723927604, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723927717, "dur": 1103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751362723928821, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723928941, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751362723929122, "dur": 723, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751362723929936, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751362723930111, "dur": 480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751362723930664, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751362723930757, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723930900, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751362723931094, "dur": 448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751362723931599, "dur": 1232, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723932831, "dur": 56454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723989286, "dur": 2210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/NativeFilePicker.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751362723991536, "dur": 2331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751362723993867, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723994118, "dur": 2298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751362723996416, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362723996527, "dur": 2252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751362723998809, "dur": 3075, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751362724001886, "dur": 1613, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362724003507, "dur": 2531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751362724006133, "dur": 8631307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751362732637462, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1751362732637445, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1751362732637594, "dur": 1458, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1751362732639056, "dur": 70897, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751362732736333, "dur": 1556, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 14916, "tid": 706, "ts": 1751362732759343, "dur": 3230, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 14916, "tid": 706, "ts": 1751362732762831, "dur": 2529, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 14916, "tid": 706, "ts": 1751362732751397, "dur": 15342, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}