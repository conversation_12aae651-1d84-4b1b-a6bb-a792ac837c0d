{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 14916, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 14916, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 14916, "tid": 2864, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 14916, "tid": 2864, "ts": 1751368187729363, "dur": 480, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 14916, "tid": 2864, "ts": 1751368187731667, "dur": 634, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 14916, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 14916, "tid": 1, "ts": 1751368186934499, "dur": 5745, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 14916, "tid": 1, "ts": 1751368186940250, "dur": 117906, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 14916, "tid": 1, "ts": 1751368187058181, "dur": 629745, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 14916, "tid": 2864, "ts": 1751368187732307, "dur": 18, "ph": "X", "name": "", "args": {}}, {"pid": 14916, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186932689, "dur": 6240, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186938933, "dur": 782491, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186939843, "dur": 2676, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186942528, "dur": 1626, "ph": "X", "name": "ProcessMessages 20487", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186944161, "dur": 319, "ph": "X", "name": "ReadAsync 20487", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186944487, "dur": 27, "ph": "X", "name": "ProcessMessages 20547", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186944517, "dur": 551, "ph": "X", "name": "ReadAsync 20547", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186945075, "dur": 5, "ph": "X", "name": "ProcessMessages 2778", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186945082, "dur": 127, "ph": "X", "name": "ReadAsync 2778", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186945215, "dur": 12, "ph": "X", "name": "ProcessMessages 8381", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186945229, "dur": 39, "ph": "X", "name": "ReadAsync 8381", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186945272, "dur": 2, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186945275, "dur": 30, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186945309, "dur": 1, "ph": "X", "name": "ProcessMessages 243", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186945311, "dur": 38, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186945351, "dur": 2, "ph": "X", "name": "ProcessMessages 972", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186945355, "dur": 30, "ph": "X", "name": "ReadAsync 972", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186945389, "dur": 1, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186945392, "dur": 29, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186945430, "dur": 3, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186945434, "dur": 246, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186945694, "dur": 3, "ph": "X", "name": "ProcessMessages 941", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186945702, "dur": 64, "ph": "X", "name": "ReadAsync 941", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186945770, "dur": 7, "ph": "X", "name": "ProcessMessages 4779", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186945778, "dur": 67, "ph": "X", "name": "ReadAsync 4779", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186945848, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186945850, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186945887, "dur": 3, "ph": "X", "name": "ProcessMessages 1645", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186945891, "dur": 57, "ph": "X", "name": "ReadAsync 1645", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186945952, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186945954, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186945987, "dur": 2, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186945991, "dur": 22, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946015, "dur": 1, "ph": "X", "name": "ProcessMessages 908", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946018, "dur": 60, "ph": "X", "name": "ReadAsync 908", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946086, "dur": 1, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946089, "dur": 27, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946118, "dur": 2, "ph": "X", "name": "ProcessMessages 1099", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946122, "dur": 24, "ph": "X", "name": "ReadAsync 1099", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946149, "dur": 1, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946152, "dur": 24, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946178, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946181, "dur": 55, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946244, "dur": 1, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946247, "dur": 27, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946277, "dur": 3, "ph": "X", "name": "ProcessMessages 1401", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946282, "dur": 55, "ph": "X", "name": "ReadAsync 1401", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946340, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946343, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946366, "dur": 1, "ph": "X", "name": "ProcessMessages 655", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946368, "dur": 24, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946395, "dur": 1, "ph": "X", "name": "ProcessMessages 179", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946398, "dur": 20, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946420, "dur": 1, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946422, "dur": 22, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946448, "dur": 1, "ph": "X", "name": "ProcessMessages 175", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946451, "dur": 30, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946484, "dur": 2, "ph": "X", "name": "ProcessMessages 958", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946487, "dur": 56, "ph": "X", "name": "ReadAsync 958", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946547, "dur": 1, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946549, "dur": 17, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946569, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946571, "dur": 18, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946593, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946596, "dur": 16, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946621, "dur": 30, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946653, "dur": 2, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946657, "dur": 20, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946679, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946682, "dur": 69, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946757, "dur": 21, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946781, "dur": 2, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946784, "dur": 18, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946808, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946811, "dur": 31, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946847, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946877, "dur": 2, "ph": "X", "name": "ProcessMessages 896", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946881, "dur": 74, "ph": "X", "name": "ReadAsync 896", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946957, "dur": 1, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946960, "dur": 21, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946988, "dur": 2, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186946991, "dur": 22, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947016, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947018, "dur": 26, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947047, "dur": 1, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947050, "dur": 35, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947098, "dur": 2, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947101, "dur": 23, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947131, "dur": 2, "ph": "X", "name": "ProcessMessages 818", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947135, "dur": 27, "ph": "X", "name": "ReadAsync 818", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947164, "dur": 1, "ph": "X", "name": "ProcessMessages 655", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947167, "dur": 24, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947194, "dur": 1, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947196, "dur": 23, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947226, "dur": 2, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947229, "dur": 17, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947252, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947255, "dur": 40, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947299, "dur": 27, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947329, "dur": 2, "ph": "X", "name": "ProcessMessages 1233", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947334, "dur": 70, "ph": "X", "name": "ReadAsync 1233", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947406, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947408, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947434, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947437, "dur": 19, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947458, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947461, "dur": 24, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947489, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947492, "dur": 22, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947516, "dur": 1, "ph": "X", "name": "ProcessMessages 751", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947519, "dur": 19, "ph": "X", "name": "ReadAsync 751", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947540, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947543, "dur": 17, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947566, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947568, "dur": 110, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947683, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947709, "dur": 2, "ph": "X", "name": "ProcessMessages 900", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947712, "dur": 21, "ph": "X", "name": "ReadAsync 900", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947737, "dur": 3, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947746, "dur": 20, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947769, "dur": 2, "ph": "X", "name": "ProcessMessages 673", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947773, "dur": 23, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947797, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947800, "dur": 25, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947828, "dur": 2, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947832, "dur": 25, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947860, "dur": 2, "ph": "X", "name": "ProcessMessages 904", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947863, "dur": 34, "ph": "X", "name": "ReadAsync 904", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947910, "dur": 1, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947913, "dur": 27, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947942, "dur": 1, "ph": "X", "name": "ProcessMessages 1061", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947945, "dur": 25, "ph": "X", "name": "ReadAsync 1061", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947973, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186947977, "dur": 23, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948002, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948006, "dur": 23, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948031, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948033, "dur": 27, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948063, "dur": 1, "ph": "X", "name": "ProcessMessages 805", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948066, "dur": 23, "ph": "X", "name": "ReadAsync 805", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948091, "dur": 1, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948094, "dur": 18, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948114, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948117, "dur": 23, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948143, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948146, "dur": 19, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948171, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948174, "dur": 26, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948202, "dur": 2, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948206, "dur": 23, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948231, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948234, "dur": 14, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948253, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948256, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948280, "dur": 1, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948283, "dur": 19, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948308, "dur": 2, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948311, "dur": 29, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948342, "dur": 2, "ph": "X", "name": "ProcessMessages 721", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948346, "dur": 27, "ph": "X", "name": "ReadAsync 721", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948375, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948377, "dur": 22, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948401, "dur": 1, "ph": "X", "name": "ProcessMessages 745", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948404, "dur": 20, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948427, "dur": 2, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948430, "dur": 21, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948453, "dur": 1, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948456, "dur": 13, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948471, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948473, "dur": 19, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948495, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948497, "dur": 27, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948527, "dur": 1, "ph": "X", "name": "ProcessMessages 725", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948529, "dur": 28, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948561, "dur": 3, "ph": "X", "name": "ProcessMessages 935", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948565, "dur": 16, "ph": "X", "name": "ReadAsync 935", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948587, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948591, "dur": 20, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948617, "dur": 1, "ph": "X", "name": "ProcessMessages 763", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948619, "dur": 21, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948642, "dur": 1, "ph": "X", "name": "ProcessMessages 716", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948645, "dur": 21, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948669, "dur": 1, "ph": "X", "name": "ProcessMessages 655", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948672, "dur": 16, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948690, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948692, "dur": 15, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948714, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948717, "dur": 21, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948748, "dur": 1, "ph": "X", "name": "ProcessMessages 790", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948751, "dur": 28, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948782, "dur": 1, "ph": "X", "name": "ProcessMessages 894", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948784, "dur": 20, "ph": "X", "name": "ReadAsync 894", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948807, "dur": 2, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948810, "dur": 20, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948836, "dur": 2, "ph": "X", "name": "ProcessMessages 775", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948840, "dur": 21, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948867, "dur": 1, "ph": "X", "name": "ProcessMessages 728", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948870, "dur": 21, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948893, "dur": 1, "ph": "X", "name": "ProcessMessages 665", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948896, "dur": 18, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948918, "dur": 5, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948926, "dur": 21, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948953, "dur": 2, "ph": "X", "name": "ProcessMessages 935", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948957, "dur": 22, "ph": "X", "name": "ReadAsync 935", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948985, "dur": 1, "ph": "X", "name": "ProcessMessages 611", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186948987, "dur": 33, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949023, "dur": 2, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949026, "dur": 19, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949048, "dur": 1, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949051, "dur": 21, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949078, "dur": 2, "ph": "X", "name": "ProcessMessages 734", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949081, "dur": 19, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949107, "dur": 2, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949110, "dur": 21, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949133, "dur": 2, "ph": "X", "name": "ProcessMessages 627", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949137, "dur": 21, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949160, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949163, "dur": 20, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949187, "dur": 2, "ph": "X", "name": "ProcessMessages 732", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949192, "dur": 21, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949219, "dur": 1, "ph": "X", "name": "ProcessMessages 637", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949222, "dur": 22, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949246, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949249, "dur": 19, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949270, "dur": 5, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949278, "dur": 23, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949307, "dur": 2, "ph": "X", "name": "ProcessMessages 1089", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949311, "dur": 20, "ph": "X", "name": "ReadAsync 1089", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949336, "dur": 1, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949339, "dur": 23, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949365, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949367, "dur": 29, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949399, "dur": 2, "ph": "X", "name": "ProcessMessages 951", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949403, "dur": 23, "ph": "X", "name": "ReadAsync 951", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949428, "dur": 2, "ph": "X", "name": "ProcessMessages 694", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949431, "dur": 21, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949459, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949462, "dur": 15, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949479, "dur": 1, "ph": "X", "name": "ProcessMessages 222", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949481, "dur": 25, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949509, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949512, "dur": 21, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949540, "dur": 2, "ph": "X", "name": "ProcessMessages 867", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949544, "dur": 18, "ph": "X", "name": "ReadAsync 867", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949564, "dur": 1, "ph": "X", "name": "ProcessMessages 522", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949567, "dur": 16, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949585, "dur": 1, "ph": "X", "name": "ProcessMessages 186", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949587, "dur": 17, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949607, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949609, "dur": 19, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949635, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949639, "dur": 20, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949665, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949668, "dur": 23, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949693, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949695, "dur": 16, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949715, "dur": 1, "ph": "X", "name": "ProcessMessages 74", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949718, "dur": 17, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949737, "dur": 1, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949740, "dur": 19, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949760, "dur": 1, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949762, "dur": 22, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949787, "dur": 2, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949790, "dur": 24, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949817, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949819, "dur": 21, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949842, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949845, "dur": 20, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949867, "dur": 2, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949871, "dur": 19, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949896, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949899, "dur": 19, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949924, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949927, "dur": 21, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949951, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949953, "dur": 19, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949974, "dur": 2, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186949977, "dur": 27, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950007, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950010, "dur": 25, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950038, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950041, "dur": 29, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950072, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950086, "dur": 23, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950116, "dur": 149, "ph": "X", "name": "ProcessMessages 928", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950268, "dur": 76, "ph": "X", "name": "ReadAsync 928", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950347, "dur": 6, "ph": "X", "name": "ProcessMessages 4662", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950355, "dur": 18, "ph": "X", "name": "ReadAsync 4662", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950374, "dur": 1, "ph": "X", "name": "ProcessMessages 751", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950377, "dur": 17, "ph": "X", "name": "ReadAsync 751", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950400, "dur": 2, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950405, "dur": 23, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950430, "dur": 1, "ph": "X", "name": "ProcessMessages 817", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950433, "dur": 15, "ph": "X", "name": "ReadAsync 817", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950450, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950452, "dur": 22, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950478, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950480, "dur": 28, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950510, "dur": 2, "ph": "X", "name": "ProcessMessages 925", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950514, "dur": 20, "ph": "X", "name": "ReadAsync 925", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950535, "dur": 5, "ph": "X", "name": "ProcessMessages 73", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950543, "dur": 20, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950566, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950569, "dur": 17, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950588, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950591, "dur": 17, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950615, "dur": 2, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950618, "dur": 23, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950644, "dur": 1, "ph": "X", "name": "ProcessMessages 869", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950652, "dur": 30, "ph": "X", "name": "ReadAsync 869", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950685, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950688, "dur": 25, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950715, "dur": 2, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950719, "dur": 26, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950747, "dur": 2, "ph": "X", "name": "ProcessMessages 917", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950751, "dur": 20, "ph": "X", "name": "ReadAsync 917", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950773, "dur": 1, "ph": "X", "name": "ProcessMessages 250", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950776, "dur": 20, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950798, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950800, "dur": 19, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950822, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950825, "dur": 27, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950855, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950858, "dur": 23, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950887, "dur": 1, "ph": "X", "name": "ProcessMessages 906", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950890, "dur": 20, "ph": "X", "name": "ReadAsync 906", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950912, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950915, "dur": 22, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950941, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950944, "dur": 22, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950969, "dur": 1, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950972, "dur": 20, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186950998, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951000, "dur": 20, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951023, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951026, "dur": 22, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951050, "dur": 1, "ph": "X", "name": "ProcessMessages 267", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951053, "dur": 21, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951078, "dur": 2, "ph": "X", "name": "ProcessMessages 739", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951086, "dur": 29, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951118, "dur": 2, "ph": "X", "name": "ProcessMessages 1011", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951122, "dur": 19, "ph": "X", "name": "ReadAsync 1011", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951144, "dur": 1, "ph": "X", "name": "ProcessMessages 662", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951146, "dur": 23, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951173, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951175, "dur": 22, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951201, "dur": 1, "ph": "X", "name": "ProcessMessages 673", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951204, "dur": 20, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951230, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951233, "dur": 18, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951254, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951256, "dur": 29, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951288, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951290, "dur": 24, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951316, "dur": 2, "ph": "X", "name": "ProcessMessages 834", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951320, "dur": 19, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951344, "dur": 1, "ph": "X", "name": "ProcessMessages 579", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951347, "dur": 19, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951369, "dur": 3, "ph": "X", "name": "ProcessMessages 652", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951374, "dur": 22, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951398, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951400, "dur": 22, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951425, "dur": 2, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951429, "dur": 19, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951453, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951456, "dur": 18, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951477, "dur": 1, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951479, "dur": 17, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951504, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951507, "dur": 22, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951531, "dur": 1, "ph": "X", "name": "ProcessMessages 939", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951534, "dur": 19, "ph": "X", "name": "ReadAsync 939", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951555, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951558, "dur": 18, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951581, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951585, "dur": 18, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951605, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951607, "dur": 22, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951631, "dur": 1, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951634, "dur": 21, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951661, "dur": 2, "ph": "X", "name": "ProcessMessages 679", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951665, "dur": 19, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951690, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951692, "dur": 26, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951720, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951723, "dur": 20, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951749, "dur": 2, "ph": "X", "name": "ProcessMessages 681", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951753, "dur": 20, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951779, "dur": 2, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951783, "dur": 16, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951804, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951807, "dur": 19, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951828, "dur": 1, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951831, "dur": 29, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951863, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951866, "dur": 23, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951893, "dur": 4, "ph": "X", "name": "ProcessMessages 703", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951903, "dur": 17, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951922, "dur": 1, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951925, "dur": 29, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951956, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951959, "dur": 28, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951990, "dur": 2, "ph": "X", "name": "ProcessMessages 699", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186951993, "dur": 18, "ph": "X", "name": "ReadAsync 699", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952013, "dur": 2, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952017, "dur": 19, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952037, "dur": 1, "ph": "X", "name": "ProcessMessages 772", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952040, "dur": 20, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952062, "dur": 1, "ph": "X", "name": "ProcessMessages 213", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952064, "dur": 22, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952089, "dur": 5, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952098, "dur": 18, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952122, "dur": 1, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952125, "dur": 22, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952153, "dur": 3, "ph": "X", "name": "ProcessMessages 820", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952158, "dur": 26, "ph": "X", "name": "ReadAsync 820", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952186, "dur": 1, "ph": "X", "name": "ProcessMessages 907", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952189, "dur": 18, "ph": "X", "name": "ReadAsync 907", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952213, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952217, "dur": 16, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952238, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952241, "dur": 22, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952269, "dur": 1, "ph": "X", "name": "ProcessMessages 652", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952272, "dur": 21, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952295, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952298, "dur": 23, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952322, "dur": 6, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952331, "dur": 19, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952356, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952360, "dur": 25, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952388, "dur": 1, "ph": "X", "name": "ProcessMessages 637", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952391, "dur": 26, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952419, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952422, "dur": 26, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952451, "dur": 2, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952454, "dur": 22, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952479, "dur": 2, "ph": "X", "name": "ProcessMessages 685", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952482, "dur": 21, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952509, "dur": 1, "ph": "X", "name": "ProcessMessages 766", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952512, "dur": 19, "ph": "X", "name": "ReadAsync 766", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952533, "dur": 1, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952536, "dur": 21, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952559, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952562, "dur": 22, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952587, "dur": 1, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952589, "dur": 18, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952609, "dur": 1, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952612, "dur": 25, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952639, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952642, "dur": 25, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952670, "dur": 2, "ph": "X", "name": "ProcessMessages 662", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952673, "dur": 23, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952702, "dur": 2, "ph": "X", "name": "ProcessMessages 811", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952707, "dur": 22, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952731, "dur": 1, "ph": "X", "name": "ProcessMessages 913", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952734, "dur": 29, "ph": "X", "name": "ReadAsync 913", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952766, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952769, "dur": 17, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952792, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952795, "dur": 22, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952823, "dur": 2, "ph": "X", "name": "ProcessMessages 735", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952827, "dur": 21, "ph": "X", "name": "ReadAsync 735", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952854, "dur": 1, "ph": "X", "name": "ProcessMessages 770", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952857, "dur": 23, "ph": "X", "name": "ReadAsync 770", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952882, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952885, "dur": 15, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952902, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952904, "dur": 20, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952926, "dur": 1, "ph": "X", "name": "ProcessMessages 701", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952929, "dur": 19, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952949, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952955, "dur": 28, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952989, "dur": 3, "ph": "X", "name": "ProcessMessages 927", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186952995, "dur": 17, "ph": "X", "name": "ReadAsync 927", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953014, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953016, "dur": 29, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953048, "dur": 2, "ph": "X", "name": "ProcessMessages 831", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953052, "dur": 18, "ph": "X", "name": "ReadAsync 831", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953071, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953074, "dur": 25, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953101, "dur": 1, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953103, "dur": 23, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953130, "dur": 2, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953133, "dur": 24, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953160, "dur": 1, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953163, "dur": 21, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953189, "dur": 1, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953192, "dur": 21, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953215, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953218, "dur": 22, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953243, "dur": 1, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953246, "dur": 26, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953278, "dur": 2, "ph": "X", "name": "ProcessMessages 1062", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953281, "dur": 20, "ph": "X", "name": "ReadAsync 1062", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953306, "dur": 1, "ph": "X", "name": "ProcessMessages 742", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953309, "dur": 19, "ph": "X", "name": "ReadAsync 742", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953330, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953333, "dur": 22, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953358, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953360, "dur": 103, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953467, "dur": 2, "ph": "X", "name": "ProcessMessages 973", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953471, "dur": 37, "ph": "X", "name": "ReadAsync 973", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953510, "dur": 3, "ph": "X", "name": "ProcessMessages 2801", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953515, "dur": 16, "ph": "X", "name": "ReadAsync 2801", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953536, "dur": 4, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953541, "dur": 26, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953569, "dur": 1, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953576, "dur": 26, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953605, "dur": 2, "ph": "X", "name": "ProcessMessages 780", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953608, "dur": 18, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953630, "dur": 2, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953634, "dur": 20, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953659, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953662, "dur": 18, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953683, "dur": 1, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953685, "dur": 25, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953713, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953716, "dur": 34, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953763, "dur": 2, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953767, "dur": 23, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953796, "dur": 2, "ph": "X", "name": "ProcessMessages 813", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953799, "dur": 19, "ph": "X", "name": "ReadAsync 813", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953821, "dur": 1, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953824, "dur": 25, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953851, "dur": 2, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953855, "dur": 22, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953880, "dur": 2, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953883, "dur": 19, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953909, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953912, "dur": 16, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953930, "dur": 1, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953933, "dur": 22, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953957, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953960, "dur": 27, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953990, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186953993, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954022, "dur": 2, "ph": "X", "name": "ProcessMessages 751", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954025, "dur": 15, "ph": "X", "name": "ReadAsync 751", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954046, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954048, "dur": 55, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954108, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954157, "dur": 2, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954161, "dur": 25, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954188, "dur": 2, "ph": "X", "name": "ProcessMessages 1318", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954191, "dur": 24, "ph": "X", "name": "ReadAsync 1318", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954223, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954226, "dur": 18, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954247, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954249, "dur": 18, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954270, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954272, "dur": 65, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954342, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954366, "dur": 1, "ph": "X", "name": "ProcessMessages 716", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954369, "dur": 25, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954397, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954400, "dur": 30, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954433, "dur": 2, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954436, "dur": 40, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954488, "dur": 2, "ph": "X", "name": "ProcessMessages 806", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954491, "dur": 30, "ph": "X", "name": "ReadAsync 806", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954524, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954527, "dur": 30, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954561, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954588, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954591, "dur": 20, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954613, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954616, "dur": 13, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954631, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954633, "dur": 41, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954677, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954680, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954720, "dur": 2, "ph": "X", "name": "ProcessMessages 832", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954723, "dur": 17, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954743, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954746, "dur": 41, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954792, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954814, "dur": 2, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954818, "dur": 22, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954843, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954846, "dur": 48, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954898, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954926, "dur": 3, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954931, "dur": 21, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954954, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186954957, "dur": 46, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955008, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955034, "dur": 1, "ph": "X", "name": "ProcessMessages 702", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955037, "dur": 19, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955059, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955062, "dur": 48, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955112, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955114, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955134, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955137, "dur": 18, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955158, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955160, "dur": 16, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955178, "dur": 1, "ph": "X", "name": "ProcessMessages 139", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955180, "dur": 43, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955227, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955254, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955257, "dur": 20, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955279, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955282, "dur": 48, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955334, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955356, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955359, "dur": 23, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955385, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955387, "dur": 15, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955404, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955406, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955450, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955472, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955475, "dur": 19, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955497, "dur": 1, "ph": "X", "name": "ProcessMessages 522", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955500, "dur": 14, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955516, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955518, "dur": 43, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955565, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955586, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955589, "dur": 25, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955617, "dur": 2, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955621, "dur": 43, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955668, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955689, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955691, "dur": 18, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955712, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955714, "dur": 17, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955733, "dur": 1, "ph": "X", "name": "ProcessMessages 136", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955736, "dur": 42, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955783, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955807, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955810, "dur": 28, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955841, "dur": 3, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955846, "dur": 45, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955896, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955898, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955925, "dur": 2, "ph": "X", "name": "ProcessMessages 862", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955929, "dur": 15, "ph": "X", "name": "ReadAsync 862", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955946, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186955949, "dur": 63, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956016, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956018, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956053, "dur": 2, "ph": "X", "name": "ProcessMessages 949", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956058, "dur": 17, "ph": "X", "name": "ReadAsync 949", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956076, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956079, "dur": 38, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956120, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956122, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956142, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956145, "dur": 18, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956166, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956169, "dur": 18, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956190, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956193, "dur": 41, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956238, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956264, "dur": 1, "ph": "X", "name": "ProcessMessages 742", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956267, "dur": 22, "ph": "X", "name": "ReadAsync 742", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956291, "dur": 1, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956294, "dur": 44, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956341, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956343, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956365, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956368, "dur": 17, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956387, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956389, "dur": 18, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956410, "dur": 1, "ph": "X", "name": "ProcessMessages 139", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956412, "dur": 43, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956459, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956461, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956481, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956483, "dur": 19, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956504, "dur": 1, "ph": "X", "name": "ProcessMessages 455", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956507, "dur": 19, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956529, "dur": 1, "ph": "X", "name": "ProcessMessages 145", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956532, "dur": 37, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956573, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956598, "dur": 2, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956602, "dur": 20, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956624, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956626, "dur": 46, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956675, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956678, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956707, "dur": 2, "ph": "X", "name": "ProcessMessages 683", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956710, "dur": 20, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956731, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956734, "dur": 44, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956781, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956784, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956810, "dur": 1, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956813, "dur": 16, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956832, "dur": 1, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956835, "dur": 17, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956854, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956857, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956895, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956918, "dur": 1, "ph": "X", "name": "ProcessMessages 398", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956921, "dur": 28, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956951, "dur": 2, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956954, "dur": 22, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956980, "dur": 2, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186956984, "dur": 21, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957007, "dur": 1, "ph": "X", "name": "ProcessMessages 698", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957010, "dur": 15, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957028, "dur": 1, "ph": "X", "name": "ProcessMessages 59", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957031, "dur": 17, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957051, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957053, "dur": 47, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957104, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957127, "dur": 1, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957130, "dur": 20, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957153, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957155, "dur": 47, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957207, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957230, "dur": 1, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957233, "dur": 23, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957259, "dur": 2, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957263, "dur": 20, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957284, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957287, "dur": 17, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957307, "dur": 1, "ph": "X", "name": "ProcessMessages 247", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957310, "dur": 23, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957337, "dur": 2, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957341, "dur": 17, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957360, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957363, "dur": 43, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957409, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957412, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957438, "dur": 1, "ph": "X", "name": "ProcessMessages 685", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957441, "dur": 20, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957464, "dur": 2, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957468, "dur": 44, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957517, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957542, "dur": 2, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957545, "dur": 17, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957564, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957566, "dur": 53, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957625, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957628, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957653, "dur": 2, "ph": "X", "name": "ProcessMessages 873", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957656, "dur": 15, "ph": "X", "name": "ReadAsync 873", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957674, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957677, "dur": 48, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957730, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957750, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957753, "dur": 20, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957775, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957778, "dur": 18, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957797, "dur": 1, "ph": "X", "name": "ProcessMessages 402", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957801, "dur": 19, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957822, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957825, "dur": 40, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957868, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957891, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957894, "dur": 20, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957917, "dur": 1, "ph": "X", "name": "ProcessMessages 710", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957919, "dur": 45, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957966, "dur": 2, "ph": "X", "name": "ProcessMessages 1103", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957969, "dur": 18, "ph": "X", "name": "ReadAsync 1103", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957990, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186957993, "dur": 17, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958012, "dur": 1, "ph": "X", "name": "ProcessMessages 234", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958015, "dur": 45, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958064, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958086, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958088, "dur": 20, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958110, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958113, "dur": 19, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958135, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958139, "dur": 38, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958180, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958204, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958207, "dur": 26, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958235, "dur": 1, "ph": "X", "name": "ProcessMessages 912", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958238, "dur": 24, "ph": "X", "name": "ReadAsync 912", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958265, "dur": 2, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958268, "dur": 19, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958289, "dur": 1, "ph": "X", "name": "ProcessMessages 471", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958292, "dur": 18, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958312, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958315, "dur": 16, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958333, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958336, "dur": 45, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958385, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958406, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958409, "dur": 20, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958431, "dur": 1, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958434, "dur": 14, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958450, "dur": 1, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958452, "dur": 40, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958496, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958518, "dur": 2, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958522, "dur": 21, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958546, "dur": 1, "ph": "X", "name": "ProcessMessages 690", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958548, "dur": 27, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958577, "dur": 2, "ph": "X", "name": "ProcessMessages 709", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958580, "dur": 25, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958609, "dur": 2, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958612, "dur": 19, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958634, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958637, "dur": 27, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958669, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958672, "dur": 27, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958702, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958705, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958730, "dur": 2, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958733, "dur": 19, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958755, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958757, "dur": 42, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958803, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958830, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958833, "dur": 20, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958856, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958859, "dur": 11, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958874, "dur": 41, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958919, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958942, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958944, "dur": 19, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958965, "dur": 1, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958974, "dur": 16, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958992, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186958995, "dur": 23, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959022, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959048, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959051, "dur": 36, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959090, "dur": 2, "ph": "X", "name": "ProcessMessages 1090", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959093, "dur": 21, "ph": "X", "name": "ReadAsync 1090", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959117, "dur": 2, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959120, "dur": 21, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959143, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959146, "dur": 18, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959166, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959169, "dur": 18, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959189, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959192, "dur": 38, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959235, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959257, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959260, "dur": 24, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959286, "dur": 1, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959290, "dur": 43, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959337, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959360, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959363, "dur": 19, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959385, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959388, "dur": 41, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959434, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959459, "dur": 2, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959463, "dur": 22, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959487, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959490, "dur": 42, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959536, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959562, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959564, "dur": 20, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959587, "dur": 1, "ph": "X", "name": "ProcessMessages 611", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959589, "dur": 45, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959639, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959660, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959663, "dur": 22, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959687, "dur": 1, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959690, "dur": 20, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959712, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959715, "dur": 19, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959737, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959740, "dur": 19, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959761, "dur": 1, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959764, "dur": 15, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959781, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959783, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959804, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959807, "dur": 43, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959854, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959878, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959881, "dur": 21, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959905, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959907, "dur": 13, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959923, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959925, "dur": 37, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959967, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959986, "dur": 2, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186959989, "dur": 16, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960008, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960010, "dur": 18, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960030, "dur": 1, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960032, "dur": 45, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960082, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960108, "dur": 2, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960112, "dur": 17, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960130, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960133, "dur": 18, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960154, "dur": 1, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960156, "dur": 19, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960177, "dur": 1, "ph": "X", "name": "ProcessMessages 193", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960179, "dur": 40, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960224, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960249, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960252, "dur": 17, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960271, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960273, "dur": 35, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960312, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960330, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960332, "dur": 41, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960378, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960400, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960402, "dur": 19, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960424, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960426, "dur": 15, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960444, "dur": 1, "ph": "X", "name": "ProcessMessages 63", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960446, "dur": 42, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960492, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960515, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960518, "dur": 19, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960539, "dur": 1, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960541, "dur": 15, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960558, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960561, "dur": 39, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960604, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960626, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960629, "dur": 19, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960650, "dur": 1, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960652, "dur": 16, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960670, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960672, "dur": 40, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960717, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960739, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960741, "dur": 14, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960760, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960782, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960785, "dur": 20, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960808, "dur": 2, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960811, "dur": 44, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960857, "dur": 2, "ph": "X", "name": "ProcessMessages 950", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960860, "dur": 18, "ph": "X", "name": "ReadAsync 950", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960880, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960883, "dur": 43, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960930, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960954, "dur": 2, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960957, "dur": 20, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960980, "dur": 1, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186960982, "dur": 44, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961031, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961053, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961055, "dur": 17, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961075, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961079, "dur": 16, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961097, "dur": 1, "ph": "X", "name": "ProcessMessages 234", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961100, "dur": 52, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961155, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961177, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961180, "dur": 19, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961201, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961204, "dur": 15, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961221, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961224, "dur": 40, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961267, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961288, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961291, "dur": 20, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961314, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961317, "dur": 45, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961366, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961388, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961390, "dur": 18, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961410, "dur": 2, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961414, "dur": 15, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961432, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961434, "dur": 43, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961482, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961502, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961505, "dur": 14, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961523, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961548, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961551, "dur": 48, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961602, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961624, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961627, "dur": 18, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961647, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961650, "dur": 16, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961668, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961671, "dur": 39, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961714, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961732, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961734, "dur": 15, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961752, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961754, "dur": 17, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961774, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961777, "dur": 40, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961821, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961842, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961844, "dur": 26, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961873, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961876, "dur": 43, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961924, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961945, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961948, "dur": 20, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961970, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961972, "dur": 14, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961988, "dur": 1, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186961991, "dur": 40, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962033, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962037, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962065, "dur": 2, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962069, "dur": 41, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962113, "dur": 2, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962116, "dur": 26, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962146, "dur": 2, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962150, "dur": 18, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962172, "dur": 18, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962192, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962195, "dur": 28, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962226, "dur": 2, "ph": "X", "name": "ProcessMessages 639", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962229, "dur": 39, "ph": "X", "name": "ReadAsync 639", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962273, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962305, "dur": 2, "ph": "X", "name": "ProcessMessages 725", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962309, "dur": 34, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962348, "dur": 2, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962352, "dur": 26, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962379, "dur": 2, "ph": "X", "name": "ProcessMessages 956", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962383, "dur": 18, "ph": "X", "name": "ReadAsync 956", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962403, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962406, "dur": 45, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962454, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962457, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962479, "dur": 1, "ph": "X", "name": "ProcessMessages 679", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962483, "dur": 21, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962507, "dur": 2, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962510, "dur": 51, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962565, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962569, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962606, "dur": 2, "ph": "X", "name": "ProcessMessages 800", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962611, "dur": 29, "ph": "X", "name": "ReadAsync 800", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962642, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962645, "dur": 32, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962681, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962683, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962708, "dur": 1, "ph": "X", "name": "ProcessMessages 867", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962711, "dur": 18, "ph": "X", "name": "ReadAsync 867", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962733, "dur": 2, "ph": "X", "name": "ProcessMessages 120", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962736, "dur": 15, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962754, "dur": 1, "ph": "X", "name": "ProcessMessages 138", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962756, "dur": 40, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962800, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962803, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962840, "dur": 2, "ph": "X", "name": "ProcessMessages 907", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962844, "dur": 26, "ph": "X", "name": "ReadAsync 907", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962873, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962877, "dur": 29, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962911, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962940, "dur": 2, "ph": "X", "name": "ProcessMessages 805", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962944, "dur": 20, "ph": "X", "name": "ReadAsync 805", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962965, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962968, "dur": 22, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962994, "dur": 2, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186962998, "dur": 33, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963035, "dur": 2, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963039, "dur": 24, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963067, "dur": 2, "ph": "X", "name": "ProcessMessages 299", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963072, "dur": 42, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963118, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963142, "dur": 2, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963146, "dur": 23, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963172, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963174, "dur": 44, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963222, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963224, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963245, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963248, "dur": 22, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963272, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963274, "dur": 14, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963291, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963293, "dur": 39, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963336, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963362, "dur": 2, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963365, "dur": 22, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963389, "dur": 1, "ph": "X", "name": "ProcessMessages 659", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963392, "dur": 46, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963443, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963472, "dur": 1, "ph": "X", "name": "ProcessMessages 858", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963475, "dur": 21, "ph": "X", "name": "ReadAsync 858", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963499, "dur": 2, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963503, "dur": 43, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963549, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963551, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963583, "dur": 2, "ph": "X", "name": "ProcessMessages 728", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963587, "dur": 22, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963612, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963615, "dur": 42, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963660, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963663, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963686, "dur": 1, "ph": "X", "name": "ProcessMessages 684", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963690, "dur": 19, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963711, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963714, "dur": 47, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963764, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963767, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963791, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963794, "dur": 22, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963819, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963821, "dur": 15, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963839, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963841, "dur": 38, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963882, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963884, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963906, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963909, "dur": 25, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963936, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963939, "dur": 16, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963958, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186963960, "dur": 38, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964002, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964024, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964026, "dur": 21, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964050, "dur": 1, "ph": "X", "name": "ProcessMessages 641", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964053, "dur": 20, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964075, "dur": 1, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964077, "dur": 18, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964098, "dur": 2, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964102, "dur": 22, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964127, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964130, "dur": 17, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964149, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964152, "dur": 47, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964203, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964224, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964227, "dur": 21, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964251, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964253, "dur": 15, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964271, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964274, "dur": 39, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964317, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964338, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964340, "dur": 21, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964364, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964366, "dur": 19, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964388, "dur": 2, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964391, "dur": 23, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964416, "dur": 1, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964419, "dur": 19, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964440, "dur": 1, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964443, "dur": 17, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964462, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964465, "dur": 50, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964519, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964522, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964544, "dur": 2, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964547, "dur": 16, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964565, "dur": 1, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964568, "dur": 18, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964588, "dur": 1, "ph": "X", "name": "ProcessMessages 438", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964590, "dur": 20, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964612, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964615, "dur": 21, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964639, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964642, "dur": 18, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964662, "dur": 1, "ph": "X", "name": "ProcessMessages 227", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964664, "dur": 18, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964684, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964686, "dur": 39, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964729, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964752, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964755, "dur": 20, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964777, "dur": 1, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964780, "dur": 21, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964802, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964805, "dur": 19, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964826, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964829, "dur": 20, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964852, "dur": 2, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964855, "dur": 19, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964877, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964880, "dur": 44, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964927, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964946, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186964948, "dur": 83, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186965037, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186965041, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186965074, "dur": 293, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186965373, "dur": 119, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186965499, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186965504, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186965550, "dur": 7, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186965560, "dur": 27, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186965591, "dur": 3, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186965596, "dur": 27, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186965628, "dur": 3, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186965635, "dur": 31, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186965669, "dur": 5, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186965676, "dur": 27, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186965708, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186965715, "dur": 30, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186965752, "dur": 5, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186965766, "dur": 31, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186965801, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186965807, "dur": 29, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186965842, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186965847, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186965875, "dur": 4, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186965881, "dur": 26, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186965912, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186965918, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186965956, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186965963, "dur": 34, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966001, "dur": 3, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966006, "dur": 29, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966041, "dur": 5, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966049, "dur": 35, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966088, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966092, "dur": 29, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966125, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966131, "dur": 28, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966163, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966167, "dur": 31, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966204, "dur": 4, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966210, "dur": 33, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966248, "dur": 6, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966257, "dur": 29, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966289, "dur": 5, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966296, "dur": 25, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966324, "dur": 4, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966330, "dur": 38, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966372, "dur": 5, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966379, "dur": 37, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966420, "dur": 6, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966428, "dur": 36, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966469, "dur": 10, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966482, "dur": 28, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966513, "dur": 5, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966520, "dur": 17, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966540, "dur": 3, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966546, "dur": 32, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966583, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966588, "dur": 29, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966619, "dur": 5, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966626, "dur": 26, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966657, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966664, "dur": 34, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966701, "dur": 4, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966708, "dur": 36, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966750, "dur": 5, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966757, "dur": 27, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966788, "dur": 5, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966795, "dur": 28, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966827, "dur": 3, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966832, "dur": 32, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966867, "dur": 6, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966876, "dur": 30, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966910, "dur": 5, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966918, "dur": 23, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966944, "dur": 5, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966952, "dur": 31, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966988, "dur": 4, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186966995, "dur": 29, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186967028, "dur": 5, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186967035, "dur": 29, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186967070, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186967078, "dur": 49, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186967131, "dur": 4, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186967137, "dur": 58, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186967200, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186967204, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186967242, "dur": 6, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186967250, "dur": 35, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186967289, "dur": 5, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186967297, "dur": 30, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186967333, "dur": 4, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186967340, "dur": 31, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186967376, "dur": 6, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186967384, "dur": 23, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186967410, "dur": 3, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186967416, "dur": 22, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186967441, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186967445, "dur": 18, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186967466, "dur": 3, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186967472, "dur": 13, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186967487, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186967492, "dur": 18, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186967512, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186967516, "dur": 26, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186967548, "dur": 5, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186967555, "dur": 32, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186967593, "dur": 4, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186967600, "dur": 25, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186967628, "dur": 4, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186967635, "dur": 20, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186967659, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186967663, "dur": 28, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186967696, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186967702, "dur": 16, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186967721, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186967725, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186967747, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186967752, "dur": 5492, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186973257, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186973264, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186973295, "dur": 5, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186973303, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186973323, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186973327, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186973359, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186973365, "dur": 290, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186973660, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186973663, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186973696, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186973702, "dur": 2089, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186975799, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186975803, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186975840, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186975846, "dur": 44, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186975894, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186975897, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186975922, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186975925, "dur": 271, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186976200, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186976233, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186976238, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186976259, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186976262, "dur": 153, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186976420, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186976424, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186976463, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186976469, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186976493, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186976497, "dur": 23, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186976527, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186976532, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186976561, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186976564, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186976585, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186976587, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186976637, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186976640, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186976665, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186976669, "dur": 118, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186976791, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186976794, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186976814, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186976818, "dur": 277, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977100, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977105, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977131, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977135, "dur": 50, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977190, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977194, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977222, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977226, "dur": 72, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977303, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977307, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977331, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977335, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977358, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977362, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977389, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977394, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977417, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977421, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977443, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977447, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977468, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977471, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977492, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977495, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977519, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977522, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977540, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977543, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977564, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977567, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977614, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977617, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977635, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977638, "dur": 55, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977697, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977700, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977725, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977729, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977753, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977757, "dur": 141, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977901, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977904, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977927, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977931, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977954, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977957, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977989, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186977992, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978012, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978015, "dur": 113, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978131, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978134, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978168, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978172, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978195, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978215, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978218, "dur": 67, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978288, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978291, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978315, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978319, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978355, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978359, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978391, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978395, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978416, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978419, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978451, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978454, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978478, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978482, "dur": 55, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978542, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978546, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978570, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978574, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978616, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978620, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978647, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978651, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978669, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978672, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978697, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978701, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978727, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978731, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978757, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978761, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978787, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978793, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978826, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978830, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978851, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978854, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978910, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978912, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978929, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978932, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978963, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978967, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978989, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186978993, "dur": 12, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186979008, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186979011, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186979049, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186979052, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186979068, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186979072, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186979105, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186979109, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186979135, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186979139, "dur": 160, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186979303, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186979306, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186979336, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186979342, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186979391, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186979394, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186979417, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186979423, "dur": 81, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186979507, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186979510, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186979536, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186979540, "dur": 189, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186979732, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186979735, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186979767, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186979773, "dur": 28, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186979806, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186979812, "dur": 18, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186979832, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186979836, "dur": 42, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186979881, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186979884, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186979906, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186979910, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186979962, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186979966, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186979987, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186979990, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980015, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980020, "dur": 18, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980041, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980045, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980090, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980093, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980117, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980121, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980142, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980145, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980167, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980173, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980191, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980197, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980249, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980252, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980279, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980283, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980301, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980304, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980380, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980383, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980403, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980406, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980434, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980437, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980453, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980457, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980473, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980475, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980501, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980505, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980524, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980527, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980559, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980562, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980582, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980586, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980610, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980612, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980632, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980635, "dur": 98, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980736, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980740, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980757, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980760, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980814, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980818, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980835, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980838, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980854, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980857, "dur": 131, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980993, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186980996, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981014, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981018, "dur": 19, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981041, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981045, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981078, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981082, "dur": 100, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981186, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981189, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981216, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981221, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981248, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981251, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981273, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981277, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981306, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981310, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981336, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981339, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981379, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981404, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981407, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981431, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981434, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981464, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981469, "dur": 59, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981531, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981534, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981559, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981562, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981585, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981589, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981604, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981606, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981636, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981641, "dur": 116, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981761, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981765, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981796, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981801, "dur": 22, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981826, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981829, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981856, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981861, "dur": 73, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981938, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981940, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981966, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186981973, "dur": 230, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186982208, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186982212, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186982238, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186982242, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186982270, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186982274, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186982292, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186982295, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186982315, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186982318, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186982337, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186982341, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186982363, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186982366, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186982389, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186982392, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186982413, "dur": 564, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186982983, "dur": 41, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186983028, "dur": 3, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186983033, "dur": 67, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186983106, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186983138, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186983142, "dur": 69, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186983214, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186983217, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186983242, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186983244, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186983275, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186983277, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186983303, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186983306, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186983334, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186983337, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186983362, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186983365, "dur": 118, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186983487, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186983490, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186983519, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186983524, "dur": 210, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186983740, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186983750, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186983778, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186983782, "dur": 94, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186983880, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186983883, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186983901, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186983904, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186983927, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186983930, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186983965, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186983968, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186983994, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186983998, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186984029, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186984034, "dur": 126, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186984164, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186984167, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186984192, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186984195, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186984217, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186984219, "dur": 82, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186984308, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186984312, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186984348, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186984353, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186984395, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186984398, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186984421, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186984423, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186984472, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186984476, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186984497, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186984502, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186984520, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186984524, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186984541, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186984544, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186984562, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186984565, "dur": 85, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186984657, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186984660, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186984691, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186984695, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186984763, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186984767, "dur": 86, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186984857, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186984860, "dur": 83, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186984946, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186984952, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186985031, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186985033, "dur": 34, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186985072, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186985076, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186985099, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186985103, "dur": 117, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186985223, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186985226, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186985251, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186985253, "dur": 129, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186985385, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186985388, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186985460, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186985463, "dur": 122, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186985589, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186985591, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186985621, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186985624, "dur": 448, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186986079, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186986082, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186986097, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186986099, "dur": 91, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186986196, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186986200, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186986221, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186986224, "dur": 813, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186987042, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186987047, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186987080, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186987083, "dur": 59, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186987148, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186987151, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186987169, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186987173, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186987200, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186987203, "dur": 301, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186987510, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186987513, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186987540, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186987544, "dur": 97, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186987646, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186987649, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186987688, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186987694, "dur": 175, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186987875, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186987879, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186987939, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186987944, "dur": 445, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186988396, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186988400, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186988419, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186988422, "dur": 992, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186989423, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186989430, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186989466, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368186989472, "dur": 23884, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187013369, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187013375, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187013424, "dur": 1538, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187014968, "dur": 6196, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187021176, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187021183, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187021215, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187021219, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187021261, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187021266, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187021294, "dur": 3, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187021299, "dur": 142, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187021448, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187021453, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187021481, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187021485, "dur": 585, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187022074, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187022078, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187022095, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187022098, "dur": 210, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187022313, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187022317, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187022339, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187022342, "dur": 686, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187023033, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187023036, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187023054, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187023058, "dur": 306, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187023370, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187023374, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187023406, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187023411, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187023431, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187023435, "dur": 77, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187023517, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187023521, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187023541, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187023545, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187023591, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187023595, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187023612, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187023615, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187023651, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187023655, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187023675, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187023678, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187023706, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187023710, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187023731, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187023735, "dur": 183, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187023922, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187023926, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187023945, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187023948, "dur": 202, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187024157, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187024161, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187024181, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187024184, "dur": 259, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187024448, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187024452, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187024493, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187024497, "dur": 61, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187024565, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187024569, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187024592, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187024596, "dur": 1249, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187025849, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187025853, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187025872, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187025876, "dur": 188, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187026069, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187026075, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187026096, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187026099, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187026138, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187026143, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187026166, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187026170, "dur": 113, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187026287, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187026294, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187026314, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187026317, "dur": 75, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187026397, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187026401, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187026424, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187026427, "dur": 138, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187026571, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187026575, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187026597, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187026601, "dur": 342, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187026947, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187026951, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187026970, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187026973, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187027032, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187027036, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187027054, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187027057, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187027113, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187027117, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187027137, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187027140, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187027173, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187027176, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187027197, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187027200, "dur": 440, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187027645, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187027649, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187027666, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187027671, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187027696, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187027701, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187027740, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187027743, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187027765, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187027768, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187027796, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187027800, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187027817, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187027820, "dur": 599, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187028424, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187028428, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187028449, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187028452, "dur": 955, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187029412, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187029416, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187029448, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187029453, "dur": 208, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187029667, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187029670, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187029688, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187029692, "dur": 71, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187029767, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187029771, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187029788, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187029791, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187029808, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187029811, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187029864, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187029869, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187029891, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187029894, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187029926, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187029930, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187029948, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187029951, "dur": 93, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187030047, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187030049, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187030065, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187030068, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187030100, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187030103, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187030122, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187030125, "dur": 374, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187030503, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187030506, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187030523, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187030526, "dur": 509, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187031040, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187031043, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187031060, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187031064, "dur": 377, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187031445, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187031449, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187031474, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187031478, "dur": 218, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187031699, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187031702, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187031718, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187031721, "dur": 151, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187031876, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187031880, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187031905, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187031909, "dur": 203, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187032118, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187032121, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187032138, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187032141, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187032186, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187032190, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187032216, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187032220, "dur": 373, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187032596, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187032599, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187032616, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187032619, "dur": 151, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187032775, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187032779, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187032796, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187032803, "dur": 62, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187032870, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187032872, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187032888, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187032891, "dur": 340, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187033235, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187033238, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187033262, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187033266, "dur": 391, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187033662, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187033665, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187033686, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187033690, "dur": 247, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187033941, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187033944, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187033960, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187033962, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187033979, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187033982, "dur": 490, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187034477, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187034481, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187034501, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187034504, "dur": 300, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187034808, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187034811, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187034838, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187034842, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187034868, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187034872, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187034900, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187034905, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187034934, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187034936, "dur": 202, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035144, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035148, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035167, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035171, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035185, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035188, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035209, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035214, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035250, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035254, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035278, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035281, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035309, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035315, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035339, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035346, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035363, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035367, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035386, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035390, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035406, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035410, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035425, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035427, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035451, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035456, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035488, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035494, "dur": 19, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035515, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035519, "dur": 36, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035560, "dur": 4, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035567, "dur": 32, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035604, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035610, "dur": 28, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035644, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035650, "dur": 24, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035677, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035682, "dur": 20, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035707, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035713, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035736, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035741, "dur": 17, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035761, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035765, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035787, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035792, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035827, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035833, "dur": 29, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035866, "dur": 4, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035873, "dur": 27, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035905, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035911, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035939, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035944, "dur": 16, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035963, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035966, "dur": 20, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035991, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187035995, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187036027, "dur": 4, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187036033, "dur": 20, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187036056, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187036060, "dur": 23, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187036088, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187036093, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187036119, "dur": 3, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187036124, "dur": 38, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187036170, "dur": 5, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187036178, "dur": 39, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187036221, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187036225, "dur": 73, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187036306, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187036311, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187036342, "dur": 3, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187036348, "dur": 139, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187036494, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187036498, "dur": 109, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187036625, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187036629, "dur": 433, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187037069, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187037073, "dur": 138, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187037222, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187037229, "dur": 166796, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187204037, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187204043, "dur": 92, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187204156, "dur": 35, "ph": "X", "name": "ProcessMessages 1632", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187204203, "dur": 14147, "ph": "X", "name": "ReadAsync 1632", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187218367, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187218374, "dur": 122, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187218506, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187218521, "dur": 88414, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187306948, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187306966, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187307016, "dur": 5, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187307023, "dur": 52527, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187359563, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187359569, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187359622, "dur": 27, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187359652, "dur": 15519, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187375184, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187375190, "dur": 116, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187375312, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187375316, "dur": 20614, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187395943, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187395950, "dur": 114, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187396082, "dur": 45, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187396132, "dur": 14720, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187410865, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187410871, "dur": 101, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187410979, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187410985, "dur": 28636, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187439634, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187439640, "dur": 134, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187439780, "dur": 28, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187439811, "dur": 65727, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187505550, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187505557, "dur": 157, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187505733, "dur": 7, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187505745, "dur": 433, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187506185, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187506188, "dur": 100, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187506294, "dur": 40, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187506337, "dur": 186448, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187692794, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187692800, "dur": 101, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187692908, "dur": 460, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 14916, "tid": 12884901888, "ts": 1751368187693375, "dur": 27989, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 14916, "tid": 2864, "ts": 1751368187732328, "dur": 5688, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 14916, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 14916, "tid": 8589934592, "ts": 1751368186930452, "dur": 757529, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 14916, "tid": 8589934592, "ts": 1751368187687984, "dur": 7, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 14916, "tid": 8589934592, "ts": 1751368187687992, "dur": 1144, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 14916, "tid": 2864, "ts": 1751368187738019, "dur": 33, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 14916, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 14916, "tid": 4294967296, "ts": 1751368186913942, "dur": 808582, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 14916, "tid": 4294967296, "ts": 1751368186917222, "dur": 5878, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 14916, "tid": 4294967296, "ts": 1751368187722746, "dur": 4529, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 14916, "tid": 4294967296, "ts": 1751368187724903, "dur": 130, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 14916, "tid": 4294967296, "ts": 1751368187727357, "dur": 16, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 14916, "tid": 2864, "ts": 1751368187738055, "dur": 34, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751368186937867, "dur": 1581, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751368186939457, "dur": 1860, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751368186941431, "dur": 142, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751368186941574, "dur": 271, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751368186942624, "dur": 658, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_4B40DB5AF1CE9D22.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751368186943949, "dur": 1973, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751368186946043, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751368186946613, "dur": 129, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751368186946957, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751368186947228, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PlasticSCM.Editor.ref.dll_EBB1919501649DC3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751368186947385, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751368186947771, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751368186947972, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751368186948181, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751368186948369, "dur": 95, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Runtime.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751368186948827, "dur": 93, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751368186941859, "dur": 24586, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751368186966462, "dur": 726925, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751368187693388, "dur": 507, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751368187693905, "dur": 109, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751368187694212, "dur": 23181, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751368186942054, "dur": 24413, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368186966544, "dur": 426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_E4B698B849B24AEB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751368186967169, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_5C2FAC469D562E01.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751368186968076, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751368186968242, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751368186968325, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751368186968502, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751368186968599, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368186968737, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751368186969172, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368186969396, "dur": 786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368186970182, "dur": 155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368186970337, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368186970534, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368186970772, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368186970987, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368186971209, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368186971658, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368186971873, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368186972089, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368186972305, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368186972543, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368186972763, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368186972998, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368186973209, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368186973442, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368186973659, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368186973922, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368186974143, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368186974351, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368186974561, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368186974873, "dur": 1680, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Core\\EditorBinding\\Inspector\\InspectorActionDirectionAttribute.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751368186974831, "dur": 1835, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368186976666, "dur": 634, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368186977301, "dur": 451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368186977755, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751368186977940, "dur": 865, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751368186978805, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368186978948, "dur": 845, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368186979799, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751368186980184, "dur": 1014, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751368186981199, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368186981322, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751368186981479, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751368186981541, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/ScriptablePacker.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751368186981889, "dur": 542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/ScriptablePacker.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751368186982431, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368186982552, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368186982795, "dur": 318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368186983113, "dur": 2166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368186985280, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751368186985388, "dur": 382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751368186985822, "dur": 3372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368186989195, "dur": 31268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368187020466, "dur": 2454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751368187022950, "dur": 2825, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751368187025775, "dur": 284, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368187026066, "dur": 2400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751368187028467, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368187028679, "dur": 513, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751368187029194, "dur": 2144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/NativeFilePicker.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751368187031372, "dur": 2005, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751368187033378, "dur": 718, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368187034103, "dur": 2795, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751368187036899, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368187037102, "dur": 302, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751368187037459, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368187037645, "dur": 873, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751368187038519, "dur": 654831, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751368186942100, "dur": 24387, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751368186966490, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_7B22B0BDD3082718.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751368186966991, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_966AC74B818A5F90.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751368186967240, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_41563AEB7BF951B8.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751368186967424, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_0E5ABA1EF864C1B6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751368186967947, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Runtime.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751368186968240, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751368186968453, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751368186968512, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751368186968611, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751368186968815, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10316626440694862930.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751368186969162, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751368186969374, "dur": 838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751368186970212, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751368186970404, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751368186970641, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751368186970860, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751368186971077, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751368186971298, "dur": 469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751368186971767, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751368186972003, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751368186972209, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751368186972444, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751368186972662, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751368186972882, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751368186973106, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751368186973789, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751368186974009, "dur": 395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751368186974405, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751368186974616, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751368186975165, "dur": 576, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Runtime\\VisualScripting.Core\\Cloning\\ISpecifiesCloner.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751368186974910, "dur": 831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751368186975741, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751368186975946, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751368186976169, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751368186976380, "dur": 553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751368186976934, "dur": 362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751368186977296, "dur": 504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751368186977801, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751368186978013, "dur": 1956, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751368186979970, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751368186980081, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751368186980466, "dur": 961, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751368186981504, "dur": 431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751368186981983, "dur": 719, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751368186982744, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751368186982911, "dur": 1268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751368186984241, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751368186984403, "dur": 735, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751368186985139, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751368186985276, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751368186985433, "dur": 587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751368186986068, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751368186986211, "dur": 449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751368186986708, "dur": 2492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751368186989201, "dur": 31278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751368187020481, "dur": 2132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751368187022647, "dur": 2484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-csharp.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751368187025161, "dur": 2433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751368187027653, "dur": 2986, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751368187030644, "dur": 784, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751368187031437, "dur": 2639, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751368187034078, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751368187034391, "dur": 3553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751368187037998, "dur": 655369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186942092, "dur": 24389, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186966484, "dur": 467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_7A744375D0C40DCD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751368186967225, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_CE214F9E6C036FF9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751368186968051, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751368186968364, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/PsdPlugin.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751368186968607, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186968712, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751368186968841, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18127688178268093734.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751368186969234, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186969428, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186970194, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186970401, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186970622, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186970843, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186971081, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186971309, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186971728, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186971933, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186972148, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186972406, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186972617, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186972844, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186973056, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186973264, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186973473, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186973678, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186973896, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186974113, "dur": 618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186974732, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186974938, "dur": 402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186975340, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186975570, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186975804, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186976019, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186976674, "dur": 618, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186977312, "dur": 443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186977758, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751368186978075, "dur": 657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751368186978733, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186978891, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751368186979205, "dur": 904, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751368186980110, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186980169, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751368186980379, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186980614, "dur": 850, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751368186981521, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186981687, "dur": 708, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751368186982395, "dur": 288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186982688, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186982817, "dur": 299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186983116, "dur": 2393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186985509, "dur": 909, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186986418, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751368186986575, "dur": 476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751368186987091, "dur": 2110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368186989202, "dur": 33530, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368187022733, "dur": 2729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751368187025462, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368187025678, "dur": 3435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751368187029153, "dur": 3649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751368187032803, "dur": 401, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368187033211, "dur": 2604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751368187035816, "dur": 524, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368187036410, "dur": 313, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751368187036818, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751368187036926, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368187037146, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751368187037491, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368187037647, "dur": 172168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368187209837, "dur": 96263, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751368187209818, "dur": 97415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751368187308284, "dur": 177, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751368187308726, "dur": 88633, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751368187412098, "dur": 94803, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751368187412092, "dur": 94811, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751368187506920, "dur": 719, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751368187507650, "dur": 185746, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186942149, "dur": 24359, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186966510, "dur": 548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_AAD14E71D524EA60.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751368186967123, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_68F0E05FD8235E34.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751368186967261, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186967911, "dur": 314, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751368186968640, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186969186, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186969498, "dur": 784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186970282, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186970489, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186970703, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186970979, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186971221, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186971663, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186971868, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186972072, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186972279, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186972504, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186972727, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186972977, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186973202, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186973399, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186973614, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186973835, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186974068, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186974305, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186974520, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186974732, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186974984, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186975206, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186975399, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186975612, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186975832, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186976043, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186976275, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186976540, "dur": 753, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186977293, "dur": 465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186977759, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751368186977988, "dur": 553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751368186978542, "dur": 432, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186978993, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186979248, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751368186979434, "dur": 900, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751368186980335, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186980580, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751368186980788, "dur": 447, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186981246, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186981391, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751368186981651, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751368186981839, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186982093, "dur": 536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751368186982630, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186982946, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186983106, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751368186983272, "dur": 460, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751368186983733, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186983816, "dur": 1710, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186985527, "dur": 3678, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368186989205, "dur": 31262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368187020472, "dur": 2249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751368187022722, "dur": 2286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368187025015, "dur": 2278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751368187027294, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368187027356, "dur": 4590, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751368187031948, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368187032012, "dur": 2268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751368187034308, "dur": 2491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/OutlineFx.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751368187037040, "dur": 528, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368187037649, "dur": 338828, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751368187376506, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751368187376478, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751368187376578, "dur": 316822, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368186942074, "dur": 24402, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368186966579, "dur": 366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_FB049F22F4C5497A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751368186966962, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_D8154579280ABE30.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751368186967255, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_A5513E9150BC6315.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751368186967419, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_26C00F9C5DF6EF10.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751368186967840, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_B867065A6613A379.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751368186968578, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368186968804, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14324468880911302389.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751368186969170, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368186969387, "dur": 743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368186970130, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368186970789, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368186971022, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368186971611, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368186971831, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368186972071, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368186972288, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368186972523, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368186972751, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368186973419, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368186973625, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368186973856, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368186974062, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368186974303, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368186974524, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368186974749, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368186974961, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368186975166, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368186975379, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368186975587, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368186975795, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368186976003, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368186976204, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368186976428, "dur": 64, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368186976544, "dur": 750, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368186977294, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368186977756, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751368186977979, "dur": 514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751368186978493, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368186978631, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751368186978835, "dur": 364, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368186979204, "dur": 585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751368186979789, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368186979863, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751368186980267, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751368186980490, "dur": 392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751368186980899, "dur": 681, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751368186981581, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368186981656, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751368186981885, "dur": 558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751368186982443, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368186982521, "dur": 291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368186982813, "dur": 307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368186983121, "dur": 2401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368186985522, "dur": 3666, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368186989189, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751368186989385, "dur": 31095, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368187020481, "dur": 2602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751368187023084, "dur": 1940, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368187025029, "dur": 2639, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751368187027669, "dur": 1572, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368187029253, "dur": 2299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751368187031553, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368187031612, "dur": 2421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751368187034034, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368187034113, "dur": 2876, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751368187037272, "dur": 378, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751368187037650, "dur": 374473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751368187412142, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751368187412124, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751368187412272, "dur": 28633, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751368187440910, "dur": 252488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186942126, "dur": 24372, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186966500, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_3122CFBB2DC47329.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751368186967263, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_8AA30A6C8DDCA8A3.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751368186967387, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_4DE7717F601CC2DC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751368186967740, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751368186967883, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751368186968006, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751368186968161, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751368186968330, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/PsdPlugin.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751368186968408, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751368186968545, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186968807, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11908194702949990099.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751368186968968, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8775554482886297096.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751368186969188, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186969395, "dur": 975, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186970370, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186970579, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186970842, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186971073, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186971295, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186971730, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186971955, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186972166, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186972369, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186972584, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186972797, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186973005, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186973228, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186973434, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186973646, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186973873, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186974089, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186974308, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186974521, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186974720, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186974915, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186975128, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186975433, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186975635, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186975845, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186976047, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186976300, "dur": 488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186976788, "dur": 510, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186977299, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186977762, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751368186977952, "dur": 915, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751368186978868, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186978946, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751368186979143, "dur": 848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751368186979991, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186980150, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186980223, "dur": 806, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751368186981030, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186981310, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751368186981469, "dur": 775, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751368186982245, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186982363, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751368186982569, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751368186982990, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186983122, "dur": 2392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186985515, "dur": 3671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368186989192, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751368186989369, "dur": 32783, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368187022153, "dur": 2349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751368187024538, "dur": 4049, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751368187028645, "dur": 2420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751368187031066, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368187031277, "dur": 4017, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751368187035295, "dur": 683, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751368187035992, "dur": 2429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751368187038515, "dur": 654884, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368186942121, "dur": 24372, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368186966495, "dur": 558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_89517EAC2A874601.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751368186967245, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_DA3F53EC74158B55.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751368186968075, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751368186968363, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751368186968487, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751368186968632, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368186968836, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10054408285822421624.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751368186969157, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368186969363, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368186970073, "dur": 789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368186970862, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368186971094, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368186971309, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368186971753, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368186971967, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368186972174, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368186972393, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368186972614, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368186972855, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368186973061, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368186973280, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368186973495, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368186973704, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368186973936, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368186974196, "dur": 570, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@24b10291b18f\\Runtime\\TMP\\FontFeatureCommon.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751368186974778, "dur": 553, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@24b10291b18f\\Runtime\\TMP\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751368186974154, "dur": 1303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368186975457, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368186975685, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368186975892, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368186976088, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368186976262, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368186976584, "dur": 707, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368186977314, "dur": 448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368186977764, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751368186977997, "dur": 563, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751368186978624, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751368186978810, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368186978900, "dur": 1710, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751368186980611, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368186980808, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity-editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751368186981029, "dur": 734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751368186981801, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368186981989, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368186982068, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751368186982349, "dur": 468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368186982818, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368186983109, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751368186983275, "dur": 386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751368186983736, "dur": 1773, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368186985510, "dur": 3040, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368186988551, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751368186988650, "dur": 331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751368186989009, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368186989199, "dur": 31269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368187020469, "dur": 2251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/OutlineFx.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751368187022776, "dur": 2030, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751368187024807, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368187024869, "dur": 2895, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751368187027792, "dur": 3077, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751368187030914, "dur": 3201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751368187034116, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368187034279, "dur": 2503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751368187036783, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368187037103, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751368187037166, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368187037338, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368187037622, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368187037679, "dur": 652917, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751368187692106, "dur": 183, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 7, "ts": 1751368187692289, "dur": 990, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 7, "ts": 1751368187690597, "dur": 2733, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368186942144, "dur": 24359, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368186966505, "dur": 474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_98CF6329D3D6F9AB.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751368186967017, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_1237F680F70DA85A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751368186967142, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_41BBAEC6AFBBC340.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751368186967214, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_3EA836CAC6D1710A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751368186967861, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751368186967973, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751368186968369, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751368186968451, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751368186968589, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368186968816, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14507123614329245381.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751368186969131, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368186969347, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368186969519, "dur": 1220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368186970739, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368186970957, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368186971187, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368186971625, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368186971947, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368186972161, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368186972444, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368186972666, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368186972887, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368186973098, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368186973325, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368186973531, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368186973761, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368186973981, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368186974200, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368186974399, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368186974597, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368186974880, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368186975171, "dur": 569, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.postprocessing@026b2a0827a4\\PostProcessing\\Runtime\\PostProcessManager.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751368186975097, "dur": 773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368186975870, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368186976070, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368186976276, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368186976511, "dur": 93, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368186976604, "dur": 691, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368186977295, "dur": 462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368186977758, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751368186977945, "dur": 645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751368186978591, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368186978724, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751368186979062, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751368186979517, "dur": 547, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368186980068, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751368186980278, "dur": 1361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751368186981640, "dur": 601, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368186982252, "dur": 485, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368186982743, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751368186982884, "dur": 1113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751368186984057, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751368186984146, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751368186984491, "dur": 1021, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368186985512, "dur": 3678, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368186989190, "dur": 31275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368187020466, "dur": 2020, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751368187022488, "dur": 266, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368187022762, "dur": 2126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751368187024915, "dur": 3989, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751368187028905, "dur": 335, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368187029249, "dur": 2163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751368187031447, "dur": 2769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751368187034217, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368187034286, "dur": 2600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751368187036918, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368187037237, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368187037633, "dur": 217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751368187037850, "dur": 655520, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186942166, "dur": 24348, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186966516, "dur": 449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_D07FFB3B2770754A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751368186967067, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_D0FAA40E40B36FD7.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751368186967271, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_B70C9B9527050002.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751368186967372, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_06CB192F9C3927A4.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751368186967710, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751368186968193, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Common.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1751368186968365, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1751368186968504, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751368186968601, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186968787, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751368186968982, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4308445135959118664.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751368186969219, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186969474, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186970325, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186970527, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186970758, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186971017, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186971242, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186971692, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186971909, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186972105, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186972301, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186972525, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186972735, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186972991, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186973217, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186973438, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186973650, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186973874, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186974083, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186974297, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186974517, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186974732, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186974943, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186975155, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186975356, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186975569, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186975805, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186976026, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186976235, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186976535, "dur": 762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186977297, "dur": 464, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186977762, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751368186977983, "dur": 603, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751368186978586, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186978868, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751368186979126, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751368186979367, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186979671, "dur": 857, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751368186980528, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186980813, "dur": 586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751368186981400, "dur": 849, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186982253, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751368186982723, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186983036, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186983104, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751368186983261, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751368186983561, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186983854, "dur": 1649, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186985506, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751368186985700, "dur": 668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751368186986415, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751368186986572, "dur": 447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751368186987070, "dur": 2126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368186989196, "dur": 31325, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368187020523, "dur": 2177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/NativeFilePicker.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751368187022702, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368187022783, "dur": 2837, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751368187025659, "dur": 2212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-unity.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751368187027904, "dur": 3369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751368187031309, "dur": 2241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/ScriptablePacker.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751368187033551, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368187033621, "dur": 2319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-unity-editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751368187035941, "dur": 371, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368187036413, "dur": 234, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751368187036776, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368187037063, "dur": 281, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751368187037451, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368187037524, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1751368187037636, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751368187037805, "dur": 655541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751368186942181, "dur": 24338, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751368186966521, "dur": 522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_21E5C8D6C2CA360E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751368186967100, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_5A6241C5E4FBF31E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751368186967273, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_34D7E2BEC34818D5.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751368186968127, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751368186968197, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1751368186968370, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1751368186968508, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751368186968591, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751368186968944, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1416584266116165248.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751368186969168, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751368186969414, "dur": 747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751368186970162, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751368186970653, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751368186970878, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751368186971094, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751368186971668, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751368186971907, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751368186972116, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751368186972343, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751368186972556, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751368186972773, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751368186972993, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751368186973273, "dur": 892, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualeffectgraph@ba0fe1b085e9\\Runtime\\Utilities\\PropertyBinding\\Implementation\\VFXMultiplePositionBinder.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751368186973209, "dur": 1145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751368186974355, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751368186974563, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751368186974898, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751368186975108, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751368186975311, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751368186975509, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751368186975723, "dur": 486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751368186976209, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751368186976675, "dur": 628, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751368186977303, "dur": 446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751368186977751, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751368186977934, "dur": 1765, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751368186979700, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751368186979823, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751368186979911, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751368186980256, "dur": 1123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751368186981379, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751368186981544, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751368186981756, "dur": 1276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751368186983103, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751368186983270, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751368186983757, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751368186983864, "dur": 710, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751368186984624, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751368186984718, "dur": 1219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751368186985992, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751368186986157, "dur": 1347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751368186987557, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751368186987701, "dur": 779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751368186988548, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751368186988700, "dur": 412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751368186989186, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751368186989389, "dur": 463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751368186989892, "dur": 491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751368186990815, "dur": 81, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751368186991322, "dur": 214131, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751368187210093, "dur": 9556, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751368187209781, "dur": 9966, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751368187220357, "dur": 85700, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751368187219793, "dur": 87048, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751368187307921, "dur": 174, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751368187308540, "dur": 52465, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751368187376452, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1751368187376444, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1751368187376546, "dur": 316860, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751368186942196, "dur": 24329, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751368186966527, "dur": 485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_D5DC31B4B0DA901E.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751368186967031, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_3713E40F818C5856.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751368186967097, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_3713E40F818C5856.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751368186967269, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_24D87271FC85A7AF.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751368186967764, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751368186967952, "dur": 6721, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751368186974785, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751368186975069, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751368186975291, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751368186975497, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751368186975700, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751368186975913, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751368186976157, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751368186976370, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751368186976929, "dur": 368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751368186977298, "dur": 679, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751368186977977, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751368186978139, "dur": 1212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751368186979351, "dur": 349, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751368186979706, "dur": 1043, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751368186980750, "dur": 1146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751368186981900, "dur": 948, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity-editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751368186982849, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751368186982910, "dur": 202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751368186983112, "dur": 1737, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751368186984851, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751368186985012, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751368186985431, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751368186985508, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751368186985664, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751368186986013, "dur": 3189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751368186989203, "dur": 31270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751368187020477, "dur": 2477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751368187022955, "dur": 2253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751368187025216, "dur": 2287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751368187027504, "dur": 565, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751368187028077, "dur": 5293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751368187033371, "dur": 316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751368187033695, "dur": 3319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751368187037015, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751368187037213, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751368187037376, "dur": 175, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PPv2URPConverters.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751368187037629, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751368187037713, "dur": 655652, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751368186942217, "dur": 24314, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751368186966533, "dur": 485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Apple.Extensions.Common.dll_D4E020400338F5BB.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751368186967278, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_4C3484A57A05DA64.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751368186967984, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751368186968161, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1751368186968365, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1751368186968633, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751368186968842, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2524390107958066875.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751368186969169, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751368186969377, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751368186970144, "dur": 1611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751368186971755, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751368186971992, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751368186972211, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751368186972423, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751368186972653, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751368186972885, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751368186973088, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751368186973784, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751368186974003, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751368186974217, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751368186974422, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751368186974631, "dur": 293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751368186974924, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751368186975132, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751368186975348, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751368186975571, "dur": 423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751368186975994, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751368186976246, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751368186976537, "dur": 762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751368186977299, "dur": 670, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751368186977970, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751368186978148, "dur": 623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751368186978833, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751368186979030, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751368186979241, "dur": 439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751368186979716, "dur": 688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751368186980405, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751368186980517, "dur": 678, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751368186981314, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751368186981498, "dur": 775, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751368186982273, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751368186982547, "dur": 261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751368186982808, "dur": 316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751368186983124, "dur": 2393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751368186985517, "dur": 3686, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751368186989204, "dur": 31255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751368187020467, "dur": 2279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751368187022787, "dur": 2190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751368187024978, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751368187025101, "dur": 3320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp-firstpass.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751368187028453, "dur": 2434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751368187030925, "dur": 2417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751368187033388, "dur": 2368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751368187035757, "dur": 878, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751368187036635, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751368187036815, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751368187037143, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.DocCodeSamples.pdb"}}, {"pid": 12345, "tid": 12, "ts": 1751368187037348, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751368187037553, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751368187037771, "dur": 655621, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751368186942237, "dur": 24330, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751368186966568, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_57E453305C48C0D1.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751368186967253, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_E951570DA9D21231.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751368186967414, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_E496E41FECA98925.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751368186967827, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751368186968197, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751368186968270, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751368186968362, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1751368186968633, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751368186968812, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6091109579032960000.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751368186968920, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3047476953324505363.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751368186969160, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751368186969404, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751368186970267, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751368186970496, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751368186970713, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751368186970968, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751368186971194, "dur": 441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751368186971635, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751368186971956, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751368186972181, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751368186972412, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751368186972632, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751368186972872, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751368186973074, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751368186973308, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751368186973511, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751368186973739, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751368186974136, "dur": 737, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1b53f46e931b\\Editor\\VisualScripting.Core\\Inspection\\Unity\\Vector4Inspector.cs"}}, {"pid": 12345, "tid": 13, "ts": 1751368186973953, "dur": 942, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751368186974895, "dur": 347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751368186975242, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751368186975447, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751368186975651, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751368186975863, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751368186976425, "dur": 377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751368186976803, "dur": 499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751368186977303, "dur": 657, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751368186977961, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751368186978263, "dur": 333, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751368186978601, "dur": 750, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751368186979352, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751368186979454, "dur": 576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751368186980050, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751368186980329, "dur": 593, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751368186980922, "dur": 356, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751368186981312, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751368186981500, "dur": 587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751368186982087, "dur": 435, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751368186982573, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751368186982745, "dur": 491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751368186983236, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751368186983342, "dur": 2165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751368186985508, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751368186985899, "dur": 3293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751368186989192, "dur": 31269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751368187020462, "dur": 2240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751368187022759, "dur": 2634, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751368187025427, "dur": 2731, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualEffectGraph.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751368187028159, "dur": 374, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751368187028540, "dur": 2580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751368187031175, "dur": 3622, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751368187034798, "dur": 360, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751368187035166, "dur": 2315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751368187037643, "dur": 358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751368187038002, "dur": 655393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368186942255, "dur": 24291, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368186966548, "dur": 440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_BA35B04A057142E1.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751368186967023, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_47EFA976E901FEC1.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751368186967137, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_60DEBCA51ABA1468.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751368186967257, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_EFD593047BD2E6A8.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751368186967911, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEngineBridge.001.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751368186968304, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751368186968409, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1751368186968501, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751368186968606, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368186968844, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6307440961136846775.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751368186968959, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14508719319121739504.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751368186969083, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3248707927565818496.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751368186969174, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368186969364, "dur": 989, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368186970353, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368186970552, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368186970777, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368186971014, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368186971248, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368186971684, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368186971893, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368186972099, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368186972307, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368186972519, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368186972743, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368186972976, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368186973187, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368186973420, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368186973622, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368186973834, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368186974154, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368186974351, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368186974559, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368186974790, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368186975000, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368186975231, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368186975485, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368186975692, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368186975892, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368186976155, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368186976365, "dur": 520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368186976886, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368186977302, "dur": 449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368186977753, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751368186977969, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751368186978038, "dur": 1384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751368186979423, "dur": 533, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368186979985, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751368186980168, "dur": 323, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368186980494, "dur": 767, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751368186981262, "dur": 334, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368186981602, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751368186982009, "dur": 749, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751368186982793, "dur": 675, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 14, "ts": 1751368186983525, "dur": 194, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368186983910, "dur": 30924, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 14, "ts": 1751368187020458, "dur": 3331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751368187023821, "dur": 2427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751368187026248, "dur": 1318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368187027576, "dur": 2206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751368187029783, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368187029923, "dur": 2270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751368187032194, "dur": 348, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368187032549, "dur": 2328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751368187034877, "dur": 565, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368187035448, "dur": 2309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751368187037790, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751368187037859, "dur": 655548, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186942269, "dur": 24281, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186966554, "dur": 459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_8AA31634E5830E2F.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751368186967037, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_07ED47E0EFE03F3D.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751368186967192, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_A01C5835F2B3673A.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751368186967992, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751368186968298, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751368186968488, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751368186968627, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186969157, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186969363, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186969950, "dur": 625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186970575, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186970794, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186971019, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186971237, "dur": 437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186971674, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186971894, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186972109, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186972309, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186972533, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186972755, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186972974, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186973186, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186973421, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186973654, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186973864, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186974066, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186974280, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186974496, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186974787, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186975038, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186975313, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186975505, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186975702, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186975895, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186976130, "dur": 95, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186976225, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186976501, "dur": 61, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186976562, "dur": 733, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186977296, "dur": 468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186977766, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751368186977935, "dur": 357, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186978297, "dur": 556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751368186978902, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751368186979174, "dur": 1242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186980420, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751368186980686, "dur": 630, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186981319, "dur": 724, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751368186982124, "dur": 622, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186982830, "dur": 276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186983107, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751368186983255, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186983446, "dur": 437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751368186983936, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751368186984079, "dur": 712, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751368186984846, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751368186984989, "dur": 450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751368186985502, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751368186985667, "dur": 525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751368186986241, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751368186986398, "dur": 434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751368186986868, "dur": 2325, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368186989193, "dur": 31276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368187020472, "dur": 2286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751368187022788, "dur": 2390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751368187025273, "dur": 3367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751368187028697, "dur": 2238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751368187030939, "dur": 613, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751368187031559, "dur": 3143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751368187034763, "dur": 2908, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751368187037704, "dur": 655662, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751368186942287, "dur": 24274, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751368186966563, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_7D19F4582D6FA60D.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751368186967025, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_50CFC6D0BF59D948.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751368186967233, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_15728F368AD157F8.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751368186967675, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751368186967850, "dur": 6921, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751368186974852, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751368186975161, "dur": 2090, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751368186977311, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751368186977399, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751368186977748, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751368186977921, "dur": 792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751368186978713, "dur": 504, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751368186979246, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751368186979408, "dur": 443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751368186979851, "dur": 717, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751368186980575, "dur": 864, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751368186981440, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751368186981683, "dur": 588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751368186982271, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751368186982325, "dur": 474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751368186982799, "dur": 310, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751368186983109, "dur": 1518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751368186984628, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751368186984781, "dur": 412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751368186985267, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751368186985511, "dur": 3680, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751368186989191, "dur": 31287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751368187020479, "dur": 3026, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751368187023506, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751368187023575, "dur": 2328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751368187025996, "dur": 3280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751368187029307, "dur": 2285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751368187031593, "dur": 1354, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751368187032954, "dur": 2441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualEffectGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751368187035395, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751368187035486, "dur": 2275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751368187037806, "dur": 655538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751368187720803, "dur": 1179, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 14916, "tid": 2864, "ts": 1751368187738675, "dur": 2311, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 14916, "tid": 2864, "ts": 1751368187741125, "dur": 1685, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 14916, "tid": 2864, "ts": 1751368187730719, "dur": 12728, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}