{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 2364, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 2364, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 2364, "tid": 2188, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 2364, "tid": 2188, "ts": 1751389269538224, "dur": 558, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 2364, "tid": 2188, "ts": 1751389269541188, "dur": 990, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 2364, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 2364, "tid": 1, "ts": 1751389268952089, "dur": 5176, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 2364, "tid": 1, "ts": 1751389268957269, "dur": 114294, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 2364, "tid": 1, "ts": 1751389269071579, "dur": 105662, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 2364, "tid": 2188, "ts": 1751389269542184, "dur": 20, "ph": "X", "name": "", "args": {}}, {"pid": 2364, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268946784, "dur": 186, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268946973, "dur": 582363, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268947602, "dur": 2565, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268950178, "dur": 1786, "ph": "X", "name": "ProcessMessages 20487", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268951969, "dur": 357, "ph": "X", "name": "ReadAsync 20487", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268952333, "dur": 25, "ph": "X", "name": "ProcessMessages 20547", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268952360, "dur": 61, "ph": "X", "name": "ReadAsync 20547", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268952427, "dur": 6, "ph": "X", "name": "ProcessMessages 3556", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268952435, "dur": 450, "ph": "X", "name": "ReadAsync 3556", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268952892, "dur": 2, "ph": "X", "name": "ProcessMessages 11", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268952896, "dur": 98, "ph": "X", "name": "ReadAsync 11", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268952996, "dur": 11, "ph": "X", "name": "ProcessMessages 8213", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268953009, "dur": 22, "ph": "X", "name": "ReadAsync 8213", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268953037, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268953060, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268953062, "dur": 117, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268953187, "dur": 3, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268953192, "dur": 44, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268953238, "dur": 4, "ph": "X", "name": "ProcessMessages 2909", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268953243, "dur": 28, "ph": "X", "name": "ReadAsync 2909", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268953273, "dur": 1, "ph": "X", "name": "ProcessMessages 637", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268953275, "dur": 21, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268953302, "dur": 6, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268953311, "dur": 23, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268953341, "dur": 2, "ph": "X", "name": "ProcessMessages 750", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268953345, "dur": 100, "ph": "X", "name": "ReadAsync 750", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268953446, "dur": 1, "ph": "X", "name": "ProcessMessages 75", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268953449, "dur": 41, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268953496, "dur": 4, "ph": "X", "name": "ProcessMessages 1624", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268953503, "dur": 134, "ph": "X", "name": "ReadAsync 1624", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268953648, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268953652, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268953725, "dur": 5, "ph": "X", "name": "ProcessMessages 2191", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268953732, "dur": 33, "ph": "X", "name": "ReadAsync 2191", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268953769, "dur": 36, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268953813, "dur": 7, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268953821, "dur": 286, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268954114, "dur": 4, "ph": "X", "name": "ProcessMessages 1103", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268954120, "dur": 88, "ph": "X", "name": "ReadAsync 1103", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268954211, "dur": 6, "ph": "X", "name": "ProcessMessages 4652", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268954222, "dur": 26, "ph": "X", "name": "ReadAsync 4652", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268954251, "dur": 1, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268954253, "dur": 18, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268954278, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268954281, "dur": 21, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268954309, "dur": 2, "ph": "X", "name": "ProcessMessages 699", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268954313, "dur": 107, "ph": "X", "name": "ReadAsync 699", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268954424, "dur": 1, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268954427, "dur": 38, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268954471, "dur": 4, "ph": "X", "name": "ProcessMessages 1304", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268954479, "dur": 110, "ph": "X", "name": "ReadAsync 1304", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268954600, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268954606, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268954685, "dur": 4, "ph": "X", "name": "ProcessMessages 1059", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268954690, "dur": 38, "ph": "X", "name": "ReadAsync 1059", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268954730, "dur": 3, "ph": "X", "name": "ProcessMessages 2011", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268954734, "dur": 121, "ph": "X", "name": "ReadAsync 2011", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268954866, "dur": 30, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268954898, "dur": 3, "ph": "X", "name": "ProcessMessages 2018", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268954903, "dur": 122, "ph": "X", "name": "ReadAsync 2018", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268955037, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268955044, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268955094, "dur": 5, "ph": "X", "name": "ProcessMessages 2112", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268955103, "dur": 127, "ph": "X", "name": "ReadAsync 2112", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268955236, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268955240, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268955323, "dur": 6, "ph": "X", "name": "ProcessMessages 2405", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268955331, "dur": 44, "ph": "X", "name": "ReadAsync 2405", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268955381, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268955383, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268955414, "dur": 2, "ph": "X", "name": "ProcessMessages 1172", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268955417, "dur": 17, "ph": "X", "name": "ReadAsync 1172", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268955441, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268955444, "dur": 176, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268955622, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268955624, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268955664, "dur": 4, "ph": "X", "name": "ProcessMessages 1862", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268955671, "dur": 55, "ph": "X", "name": "ReadAsync 1862", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268955728, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268955730, "dur": 49, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268955790, "dur": 1, "ph": "X", "name": "ProcessMessages 270", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268955792, "dur": 28, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268955822, "dur": 2, "ph": "X", "name": "ProcessMessages 1637", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268955826, "dur": 15, "ph": "X", "name": "ReadAsync 1637", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268955843, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268955846, "dur": 21, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268955870, "dur": 1, "ph": "X", "name": "ProcessMessages 285", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268955872, "dur": 16, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268955895, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268955897, "dur": 22, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268955921, "dur": 1, "ph": "X", "name": "ProcessMessages 670", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268955924, "dur": 21, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268955947, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268955950, "dur": 22, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268955974, "dur": 5, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268955982, "dur": 23, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956006, "dur": 1, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956009, "dur": 17, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956031, "dur": 1, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956033, "dur": 26, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956062, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956065, "dur": 35, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956102, "dur": 2, "ph": "X", "name": "ProcessMessages 767", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956105, "dur": 22, "ph": "X", "name": "ReadAsync 767", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956129, "dur": 1, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956131, "dur": 39, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956173, "dur": 4, "ph": "X", "name": "ProcessMessages 1092", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956179, "dur": 25, "ph": "X", "name": "ReadAsync 1092", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956206, "dur": 2, "ph": "X", "name": "ProcessMessages 855", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956209, "dur": 19, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956231, "dur": 5, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956239, "dur": 27, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956269, "dur": 1, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956272, "dur": 27, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956301, "dur": 2, "ph": "X", "name": "ProcessMessages 747", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956304, "dur": 26, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956333, "dur": 1, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956336, "dur": 21, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956358, "dur": 1, "ph": "X", "name": "ProcessMessages 181", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956361, "dur": 20, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956387, "dur": 1, "ph": "X", "name": "ProcessMessages 733", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956390, "dur": 19, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956412, "dur": 1, "ph": "X", "name": "ProcessMessages 686", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956414, "dur": 21, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956437, "dur": 1, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956440, "dur": 19, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956461, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956463, "dur": 18, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956483, "dur": 5, "ph": "X", "name": "ProcessMessages 82", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956491, "dur": 23, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956516, "dur": 1, "ph": "X", "name": "ProcessMessages 991", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956519, "dur": 25, "ph": "X", "name": "ReadAsync 991", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956546, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956549, "dur": 31, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956585, "dur": 2, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956589, "dur": 15, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956607, "dur": 1, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956610, "dur": 22, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956634, "dur": 1, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956636, "dur": 16, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956654, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956656, "dur": 21, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956680, "dur": 1, "ph": "X", "name": "ProcessMessages 198", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956682, "dur": 20, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956704, "dur": 1, "ph": "X", "name": "ProcessMessages 805", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956707, "dur": 19, "ph": "X", "name": "ReadAsync 805", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956728, "dur": 5, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956736, "dur": 25, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956763, "dur": 1, "ph": "X", "name": "ProcessMessages 1026", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956766, "dur": 22, "ph": "X", "name": "ReadAsync 1026", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956791, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956794, "dur": 32, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956828, "dur": 2, "ph": "X", "name": "ProcessMessages 989", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956831, "dur": 28, "ph": "X", "name": "ReadAsync 989", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956861, "dur": 1, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956864, "dur": 25, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956891, "dur": 1, "ph": "X", "name": "ProcessMessages 619", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956893, "dur": 28, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956924, "dur": 1, "ph": "X", "name": "ProcessMessages 808", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956927, "dur": 24, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956953, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956956, "dur": 26, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956985, "dur": 1, "ph": "X", "name": "ProcessMessages 685", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268956988, "dur": 27, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957016, "dur": 1, "ph": "X", "name": "ProcessMessages 766", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957019, "dur": 25, "ph": "X", "name": "ReadAsync 766", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957047, "dur": 1, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957049, "dur": 21, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957072, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957076, "dur": 23, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957102, "dur": 2, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957105, "dur": 22, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957130, "dur": 5, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957137, "dur": 26, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957166, "dur": 2, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957169, "dur": 17, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957187, "dur": 1, "ph": "X", "name": "ProcessMessages 285", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957190, "dur": 21, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957213, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957215, "dur": 21, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957238, "dur": 1, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957241, "dur": 19, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957261, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957264, "dur": 25, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957292, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957294, "dur": 22, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957318, "dur": 1, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957321, "dur": 24, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957347, "dur": 3, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957352, "dur": 27, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957382, "dur": 1, "ph": "X", "name": "ProcessMessages 915", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957384, "dur": 23, "ph": "X", "name": "ReadAsync 915", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957413, "dur": 2, "ph": "X", "name": "ProcessMessages 744", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957416, "dur": 18, "ph": "X", "name": "ReadAsync 744", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957436, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957438, "dur": 23, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957463, "dur": 6, "ph": "X", "name": "ProcessMessages 734", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957471, "dur": 26, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957500, "dur": 1, "ph": "X", "name": "ProcessMessages 724", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957503, "dur": 29, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957534, "dur": 1, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957537, "dur": 20, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957563, "dur": 1, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957566, "dur": 25, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957596, "dur": 1, "ph": "X", "name": "ProcessMessages 866", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957599, "dur": 19, "ph": "X", "name": "ReadAsync 866", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957621, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957624, "dur": 14, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957640, "dur": 1, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957642, "dur": 21, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957666, "dur": 27, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957696, "dur": 1, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957698, "dur": 26, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957727, "dur": 2, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957730, "dur": 20, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957752, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957755, "dur": 17, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957774, "dur": 5, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957781, "dur": 26, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957809, "dur": 1, "ph": "X", "name": "ProcessMessages 794", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957812, "dur": 25, "ph": "X", "name": "ReadAsync 794", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957839, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957842, "dur": 30, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957874, "dur": 2, "ph": "X", "name": "ProcessMessages 736", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957877, "dur": 22, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957902, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957904, "dur": 26, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957932, "dur": 2, "ph": "X", "name": "ProcessMessages 790", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957935, "dur": 24, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957961, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957964, "dur": 17, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957984, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268957986, "dur": 17, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958005, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958008, "dur": 23, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958032, "dur": 5, "ph": "X", "name": "ProcessMessages 685", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958040, "dur": 30, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958072, "dur": 1, "ph": "X", "name": "ProcessMessages 818", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958075, "dur": 23, "ph": "X", "name": "ReadAsync 818", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958101, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958103, "dur": 26, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958131, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958148, "dur": 21, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958172, "dur": 231, "ph": "X", "name": "ProcessMessages 1000", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958409, "dur": 103, "ph": "X", "name": "ReadAsync 1000", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958524, "dur": 8, "ph": "X", "name": "ProcessMessages 6045", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958534, "dur": 54, "ph": "X", "name": "ReadAsync 6045", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958591, "dur": 3, "ph": "X", "name": "ProcessMessages 1977", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958595, "dur": 15, "ph": "X", "name": "ReadAsync 1977", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958612, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958615, "dur": 22, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958639, "dur": 5, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958646, "dur": 26, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958675, "dur": 2, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958678, "dur": 23, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958706, "dur": 1, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958709, "dur": 23, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958734, "dur": 1, "ph": "X", "name": "ProcessMessages 677", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958737, "dur": 27, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958766, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958769, "dur": 23, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958794, "dur": 1, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958797, "dur": 27, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958826, "dur": 1, "ph": "X", "name": "ProcessMessages 897", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958829, "dur": 16, "ph": "X", "name": "ReadAsync 897", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958847, "dur": 1, "ph": "X", "name": "ProcessMessages 270", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958850, "dur": 27, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958879, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958882, "dur": 21, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958906, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958908, "dur": 19, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958930, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958932, "dur": 22, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958956, "dur": 5, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958964, "dur": 27, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958993, "dur": 2, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268958997, "dur": 24, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959023, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959026, "dur": 27, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959055, "dur": 2, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959058, "dur": 25, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959087, "dur": 1, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959090, "dur": 30, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959122, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959125, "dur": 15, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959143, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959145, "dur": 24, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959171, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959174, "dur": 25, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959202, "dur": 1, "ph": "X", "name": "ProcessMessages 783", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959205, "dur": 26, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959233, "dur": 1, "ph": "X", "name": "ProcessMessages 765", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959236, "dur": 23, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959262, "dur": 1, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959264, "dur": 25, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959292, "dur": 1, "ph": "X", "name": "ProcessMessages 658", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959294, "dur": 19, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959316, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959319, "dur": 20, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959341, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959344, "dur": 24, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959369, "dur": 5, "ph": "X", "name": "ProcessMessages 612", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959377, "dur": 22, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959401, "dur": 1, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959404, "dur": 26, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959432, "dur": 1, "ph": "X", "name": "ProcessMessages 1008", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959435, "dur": 24, "ph": "X", "name": "ReadAsync 1008", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959461, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959464, "dur": 24, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959490, "dur": 1, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959492, "dur": 18, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959513, "dur": 1, "ph": "X", "name": "ProcessMessages 491", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959515, "dur": 21, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959539, "dur": 1, "ph": "X", "name": "ProcessMessages 261", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959541, "dur": 26, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959569, "dur": 1, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959572, "dur": 20, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959594, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959597, "dur": 19, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959619, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959621, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959650, "dur": 1, "ph": "X", "name": "ProcessMessages 778", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959653, "dur": 32, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959688, "dur": 1, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959691, "dur": 26, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959718, "dur": 2, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959721, "dur": 25, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959749, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959751, "dur": 27, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959781, "dur": 1, "ph": "X", "name": "ProcessMessages 671", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959783, "dur": 26, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959812, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959815, "dur": 21, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959837, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959840, "dur": 24, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959866, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959869, "dur": 29, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959900, "dur": 2, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959903, "dur": 25, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959930, "dur": 2, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959933, "dur": 25, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959961, "dur": 1, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959964, "dur": 24, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959990, "dur": 1, "ph": "X", "name": "ProcessMessages 737", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268959993, "dur": 23, "ph": "X", "name": "ReadAsync 737", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960018, "dur": 1, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960020, "dur": 25, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960047, "dur": 1, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960050, "dur": 24, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960076, "dur": 1, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960079, "dur": 19, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960100, "dur": 2, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960103, "dur": 21, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960126, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960129, "dur": 24, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960155, "dur": 1, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960158, "dur": 26, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960187, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960189, "dur": 27, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960218, "dur": 1, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960221, "dur": 23, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960247, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960249, "dur": 19, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960271, "dur": 1, "ph": "X", "name": "ProcessMessages 679", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960273, "dur": 16, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960291, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960293, "dur": 20, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960315, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960318, "dur": 23, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960343, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960346, "dur": 27, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960375, "dur": 1, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960378, "dur": 25, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960406, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960408, "dur": 28, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960439, "dur": 1, "ph": "X", "name": "ProcessMessages 779", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960442, "dur": 23, "ph": "X", "name": "ReadAsync 779", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960467, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960470, "dur": 25, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960498, "dur": 2, "ph": "X", "name": "ProcessMessages 766", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960501, "dur": 28, "ph": "X", "name": "ReadAsync 766", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960532, "dur": 1, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960534, "dur": 25, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960562, "dur": 1, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960565, "dur": 21, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960589, "dur": 1, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960591, "dur": 26, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960619, "dur": 1, "ph": "X", "name": "ProcessMessages 665", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960622, "dur": 29, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960653, "dur": 2, "ph": "X", "name": "ProcessMessages 680", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960656, "dur": 27, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960686, "dur": 1, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960689, "dur": 26, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960717, "dur": 1, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960720, "dur": 27, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960749, "dur": 1, "ph": "X", "name": "ProcessMessages 828", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960752, "dur": 30, "ph": "X", "name": "ReadAsync 828", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960784, "dur": 1, "ph": "X", "name": "ProcessMessages 832", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960787, "dur": 22, "ph": "X", "name": "ReadAsync 832", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960811, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960814, "dur": 24, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960840, "dur": 1, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960843, "dur": 27, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960873, "dur": 1, "ph": "X", "name": "ProcessMessages 864", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960876, "dur": 29, "ph": "X", "name": "ReadAsync 864", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960908, "dur": 1, "ph": "X", "name": "ProcessMessages 912", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960911, "dur": 11, "ph": "X", "name": "ReadAsync 912", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960924, "dur": 1, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960927, "dur": 19, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960948, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960950, "dur": 23, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960976, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268960978, "dur": 26, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961006, "dur": 1, "ph": "X", "name": "ProcessMessages 687", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961009, "dur": 23, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961034, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961037, "dur": 31, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961070, "dur": 1, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961073, "dur": 29, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961103, "dur": 6, "ph": "X", "name": "ProcessMessages 981", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961112, "dur": 21, "ph": "X", "name": "ReadAsync 981", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961134, "dur": 2, "ph": "X", "name": "ProcessMessages 723", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961138, "dur": 26, "ph": "X", "name": "ReadAsync 723", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961165, "dur": 1, "ph": "X", "name": "ProcessMessages 402", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961168, "dur": 23, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961193, "dur": 1, "ph": "X", "name": "ProcessMessages 765", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961196, "dur": 21, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961219, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961222, "dur": 22, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961246, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961248, "dur": 19, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961268, "dur": 1, "ph": "X", "name": "ProcessMessages 643", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961272, "dur": 25, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961299, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961302, "dur": 25, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961329, "dur": 2, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961332, "dur": 17, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961352, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961354, "dur": 20, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961376, "dur": 1, "ph": "X", "name": "ProcessMessages 683", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961379, "dur": 17, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961397, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961400, "dur": 23, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961426, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961429, "dur": 24, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961455, "dur": 5, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961462, "dur": 17, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961484, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961487, "dur": 18, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961507, "dur": 1, "ph": "X", "name": "ProcessMessages 748", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961509, "dur": 20, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961531, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961538, "dur": 28, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961569, "dur": 1, "ph": "X", "name": "ProcessMessages 732", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961572, "dur": 23, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961597, "dur": 1, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961600, "dur": 28, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961630, "dur": 2, "ph": "X", "name": "ProcessMessages 736", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961634, "dur": 27, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961663, "dur": 1, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961666, "dur": 25, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961693, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961696, "dur": 26, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961724, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961727, "dur": 28, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961757, "dur": 1, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961760, "dur": 27, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961789, "dur": 2, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961793, "dur": 28, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961823, "dur": 1, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961826, "dur": 25, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961853, "dur": 1, "ph": "X", "name": "ProcessMessages 677", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961856, "dur": 19, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961877, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961880, "dur": 23, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961905, "dur": 1, "ph": "X", "name": "ProcessMessages 241", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961908, "dur": 23, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961933, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961936, "dur": 26, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961964, "dur": 1, "ph": "X", "name": "ProcessMessages 790", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961967, "dur": 25, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961995, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268961997, "dur": 34, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962043, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962046, "dur": 27, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962075, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962078, "dur": 26, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962106, "dur": 1, "ph": "X", "name": "ProcessMessages 200", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962109, "dur": 18, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962130, "dur": 1, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962133, "dur": 23, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962158, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962161, "dur": 20, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962183, "dur": 6, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962191, "dur": 62, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962258, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962308, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962311, "dur": 31, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962344, "dur": 2, "ph": "X", "name": "ProcessMessages 1432", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962348, "dur": 19, "ph": "X", "name": "ReadAsync 1432", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962369, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962372, "dur": 14, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962388, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962390, "dur": 64, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962459, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962485, "dur": 1, "ph": "X", "name": "ProcessMessages 595", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962488, "dur": 20, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962510, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962513, "dur": 22, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962537, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962539, "dur": 25, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962566, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962569, "dur": 22, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962594, "dur": 2, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962597, "dur": 23, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962623, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962625, "dur": 41, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962670, "dur": 103, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962778, "dur": 2, "ph": "X", "name": "ProcessMessages 729", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962782, "dur": 24, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962807, "dur": 2, "ph": "X", "name": "ProcessMessages 1081", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962810, "dur": 16, "ph": "X", "name": "ReadAsync 1081", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962829, "dur": 5, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962836, "dur": 14, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962852, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962854, "dur": 51, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962908, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962910, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962931, "dur": 2, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962934, "dur": 20, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962956, "dur": 1, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962958, "dur": 19, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962984, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268962987, "dur": 41, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963032, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963053, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963056, "dur": 21, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963079, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963086, "dur": 20, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963109, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963111, "dur": 36, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963152, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963200, "dur": 2, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963204, "dur": 24, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963229, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963232, "dur": 28, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963265, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963305, "dur": 2, "ph": "X", "name": "ProcessMessages 759", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963308, "dur": 23, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963333, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963336, "dur": 42, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963385, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963409, "dur": 2, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963412, "dur": 21, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963435, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963438, "dur": 14, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963454, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963457, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963510, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963540, "dur": 1, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963543, "dur": 24, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963569, "dur": 1, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963573, "dur": 51, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963626, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963629, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963666, "dur": 2, "ph": "X", "name": "ProcessMessages 865", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963669, "dur": 22, "ph": "X", "name": "ReadAsync 865", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963693, "dur": 1, "ph": "X", "name": "ProcessMessages 197", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963696, "dur": 41, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963742, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963773, "dur": 2, "ph": "X", "name": "ProcessMessages 724", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963776, "dur": 23, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963802, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963805, "dur": 45, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963854, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963872, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963875, "dur": 20, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963898, "dur": 5, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963906, "dur": 22, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963930, "dur": 1, "ph": "X", "name": "ProcessMessages 146", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963933, "dur": 37, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963972, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963974, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268963998, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964001, "dur": 26, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964029, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964032, "dur": 49, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964085, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964088, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964115, "dur": 2, "ph": "X", "name": "ProcessMessages 736", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964119, "dur": 16, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964137, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964139, "dur": 67, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964211, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964235, "dur": 1, "ph": "X", "name": "ProcessMessages 679", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964238, "dur": 19, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964258, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964261, "dur": 21, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964284, "dur": 1, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964287, "dur": 38, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964330, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964353, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964356, "dur": 25, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964383, "dur": 1, "ph": "X", "name": "ProcessMessages 693", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964386, "dur": 50, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964441, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964464, "dur": 1, "ph": "X", "name": "ProcessMessages 751", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964467, "dur": 23, "ph": "X", "name": "ReadAsync 751", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964492, "dur": 1, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964495, "dur": 21, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964518, "dur": 1, "ph": "X", "name": "ProcessMessages 58", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964521, "dur": 33, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964558, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964586, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964589, "dur": 25, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964616, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964619, "dur": 44, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964665, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964667, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964696, "dur": 1, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964699, "dur": 17, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964719, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964722, "dur": 52, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964778, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964806, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964809, "dur": 20, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964831, "dur": 1, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964834, "dur": 17, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964852, "dur": 4, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964860, "dur": 38, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964903, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964932, "dur": 2, "ph": "X", "name": "ProcessMessages 579", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964935, "dur": 24, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964962, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268964965, "dur": 46, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965014, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965016, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965039, "dur": 1, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965042, "dur": 19, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965063, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965066, "dur": 53, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965122, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965125, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965148, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965151, "dur": 20, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965174, "dur": 1, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965176, "dur": 15, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965193, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965196, "dur": 32, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965230, "dur": 2, "ph": "X", "name": "ProcessMessages 210", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965234, "dur": 21, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965257, "dur": 2, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965260, "dur": 15, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965277, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965280, "dur": 50, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965333, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965335, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965359, "dur": 1, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965362, "dur": 17, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965381, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965383, "dur": 55, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965443, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965467, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965470, "dur": 21, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965493, "dur": 1, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965496, "dur": 27, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965525, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965528, "dur": 18, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965548, "dur": 1, "ph": "X", "name": "ProcessMessages 471", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965551, "dur": 26, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965579, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965582, "dur": 22, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965606, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965608, "dur": 42, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965655, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965682, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965685, "dur": 26, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965713, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965716, "dur": 46, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965764, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965767, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965797, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965800, "dur": 24, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965826, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965828, "dur": 42, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965873, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965876, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965903, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965906, "dur": 24, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965932, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965935, "dur": 45, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268965984, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966013, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966016, "dur": 25, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966044, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966047, "dur": 26, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966075, "dur": 2, "ph": "X", "name": "ProcessMessages 579", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966078, "dur": 60, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966142, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966144, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966189, "dur": 5, "ph": "X", "name": "ProcessMessages 1156", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966195, "dur": 30, "ph": "X", "name": "ReadAsync 1156", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966229, "dur": 6, "ph": "X", "name": "ProcessMessages 802", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966238, "dur": 31, "ph": "X", "name": "ReadAsync 802", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966271, "dur": 2, "ph": "X", "name": "ProcessMessages 655", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966274, "dur": 23, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966300, "dur": 1, "ph": "X", "name": "ProcessMessages 234", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966303, "dur": 37, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966345, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966373, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966376, "dur": 27, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966405, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966408, "dur": 45, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966456, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966459, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966484, "dur": 1, "ph": "X", "name": "ProcessMessages 738", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966486, "dur": 20, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966508, "dur": 5, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966516, "dur": 29, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966548, "dur": 2, "ph": "X", "name": "ProcessMessages 789", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966551, "dur": 25, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966578, "dur": 1, "ph": "X", "name": "ProcessMessages 704", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966581, "dur": 20, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966604, "dur": 1, "ph": "X", "name": "ProcessMessages 129", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966607, "dur": 21, "ph": "X", "name": "ReadAsync 129", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966630, "dur": 1, "ph": "X", "name": "ProcessMessages 200", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966633, "dur": 39, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966675, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966678, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966706, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966709, "dur": 19, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966734, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966737, "dur": 50, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966789, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966792, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966816, "dur": 1, "ph": "X", "name": "ProcessMessages 751", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966819, "dur": 22, "ph": "X", "name": "ReadAsync 751", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966843, "dur": 1, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966846, "dur": 18, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966866, "dur": 1, "ph": "X", "name": "ProcessMessages 659", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966869, "dur": 25, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966897, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966900, "dur": 24, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966927, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966929, "dur": 22, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966954, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966956, "dur": 41, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268966999, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967001, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967022, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967025, "dur": 23, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967055, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967058, "dur": 21, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967082, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967085, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967117, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967119, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967146, "dur": 2, "ph": "X", "name": "ProcessMessages 737", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967149, "dur": 21, "ph": "X", "name": "ReadAsync 737", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967173, "dur": 5, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967180, "dur": 45, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967228, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967230, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967264, "dur": 2, "ph": "X", "name": "ProcessMessages 883", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967267, "dur": 17, "ph": "X", "name": "ReadAsync 883", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967294, "dur": 17, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967314, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967317, "dur": 17, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967337, "dur": 4, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967344, "dur": 29, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967375, "dur": 2, "ph": "X", "name": "ProcessMessages 872", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967378, "dur": 26, "ph": "X", "name": "ReadAsync 872", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967407, "dur": 1, "ph": "X", "name": "ProcessMessages 720", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967409, "dur": 26, "ph": "X", "name": "ReadAsync 720", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967438, "dur": 1, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967441, "dur": 21, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967466, "dur": 1, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967469, "dur": 22, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967494, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967496, "dur": 42, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967542, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967544, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967569, "dur": 1, "ph": "X", "name": "ProcessMessages 859", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967572, "dur": 21, "ph": "X", "name": "ReadAsync 859", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967595, "dur": 1, "ph": "X", "name": "ProcessMessages 261", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967602, "dur": 52, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967656, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967659, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967683, "dur": 2, "ph": "X", "name": "ProcessMessages 831", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967686, "dur": 19, "ph": "X", "name": "ReadAsync 831", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967708, "dur": 5, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967716, "dur": 46, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967766, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967793, "dur": 1, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967796, "dur": 21, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967820, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967823, "dur": 46, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967871, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967873, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967892, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967895, "dur": 19, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967916, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967923, "dur": 22, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967948, "dur": 1, "ph": "X", "name": "ProcessMessages 138", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967950, "dur": 33, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268967988, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968019, "dur": 2, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968022, "dur": 31, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968056, "dur": 2, "ph": "X", "name": "ProcessMessages 793", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968059, "dur": 29, "ph": "X", "name": "ReadAsync 793", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968090, "dur": 2, "ph": "X", "name": "ProcessMessages 665", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968093, "dur": 24, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968119, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968122, "dur": 28, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968153, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968156, "dur": 41, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968201, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968226, "dur": 1, "ph": "X", "name": "ProcessMessages 665", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968229, "dur": 21, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968257, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968261, "dur": 52, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968320, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968323, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968361, "dur": 3, "ph": "X", "name": "ProcessMessages 952", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968366, "dur": 27, "ph": "X", "name": "ReadAsync 952", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968396, "dur": 1, "ph": "X", "name": "ProcessMessages 143", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968399, "dur": 26, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968430, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968463, "dur": 1, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968466, "dur": 38, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968506, "dur": 2, "ph": "X", "name": "ProcessMessages 905", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968510, "dur": 44, "ph": "X", "name": "ReadAsync 905", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968556, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968559, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968594, "dur": 2, "ph": "X", "name": "ProcessMessages 881", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968597, "dur": 16, "ph": "X", "name": "ReadAsync 881", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968615, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968618, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968650, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968668, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968671, "dur": 46, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968722, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968744, "dur": 1, "ph": "X", "name": "ProcessMessages 810", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968747, "dur": 15, "ph": "X", "name": "ReadAsync 810", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968764, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968766, "dur": 20, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968789, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968791, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968844, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968866, "dur": 1, "ph": "X", "name": "ProcessMessages 915", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968869, "dur": 24, "ph": "X", "name": "ReadAsync 915", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968896, "dur": 1, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968899, "dur": 48, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968952, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968981, "dur": 1, "ph": "X", "name": "ProcessMessages 746", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268968984, "dur": 25, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969011, "dur": 1, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969014, "dur": 43, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969060, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969062, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969085, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969088, "dur": 19, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969111, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969140, "dur": 2, "ph": "X", "name": "ProcessMessages 786", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969143, "dur": 26, "ph": "X", "name": "ReadAsync 786", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969172, "dur": 1, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969175, "dur": 27, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969204, "dur": 2, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969207, "dur": 22, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969232, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969235, "dur": 46, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969285, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969310, "dur": 1, "ph": "X", "name": "ProcessMessages 766", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969313, "dur": 22, "ph": "X", "name": "ReadAsync 766", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969337, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969340, "dur": 52, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969397, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969419, "dur": 1, "ph": "X", "name": "ProcessMessages 818", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969423, "dur": 21, "ph": "X", "name": "ReadAsync 818", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969446, "dur": 5, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969454, "dur": 21, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969477, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969480, "dur": 34, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969517, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969519, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969553, "dur": 2, "ph": "X", "name": "ProcessMessages 864", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969556, "dur": 16, "ph": "X", "name": "ReadAsync 864", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969574, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969577, "dur": 49, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969630, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969654, "dur": 5, "ph": "X", "name": "ProcessMessages 709", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969661, "dur": 17, "ph": "X", "name": "ReadAsync 709", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969680, "dur": 1, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969683, "dur": 53, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969738, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969740, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969765, "dur": 1, "ph": "X", "name": "ProcessMessages 874", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969768, "dur": 18, "ph": "X", "name": "ReadAsync 874", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969788, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969791, "dur": 55, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969850, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969874, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969878, "dur": 21, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969901, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969904, "dur": 49, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969955, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969957, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969983, "dur": 1, "ph": "X", "name": "ProcessMessages 755", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268969986, "dur": 16, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970005, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970007, "dur": 55, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970065, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970067, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970094, "dur": 2, "ph": "X", "name": "ProcessMessages 878", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970098, "dur": 17, "ph": "X", "name": "ReadAsync 878", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970117, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970120, "dur": 54, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970179, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970199, "dur": 1, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970201, "dur": 16, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970219, "dur": 3, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970223, "dur": 12, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970237, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970239, "dur": 52, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970294, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970296, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970317, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970320, "dur": 23, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970345, "dur": 1, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970347, "dur": 21, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970371, "dur": 1, "ph": "X", "name": "ProcessMessages 143", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970373, "dur": 39, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970416, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970437, "dur": 1, "ph": "X", "name": "ProcessMessages 726", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970440, "dur": 24, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970466, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970469, "dur": 20, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970491, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970494, "dur": 35, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970533, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970557, "dur": 1, "ph": "X", "name": "ProcessMessages 701", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970560, "dur": 19, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970582, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970585, "dur": 52, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970641, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970670, "dur": 2, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970673, "dur": 36, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970710, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970713, "dur": 31, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970747, "dur": 2, "ph": "X", "name": "ProcessMessages 655", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970750, "dur": 29, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970781, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970785, "dur": 27, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970814, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970817, "dur": 29, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970851, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970884, "dur": 2, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970889, "dur": 27, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970919, "dur": 1, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970922, "dur": 36, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970962, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970992, "dur": 2, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268970995, "dur": 25, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971022, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971025, "dur": 43, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971073, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971116, "dur": 2, "ph": "X", "name": "ProcessMessages 888", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971119, "dur": 21, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971142, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971145, "dur": 60, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971208, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971210, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971234, "dur": 2, "ph": "X", "name": "ProcessMessages 1147", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971238, "dur": 48, "ph": "X", "name": "ReadAsync 1147", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971289, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971291, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971318, "dur": 1, "ph": "X", "name": "ProcessMessages 707", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971321, "dur": 19, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971342, "dur": 1, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971345, "dur": 28, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971379, "dur": 2, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971384, "dur": 24, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971410, "dur": 2, "ph": "X", "name": "ProcessMessages 686", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971414, "dur": 20, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971441, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971445, "dur": 51, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971498, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971500, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971520, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971523, "dur": 22, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971547, "dur": 5, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971555, "dur": 22, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971579, "dur": 1, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971582, "dur": 31, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971616, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971618, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971640, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971642, "dur": 21, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971665, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971672, "dur": 13, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971687, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971690, "dur": 40, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971734, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971759, "dur": 1, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971762, "dur": 21, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971785, "dur": 5, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971793, "dur": 47, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971844, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971872, "dur": 2, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971875, "dur": 21, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971898, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971902, "dur": 56, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971960, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971962, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971985, "dur": 2, "ph": "X", "name": "ProcessMessages 727", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268971989, "dur": 22, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972018, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972022, "dur": 49, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972073, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972075, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972104, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972107, "dur": 17, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972130, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972134, "dur": 46, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972185, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972207, "dur": 2, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972210, "dur": 23, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972236, "dur": 1, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972238, "dur": 21, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972261, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972264, "dur": 38, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972305, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972307, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972336, "dur": 2, "ph": "X", "name": "ProcessMessages 663", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972339, "dur": 26, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972367, "dur": 1, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972370, "dur": 52, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972424, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972426, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972450, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972453, "dur": 21, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972476, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972479, "dur": 21, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972502, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972505, "dur": 25, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972532, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972535, "dur": 25, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972561, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972564, "dur": 23, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972590, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972592, "dur": 43, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972640, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972658, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972661, "dur": 30, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972693, "dur": 6, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972702, "dur": 45, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972750, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972752, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972771, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972774, "dur": 23, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972799, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972802, "dur": 26, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972830, "dur": 1, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972833, "dur": 26, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972861, "dur": 1, "ph": "X", "name": "ProcessMessages 551", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972864, "dur": 17, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972887, "dur": 1, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972890, "dur": 18, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972910, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972914, "dur": 49, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972965, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972967, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972989, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268972992, "dur": 20, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268973014, "dur": 6, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268973022, "dur": 27, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268973051, "dur": 2, "ph": "X", "name": "ProcessMessages 665", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268973055, "dur": 33, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268973090, "dur": 2, "ph": "X", "name": "ProcessMessages 781", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268973093, "dur": 26, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268973122, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268973125, "dur": 38, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268973167, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268973195, "dur": 1, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268973198, "dur": 27, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268973228, "dur": 1, "ph": "X", "name": "ProcessMessages 702", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268973231, "dur": 26, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268973259, "dur": 1, "ph": "X", "name": "ProcessMessages 655", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268973262, "dur": 29, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268973293, "dur": 2, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268973296, "dur": 22, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268973320, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268973323, "dur": 48, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268973376, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268973396, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268973399, "dur": 73, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268973476, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268973479, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268973515, "dur": 279, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268973800, "dur": 143, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268973951, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268973957, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268973998, "dur": 5, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974005, "dur": 42, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974052, "dur": 4, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974059, "dur": 33, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974098, "dur": 5, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974106, "dur": 30, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974141, "dur": 3, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974146, "dur": 28, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974179, "dur": 5, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974187, "dur": 27, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974218, "dur": 3, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974230, "dur": 33, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974266, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974270, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974298, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974302, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974339, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974345, "dur": 32, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974381, "dur": 4, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974387, "dur": 34, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974427, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974433, "dur": 23, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974460, "dur": 4, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974465, "dur": 22, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974494, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974500, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974540, "dur": 5, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974547, "dur": 87, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974637, "dur": 4, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974643, "dur": 29, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974675, "dur": 4, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974681, "dur": 34, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974718, "dur": 4, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974724, "dur": 30, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974759, "dur": 4, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974766, "dur": 25, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974795, "dur": 5, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974802, "dur": 25, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974832, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974838, "dur": 26, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974867, "dur": 6, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974875, "dur": 27, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974908, "dur": 5, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974916, "dur": 39, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974960, "dur": 6, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974968, "dur": 26, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268974997, "dur": 6, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975005, "dur": 34, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975043, "dur": 3, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975049, "dur": 28, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975082, "dur": 5, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975091, "dur": 33, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975128, "dur": 5, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975137, "dur": 35, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975179, "dur": 5, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975186, "dur": 35, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975226, "dur": 6, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975234, "dur": 33, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975271, "dur": 5, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975278, "dur": 34, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975316, "dur": 4, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975322, "dur": 34, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975361, "dur": 3, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975366, "dur": 33, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975403, "dur": 4, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975408, "dur": 27, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975439, "dur": 5, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975447, "dur": 31, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975485, "dur": 8, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975495, "dur": 35, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975536, "dur": 5, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975543, "dur": 38, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975591, "dur": 3, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975597, "dur": 31, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975631, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975634, "dur": 89, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975728, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975731, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975758, "dur": 3, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975763, "dur": 30, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975798, "dur": 4, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975805, "dur": 34, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975843, "dur": 6, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975852, "dur": 28, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975885, "dur": 4, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975892, "dur": 27, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975924, "dur": 3, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975929, "dur": 25, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975958, "dur": 4, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975964, "dur": 27, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268975996, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268976002, "dur": 31, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268976038, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268976044, "dur": 27, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268976073, "dur": 3, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268976079, "dur": 21, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268976103, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268976108, "dur": 17, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268976128, "dur": 3, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268976133, "dur": 15, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268976151, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268976155, "dur": 19, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268976179, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268976185, "dur": 27, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268976215, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268976220, "dur": 11, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268976233, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268976236, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268976266, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268976270, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268976291, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268976295, "dur": 5281, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268981583, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268981589, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268981615, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268981619, "dur": 669, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268982294, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268982299, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268982325, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268982330, "dur": 212, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268982548, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268982553, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268982577, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268982581, "dur": 1587, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268984181, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268984188, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268984239, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268984247, "dur": 78, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268984331, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268984335, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268984359, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268984362, "dur": 310, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268984677, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268984680, "dur": 118, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268984805, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268984810, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268984848, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268984852, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268984869, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268984872, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268984889, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268984891, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268984915, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268984919, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268984940, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268984944, "dur": 22, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268984969, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268984972, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268984987, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268984991, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985017, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985022, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985041, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985045, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985065, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985067, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985084, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985087, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985102, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985104, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985134, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985137, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985155, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985158, "dur": 238, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985400, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985403, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985420, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985423, "dur": 174, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985600, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985603, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985624, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985628, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985647, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985650, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985669, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985673, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985688, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985691, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985707, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985710, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985737, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985740, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985753, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985758, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985779, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985782, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985798, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985800, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985817, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985820, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985852, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985856, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985877, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985880, "dur": 89, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985974, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985978, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985995, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268985998, "dur": 46, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986047, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986050, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986073, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986076, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986116, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986119, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986134, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986138, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986156, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986160, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986231, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986234, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986251, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986254, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986277, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986295, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986300, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986318, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986321, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986356, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986359, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986379, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986383, "dur": 38, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986424, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986427, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986443, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986446, "dur": 147, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986596, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986599, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986622, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986626, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986641, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986644, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986702, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986704, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986724, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986727, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986747, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986750, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986767, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986770, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986801, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986820, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986823, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986874, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986877, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986894, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986897, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986912, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986915, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986933, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986936, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986951, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986954, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986971, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986974, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268986996, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987001, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987068, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987070, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987090, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987094, "dur": 15, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987112, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987115, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987178, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987181, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987196, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987199, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987213, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987216, "dur": 108, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987327, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987330, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987345, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987348, "dur": 68, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987420, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987423, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987439, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987443, "dur": 73, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987518, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987521, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987539, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987543, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987578, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987584, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987608, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987613, "dur": 91, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987708, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987712, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987734, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987738, "dur": 49, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987791, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987794, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987809, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987812, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987833, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987836, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987852, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987855, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987871, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987874, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987951, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987953, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987969, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987973, "dur": 18, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987996, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268987999, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988017, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988020, "dur": 133, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988157, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988160, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988189, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988193, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988216, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988220, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988239, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988242, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988264, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988267, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988288, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988292, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988308, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988311, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988325, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988328, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988353, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988356, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988373, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988376, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988395, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988399, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988440, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988443, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988465, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988468, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988486, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988489, "dur": 11, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988503, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988506, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988527, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988531, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988556, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988560, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988585, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988589, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988608, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988611, "dur": 12, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988625, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988629, "dur": 93, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988726, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988729, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988746, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988749, "dur": 11, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988763, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988765, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988785, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988787, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988807, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988810, "dur": 110, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988924, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988927, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988941, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988945, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988964, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268988967, "dur": 183, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989153, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989157, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989172, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989174, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989218, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989221, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989239, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989243, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989259, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989262, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989277, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989280, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989297, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989301, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989330, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989333, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989360, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989364, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989397, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989402, "dur": 62, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989467, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989469, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989495, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989498, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989519, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989521, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989546, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989549, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989572, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989574, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989591, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989594, "dur": 58, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989655, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989673, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989676, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989701, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989703, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989720, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989722, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989739, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989741, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989760, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989763, "dur": 166, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989935, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989939, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989966, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268989974, "dur": 25, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268990002, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268990006, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268990031, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268990035, "dur": 72, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268990111, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268990115, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268990134, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268990138, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268990170, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268990174, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268990234, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268990238, "dur": 72, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268990315, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268990319, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268990348, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268990352, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268990374, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268990377, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268990401, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268990403, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268990465, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268990469, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268990509, "dur": 866, "ph": "X", "name": "ProcessMessages 72", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268991383, "dur": 53, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268991441, "dur": 5, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268991448, "dur": 33, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268991487, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268991491, "dur": 91, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268991588, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268991592, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268991625, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268991633, "dur": 74, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268991712, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268991717, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268991738, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268991742, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268991768, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268991772, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268991793, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268991795, "dur": 81, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268991881, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268991885, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268991904, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268991908, "dur": 61, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268991972, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268991976, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992006, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992011, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992029, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992032, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992084, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992088, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992108, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992112, "dur": 119, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992235, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992238, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992266, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992270, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992296, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992301, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992326, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992331, "dur": 82, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992416, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992419, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992438, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992440, "dur": 127, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992571, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992575, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992600, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992604, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992624, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992627, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992649, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992653, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992676, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992679, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992702, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992706, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992736, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992741, "dur": 76, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992822, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992826, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992846, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992849, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992870, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992874, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992899, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992904, "dur": 81, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992991, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268992994, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268993014, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268993017, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268993033, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268993037, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268993058, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268993062, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268993080, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268993084, "dur": 63, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268993151, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268993154, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268993177, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268993181, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268993198, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268993201, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268993220, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268993223, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268993266, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268993270, "dur": 83, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268993356, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268993361, "dur": 91, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268993456, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268993460, "dur": 81, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268993548, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268993552, "dur": 503, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268994062, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268994066, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268994106, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268994110, "dur": 115, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268994231, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268994234, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268994271, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268994274, "dur": 467, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268994747, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268994751, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268994782, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268994787, "dur": 64, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268994856, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268994860, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268994893, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268994897, "dur": 261, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268995165, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268995168, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268995203, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268995207, "dur": 133, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268995345, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268995350, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268995390, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268995395, "dur": 285, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268995686, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268995689, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268995710, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268995714, "dur": 674, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268996396, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268996401, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268996434, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389268996441, "dur": 22872, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269019324, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269019330, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269019382, "dur": 1699, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269021088, "dur": 6210, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269027310, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269027316, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269027350, "dur": 3, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269027355, "dur": 15, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269027373, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269027376, "dur": 241, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269027624, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269027630, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269027662, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269027666, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269027695, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269027700, "dur": 316, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269028019, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269028023, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269028064, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269028068, "dur": 872, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269028945, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269028949, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269028968, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269028971, "dur": 206, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269029181, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269029185, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269029202, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269029205, "dur": 494, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269029704, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269029708, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269029727, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269029731, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269029759, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269029762, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269029781, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269029785, "dur": 12, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269029800, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269029803, "dur": 313, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269030120, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269030123, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269030139, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269030142, "dur": 156, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269030302, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269030305, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269030326, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269030330, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269030344, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269030347, "dur": 242, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269030592, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269030595, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269030618, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269030622, "dur": 182, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269030807, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269030810, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269030830, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269030833, "dur": 261, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269031098, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269031101, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269031128, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269031133, "dur": 255, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269031392, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269031395, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269031410, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269031413, "dur": 558, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269031976, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269031979, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269031996, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269032000, "dur": 200, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269032203, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269032206, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269032221, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269032224, "dur": 73, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269032302, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269032306, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269032326, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269032329, "dur": 276, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269032609, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269032611, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269032629, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269032633, "dur": 409, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269033047, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269033053, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269033081, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269033085, "dur": 274, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269033363, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269033366, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269033390, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269033394, "dur": 345, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269033743, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269033746, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269033769, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269033773, "dur": 276, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269034053, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269034055, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269034077, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269034081, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269034098, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269034102, "dur": 202, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269034308, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269034311, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269034335, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269034340, "dur": 92, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269034438, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269034444, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269034468, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269034471, "dur": 119, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269034594, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269034598, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269034632, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269034637, "dur": 115, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269034757, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269034761, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269034782, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269034785, "dur": 211, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269034999, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269035002, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269035020, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269035023, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269035067, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269035070, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269035105, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269035110, "dur": 400, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269035514, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269035518, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269035537, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269035540, "dur": 709, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269036256, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269036261, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269036286, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269036289, "dur": 464, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269036758, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269036763, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269036785, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269036789, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269036846, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269036850, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269036870, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269036873, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269036895, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269036898, "dur": 14, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269036915, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269036919, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269037002, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269037006, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269037035, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269037039, "dur": 221, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269037263, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269037267, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269037299, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269037303, "dur": 502, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269037810, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269037814, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269037832, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269037835, "dur": 173, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269038012, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269038014, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269038029, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269038032, "dur": 81, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269038119, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269038123, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269038142, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269038144, "dur": 780, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269038928, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269038931, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269038945, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269038948, "dur": 94, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269039045, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269039048, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269039063, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269039065, "dur": 164, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269039233, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269039237, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269039253, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269039256, "dur": 13, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269039271, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269039274, "dur": 260, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269039538, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269039542, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269039566, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269039570, "dur": 415, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269039989, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269039993, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040011, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040015, "dur": 12, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040029, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040032, "dur": 141, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040176, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040179, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040199, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040203, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040259, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040297, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040301, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040323, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040327, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040344, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040348, "dur": 12, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040362, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040365, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040379, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040382, "dur": 16, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040402, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040405, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040420, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040423, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040438, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040441, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040457, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040459, "dur": 14, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040476, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040479, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040496, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040500, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040515, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040518, "dur": 10, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040531, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040534, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040551, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040554, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040574, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040577, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040594, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040597, "dur": 12, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040611, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040614, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040634, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040637, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040656, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040659, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040682, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040686, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040702, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040705, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040725, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040731, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040747, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040750, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040772, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040776, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040794, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040798, "dur": 14, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040815, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040819, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040839, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040843, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040869, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040873, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040893, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040896, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040915, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040920, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040938, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040942, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040963, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040967, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040985, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269040989, "dur": 15, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041007, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041009, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041031, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041036, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041053, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041057, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041075, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041081, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041098, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041101, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041117, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041120, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041137, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041141, "dur": 14, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041157, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041161, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041180, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041185, "dur": 22, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041213, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041219, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041253, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041258, "dur": 20, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041282, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041287, "dur": 18, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041307, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041311, "dur": 18, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041332, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041336, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041359, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041364, "dur": 18, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041385, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041388, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041404, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041407, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041424, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041427, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041455, "dur": 3, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041461, "dur": 21, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041484, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041488, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041523, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041526, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041548, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041552, "dur": 18, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041572, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041577, "dur": 24, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041605, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041611, "dur": 43, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041660, "dur": 5, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041667, "dur": 27, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041697, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041702, "dur": 117, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041826, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041830, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041858, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269041862, "dur": 190, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269042059, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269042063, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269042102, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269042106, "dur": 366, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269042486, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269042491, "dur": 127, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269042627, "dur": 8, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269042636, "dur": 204888, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269247534, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269247539, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269247580, "dur": 23, "ph": "X", "name": "ProcessMessages 1805", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269247606, "dur": 4577, "ph": "X", "name": "ReadAsync 1805", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269252192, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269252197, "dur": 105, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269252308, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269252313, "dur": 41368, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269293704, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269293710, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269293741, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269293746, "dur": 33, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269293782, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269293785, "dur": 60020, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269353819, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269353825, "dur": 95, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269353925, "dur": 26, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269353954, "dur": 15171, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269369138, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269369144, "dur": 123, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269369272, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269369277, "dur": 35741, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269405041, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269405047, "dur": 129, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269405183, "dur": 41, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269405226, "dur": 15636, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269420873, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269420879, "dur": 108, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269420993, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269421000, "dur": 1115, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269422124, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269422129, "dur": 133, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269422271, "dur": 29, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269422303, "dur": 74667, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269496979, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269496984, "dur": 136, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269497127, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269497134, "dur": 1457, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269498599, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269498604, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269498651, "dur": 44, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269498699, "dur": 954, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269499663, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269499670, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269499705, "dur": 495, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 2364, "tid": 12884901888, "ts": 1751389269500205, "dur": 29033, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 2364, "tid": 2188, "ts": 1751389269542207, "dur": 5881, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 2364, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 2364, "tid": 8589934592, "ts": 1751389268943982, "dur": 233316, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 2364, "tid": 8589934592, "ts": 1751389269177301, "dur": 8, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 2364, "tid": 8589934592, "ts": 1751389269177310, "dur": 1161, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 2364, "tid": 2188, "ts": 1751389269548107, "dur": 21, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 2364, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 2364, "tid": 4294967296, "ts": 1751389268923203, "dur": 607214, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 2364, "tid": 4294967296, "ts": 1751389268926387, "dur": 5513, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 2364, "tid": 4294967296, "ts": 1751389269530658, "dur": 5224, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 2364, "tid": 4294967296, "ts": 1751389269533309, "dur": 276, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 2364, "tid": 4294967296, "ts": 1751389269535987, "dur": 18, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 2364, "tid": 2188, "ts": 1751389269548131, "dur": 16, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751389268943576, "dur": 1429, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751389268945012, "dur": 1761, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751389268946874, "dur": 147, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751389268947021, "dur": 324, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751389268948145, "dur": 891, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_4B40DB5AF1CE9D22.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751389268949693, "dur": 2356, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751389268952208, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751389268952750, "dur": 126, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751389268953159, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751389268953308, "dur": 97, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PlasticSCM.Editor.ref.dll_EBB1919501649DC3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751389268953502, "dur": 93, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ref.dll_C2E5A21D32AE5F0E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751389268953667, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751389268953978, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751389268954127, "dur": 93, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751389268954284, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751389268954538, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.ref.dll_2B244DB205431D5A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751389268954722, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751389268954902, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751389268955101, "dur": 104, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Path.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751389268955471, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751389268958259, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751389268947360, "dur": 25849, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751389268973226, "dur": 525206, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751389269498433, "dur": 727, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751389269499282, "dur": 51, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751389269499419, "dur": 23652, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751389268947447, "dur": 25793, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751389268973298, "dur": 503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Apple.Extensions.Common.dll_D4E020400338F5BB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751389268974013, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751389268974528, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751389268974674, "dur": 7395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751389268982142, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751389268982371, "dur": 1597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751389268984045, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751389268984144, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751389268984500, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751389268984664, "dur": 944, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751389268985613, "dur": 768, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751389268986382, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751389268986558, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751389268986799, "dur": 718, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751389268987517, "dur": 513, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751389268988064, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751389268988445, "dur": 930, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751389268989376, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751389268989593, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751389268989755, "dur": 569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751389268990378, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751389268990539, "dur": 691, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751389268991288, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751389268991413, "dur": 1375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751389268992851, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751389268992979, "dur": 848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751389268993869, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751389268994012, "dur": 524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751389268994580, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751389268994676, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751389268995012, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751389268995159, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751389268995505, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751389268996118, "dur": 78, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751389268996652, "dur": 250617, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751389269251718, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751389269251355, "dur": 550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751389269252448, "dur": 38531, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751389269251934, "dur": 39841, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751389269292837, "dur": 170, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751389269293737, "dur": 59828, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751389269368707, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751389269368699, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751389269368813, "dur": 129617, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751389268947493, "dur": 25758, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751389268973253, "dur": 576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_7B22B0BDD3082718.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751389268973845, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_692349F0772110A2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751389268973948, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_FA0E2521CCF3B353.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751389268974049, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_B70C9B9527050002.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751389268974403, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_C359BB03E2AB95AC.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751389268974776, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751389268975178, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751389268975260, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751389268975404, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751389268975803, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13600192148971892375.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751389268975996, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751389268976231, "dur": 799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751389268977031, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751389268977676, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751389268977904, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751389268978138, "dur": 549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751389268978687, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751389268978948, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751389268979157, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751389268979392, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751389268979617, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751389268979919, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751389268980158, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751389268980403, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751389268980613, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751389268981250, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751389268981466, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751389268981734, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751389268981957, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751389268982194, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751389268982436, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751389268982656, "dur": 347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751389268983003, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751389268983288, "dur": 732, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751389268984021, "dur": 492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751389268984514, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751389268984686, "dur": 500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751389268985255, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751389268985452, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751389268985709, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751389268985878, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751389268986060, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751389268986255, "dur": 614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751389268986869, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751389268987012, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751389268987252, "dur": 2572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751389268989824, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751389268990001, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751389268990161, "dur": 810, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751389268990972, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751389268991068, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751389268991155, "dur": 392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751389268991602, "dur": 361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751389268991963, "dur": 3053, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751389268995016, "dur": 29822, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751389269024840, "dur": 2209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751389269027099, "dur": 2428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-csharp.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751389269029562, "dur": 2826, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751389269032443, "dur": 2367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751389269034843, "dur": 2790, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/ScriptablePacker.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751389269037637, "dur": 1115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751389269038759, "dur": 2617, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/OutlineFx.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751389269041461, "dur": 209915, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751389269251400, "dur": 39615, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751389269251378, "dur": 40825, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751389269293355, "dur": 220, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751389269293843, "dur": 110908, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751389269420411, "dur": 76268, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751389269420404, "dur": 76277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751389269496712, "dur": 1664, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751389268947443, "dur": 25788, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268973354, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_FB049F22F4C5497A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751389268973748, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_D8154579280ABE30.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751389268974044, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268974438, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_A474E787AE0D7A75.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751389268974812, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751389268974898, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751389268975398, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268975615, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.TestFramework.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751389268976011, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268976267, "dur": 824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268977091, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268977294, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268977502, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268977739, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268977958, "dur": 590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268978548, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268978804, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268979030, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268979243, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268979486, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268979707, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268979950, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268980156, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268980388, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268980602, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268980826, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268981054, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268981276, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268981485, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268981708, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268981926, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268982232, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268982458, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268982674, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268982906, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268983119, "dur": 511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268983630, "dur": 388, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268984018, "dur": 492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268984511, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751389268984719, "dur": 523, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751389268985243, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268985488, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751389268985638, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268985828, "dur": 709, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751389268986538, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268986633, "dur": 456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751389268987090, "dur": 571, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268987705, "dur": 755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751389268988461, "dur": 624, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268989116, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268989195, "dur": 413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268989608, "dur": 2350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268991958, "dur": 3054, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389268995015, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751389268995167, "dur": 29669, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389269024839, "dur": 2278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751389269027153, "dur": 3428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751389269030581, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389269030637, "dur": 3206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751389269033880, "dur": 2729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751389269036610, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389269036675, "dur": 4300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751389269041007, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Aseprite.Common.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751389269041162, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389269041463, "dur": 327275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751389269368771, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751389269368740, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751389269368855, "dur": 129589, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751389268947473, "dur": 25772, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751389268973248, "dur": 476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_7A744375D0C40DCD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751389268973740, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_7BCDC5BDF664862C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751389268973814, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_7BCDC5BDF664862C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751389268974045, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_24D87271FC85A7AF.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751389268974913, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751389268974970, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751389268975173, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Psdimporter.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1751389268975445, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751389268975653, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11908194702949990099.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751389268975993, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751389268976208, "dur": 777, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751389268976985, "dur": 1081, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751389268978067, "dur": 550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751389268978617, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751389268978887, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751389268979107, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751389268979334, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751389268979582, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751389268979888, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751389268980123, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751389268980368, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751389268980595, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751389268980815, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751389268981035, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751389268981279, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751389268981492, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751389268981728, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751389268981942, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751389268982161, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751389268982632, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751389268982883, "dur": 81, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751389268982964, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751389268983283, "dur": 729, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751389268984033, "dur": 475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751389268984511, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751389268984898, "dur": 493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751389268985391, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751389268985540, "dur": 1698, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751389268987269, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751389268987436, "dur": 343, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751389268987784, "dur": 735, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751389268988520, "dur": 572, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751389268989120, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751389268989192, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751389268989338, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751389268989603, "dur": 2354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751389268991957, "dur": 3057, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751389268995022, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751389268995172, "dur": 29671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751389269024844, "dur": 2198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/OutlineFx.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751389269027043, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751389269027123, "dur": 2376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751389269029533, "dur": 2226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751389269031801, "dur": 2585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751389269034417, "dur": 2242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751389269036660, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751389269036725, "dur": 2296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualEffectGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751389269039076, "dur": 2742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751389269041868, "dur": 456576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751389268947550, "dur": 25718, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751389268973271, "dur": 466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_98CF6329D3D6F9AB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751389268974014, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_41563AEB7BF951B8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751389268974451, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_BACE11EBDBD2371C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751389268974772, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751389268975388, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751389268975580, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-firstpass.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751389268975923, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/345768040152562728.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751389268976044, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16876957367423690161.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751389268976098, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751389268976478, "dur": 783, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751389268977262, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751389268977478, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751389268977722, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751389268977942, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751389268978177, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751389268978745, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751389268978973, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751389268979191, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751389268979422, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751389268979660, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751389268979899, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751389268980169, "dur": 400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751389268980569, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751389268980792, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751389268981012, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751389268981250, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751389268981465, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751389268981695, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751389268981920, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751389268982143, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751389268982389, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751389268982606, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751389268982817, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751389268983018, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751389268983282, "dur": 733, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751389268984016, "dur": 488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751389268984506, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751389268984838, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751389268984935, "dur": 527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751389268985462, "dur": 683, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751389268986187, "dur": 752, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751389268986940, "dur": 895, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751389268987841, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity-editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751389268988003, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751389268988098, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751389268988306, "dur": 773, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751389268989086, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751389268989187, "dur": 709, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 5, "ts": 1751389268989954, "dur": 174, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751389268990310, "dur": 28812, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 5, "ts": 1751389269024834, "dur": 2219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751389269027054, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751389269027126, "dur": 2290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751389269029417, "dur": 741, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751389269030164, "dur": 2247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751389269032445, "dur": 2028, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751389269034475, "dur": 420, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751389269034901, "dur": 2178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751389269037116, "dur": 2215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751389269039367, "dur": 2076, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751389269041507, "dur": 456924, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751389268947515, "dur": 25742, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751389268973259, "dur": 548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_89517EAC2A874601.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751389268973836, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_07ED47E0EFE03F3D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751389268973992, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_2AE2A6D2A1246318.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751389268974556, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751389268974724, "dur": 6622, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751389268981422, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751389268981652, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751389268981887, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751389268982109, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751389268982313, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751389268982527, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751389268982750, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751389268982943, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751389268983166, "dur": 69, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751389268983282, "dur": 748, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751389268984030, "dur": 477, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751389268984508, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751389268984746, "dur": 2166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751389268986913, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751389268987049, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751389268987265, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751389268987352, "dur": 530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751389268987882, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751389268988134, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751389268988397, "dur": 676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751389268989215, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751389268989600, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751389268989770, "dur": 497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751389268990318, "dur": 1638, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751389268991957, "dur": 2626, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751389268994585, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751389268994701, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751389268995032, "dur": 29823, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751389269024859, "dur": 2533, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751389269027393, "dur": 1369, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751389269028769, "dur": 2376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751389269031146, "dur": 660, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751389269031811, "dur": 2926, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751389269034738, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751389269034898, "dur": 2706, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751389269037642, "dur": 2420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751389269040108, "dur": 440, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Animation.Runtime.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751389269040667, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751389269040815, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751389269041187, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751389269041474, "dur": 378962, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751389269420454, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aEDbg.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751389269420437, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751389269420589, "dur": 1245, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751389269421838, "dur": 76609, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389268947534, "dur": 25729, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389268973265, "dur": 529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_3122CFBB2DC47329.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751389268973959, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_76128D4AF776323F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751389268974024, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_DA3F53EC74158B55.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751389268975224, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751389268975449, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389268975578, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity-editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751389268975687, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6307440961136846775.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751389268975830, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12140925242746971147.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751389268976000, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389268976207, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389268976385, "dur": 974, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389268977359, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389268977618, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389268977863, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389268978111, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389268978678, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389268978909, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389268979119, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389268979328, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389268979568, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389268979796, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389268980031, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389268980248, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389268980488, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389268980795, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389268981017, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389268981243, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389268981448, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389268982143, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389268982368, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389268982606, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389268982819, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389268983020, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389268983463, "dur": 562, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389268984025, "dur": 474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389268984502, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751389268984662, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389268984804, "dur": 683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751389268985545, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751389268985706, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751389268986070, "dur": 353, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389268986433, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389268986571, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751389268986767, "dur": 590, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751389268987435, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751389268987625, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389268987829, "dur": 695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751389268988525, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389268988796, "dur": 347, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389268989147, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389268989199, "dur": 405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389268989604, "dur": 2348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389268991955, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751389268992060, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751389268992456, "dur": 2573, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389268995029, "dur": 29813, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389269024844, "dur": 2267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751389269027112, "dur": 332, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389269027450, "dur": 2654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751389269030135, "dur": 4668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-unity.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751389269034832, "dur": 5134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751389269039967, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389269040656, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389269040857, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389269041112, "dur": 305, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751389269041452, "dur": 794, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751389269042247, "dur": 456194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751389268947566, "dur": 25708, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751389268973276, "dur": 534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_AAD14E71D524EA60.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751389268973825, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_AAD14E71D524EA60.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751389268973892, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_B5214F7A6C172998.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751389268973975, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_D3788CF92C3C660B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751389268975423, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751389268975633, "dur": 285, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751389268976013, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751389268976228, "dur": 832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751389268977060, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751389268977714, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751389268977936, "dur": 540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751389268978476, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751389268978728, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751389268978966, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751389268979202, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751389268979446, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751389268979673, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751389268979915, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751389268980133, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751389268980369, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751389268980589, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751389268980831, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751389268981052, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751389268981359, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751389268981566, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751389268981804, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751389268982041, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751389268982286, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751389268982515, "dur": 397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751389268982912, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751389268983159, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751389268983482, "dur": 547, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751389268984029, "dur": 472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751389268984507, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751389268984751, "dur": 1454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751389268986205, "dur": 358, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751389268986572, "dur": 363, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751389268986940, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751389268987172, "dur": 935, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751389268988190, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751389268988440, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751389268988625, "dur": 665, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751389268989294, "dur": 456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751389268989779, "dur": 2180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751389268991959, "dur": 3058, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751389268995018, "dur": 29843, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751389269024862, "dur": 2554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751389269027417, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751389269027495, "dur": 3383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751389269030920, "dur": 2243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751389269033164, "dur": 1283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751389269034465, "dur": 2328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751389269036832, "dur": 2223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751389269039091, "dur": 2431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751389269041556, "dur": 456873, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268947588, "dur": 25692, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268973282, "dur": 483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_D07FFB3B2770754A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751389268973794, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_966AC74B818A5F90.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751389268973882, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_299E64FBD7A1C295.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751389268974034, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_EFD593047BD2E6A8.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751389268974571, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268975039, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1751389268975218, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1751389268975444, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268975582, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Runtime.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751389268975743, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678363927977808685.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751389268976015, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268976279, "dur": 818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268977097, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268977288, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268977540, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268977778, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268977988, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268978576, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268978809, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268979058, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268979267, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268979496, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268979735, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268979980, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268980196, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268980468, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268980685, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268980934, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268981161, "dur": 336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268981497, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268981735, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268981970, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268982216, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268982467, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268982698, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268982900, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268983183, "dur": 68, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268983287, "dur": 734, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268984021, "dur": 484, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268984513, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751389268984848, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268984977, "dur": 579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751389268985556, "dur": 547, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268986129, "dur": 562, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751389268986724, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751389268986898, "dur": 806, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751389268987705, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268987787, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751389268987990, "dur": 797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751389268988788, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268988985, "dur": 514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751389268989500, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268989595, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751389268989752, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751389268990222, "dur": 1732, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268991954, "dur": 463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268992418, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751389268992539, "dur": 385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751389268992925, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268993040, "dur": 1995, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389268995035, "dur": 29827, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389269024863, "dur": 2227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/NativeFilePicker.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751389269027091, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389269027188, "dur": 2468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751389269029657, "dur": 755, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389269030420, "dur": 2629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751389269033050, "dur": 510, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389269033568, "dur": 4011, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/NativeFilePicker.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751389269037636, "dur": 2313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751389269040025, "dur": 665, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389269041017, "dur": 437, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1751389269041455, "dur": 862, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751389269042317, "dur": 456126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268947612, "dur": 25673, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268973287, "dur": 490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_21E5C8D6C2CA360E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751389268974034, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268974402, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751389268974527, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751389268974802, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751389268975139, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1751389268975430, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268975585, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-firstpass.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1751389268975804, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4289172273399079767.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751389268976009, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268976258, "dur": 854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268977112, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268977328, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268977553, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268977781, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268978015, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268978571, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268978789, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268979050, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268979279, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268979519, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268979732, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268979951, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268980177, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268980406, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268980619, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268980832, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268981048, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268981285, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268981719, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268981958, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268982201, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268982417, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268982704, "dur": 381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268983085, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268983659, "dur": 366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268984026, "dur": 630, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268984658, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751389268984833, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268984974, "dur": 624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751389268985601, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268985815, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751389268985979, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751389268986188, "dur": 1143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751389268987331, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268987389, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751389268987564, "dur": 524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751389268988089, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268988188, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751389268988392, "dur": 768, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751389268989161, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268989353, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268989597, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751389268989692, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268989830, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751389268990164, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268990321, "dur": 1640, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268991961, "dur": 3061, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389268995022, "dur": 29825, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389269024851, "dur": 2242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751389269027129, "dur": 2336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751389269029466, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389269029629, "dur": 2452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualEffectGraph.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751389269032082, "dur": 2046, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389269034134, "dur": 2406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751389269036595, "dur": 2245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751389269038880, "dur": 2395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751389269041275, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751389269041495, "dur": 456931, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389268947625, "dur": 25666, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389268973293, "dur": 424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_D5DC31B4B0DA901E.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751389268973872, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_A1C14A714FD44069.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751389268974036, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_8AA30A6C8DDCA8A3.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751389268974454, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751389268974617, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389268974699, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1751389268974836, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751389268975133, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1751389268975263, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751389268975440, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389268976020, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389268976246, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389268977083, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389268977283, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389268977531, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389268977747, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389268977977, "dur": 596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389268978574, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389268978795, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389268979008, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389268979228, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389268979468, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389268979693, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389268979935, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389268980159, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389268980457, "dur": 495, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389268980952, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389268981197, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389268981422, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389268981653, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389268981860, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389268982096, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389268982352, "dur": 261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389268982613, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389268982830, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389268983023, "dur": 632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389268983656, "dur": 357, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389268984035, "dur": 625, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389268984661, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751389268984883, "dur": 343, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389268985231, "dur": 550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751389268985781, "dur": 1121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389268986907, "dur": 878, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751389268987786, "dur": 435, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389268988225, "dur": 735, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity-editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751389268988961, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389268989191, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389268989608, "dur": 2347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389268991956, "dur": 769, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389268992726, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751389268992899, "dur": 355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751389268993292, "dur": 1728, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389268995021, "dur": 32440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389269027462, "dur": 2042, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751389269029550, "dur": 2411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Assembly-CSharp-firstpass.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751389269031962, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389269032036, "dur": 2192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751389269034262, "dur": 2221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751389269036484, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389269036609, "dur": 4218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751389269040827, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389269041141, "dur": 280, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PerformanceTesting.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1751389269041446, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751389269041562, "dur": 456866, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389268947645, "dur": 25653, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389268973301, "dur": 411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_E4B698B849B24AEB.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751389268974040, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389268974398, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_9FD2F8E91B7883F2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751389268974949, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751389268975136, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/spine-unity.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1751389268975217, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.PixelPerfect.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1751389268975390, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389268976028, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389268976257, "dur": 813, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389268977071, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389268977270, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389268977483, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389268977710, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389268977955, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389268978631, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389268978872, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389268979089, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389268979316, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389268979571, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389268979798, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389268980020, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389268980233, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389268980499, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389268980721, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389268980950, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389268981183, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389268981398, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389268981629, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389268981852, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389268982083, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389268982351, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389268982886, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389268983123, "dur": 511, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389268983634, "dur": 393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389268984027, "dur": 627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389268984655, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751389268984844, "dur": 566, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751389268985410, "dur": 1178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389268986593, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751389268986767, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389268986825, "dur": 690, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751389268987516, "dur": 908, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389268988447, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751389268988617, "dur": 441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751389268989059, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389268989192, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389268989600, "dur": 1830, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389268991434, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751389268991537, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751389268991842, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751389268992245, "dur": 2776, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389268995022, "dur": 29823, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389269024845, "dur": 2211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751389269027057, "dur": 447, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389269027509, "dur": 2248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751389269029758, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389269029961, "dur": 2871, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751389269032870, "dur": 3634, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751389269036505, "dur": 578, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751389269037091, "dur": 2692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/spine-unity-editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751389269039815, "dur": 2422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751389269042275, "dur": 456171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389268947666, "dur": 25640, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389268973308, "dur": 444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_BA35B04A057142E1.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751389268973901, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_60DEBCA51ABA1468.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751389268974038, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389268974127, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_099E8E7D5E4735AD.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751389268975180, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1751389268975418, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389268976037, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389268976260, "dur": 786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389268977047, "dur": 626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389268977674, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389268977906, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389268978141, "dur": 551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389268978693, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389268978925, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389268979140, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389268979386, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389268979615, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389268979826, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389268980051, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389268980262, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389268980521, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389268980746, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389268980962, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389268981191, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389268981441, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389268981675, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389268981897, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389268982143, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389268982358, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389268982597, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389268982803, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389268983014, "dur": 282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389268983296, "dur": 720, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389268984016, "dur": 496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389268984514, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751389268984810, "dur": 584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751389268985395, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389268985579, "dur": 597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751389268986176, "dur": 385, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389268986565, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751389268986771, "dur": 786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751389268987558, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389268987627, "dur": 564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751389268988192, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389268988396, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751389268988571, "dur": 812, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751389268989421, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751389268989534, "dur": 455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751389268989989, "dur": 410, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389268990401, "dur": 1549, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389268991953, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751389268992128, "dur": 547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751389268992722, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751389268992818, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751389268993109, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389268993186, "dur": 1837, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389268995024, "dur": 31449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389269026475, "dur": 2248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751389269028724, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389269029013, "dur": 2157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751389269031223, "dur": 2259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751389269033482, "dur": 405, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389269033916, "dur": 2122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751389269036081, "dur": 2559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1751389269038640, "dur": 1172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389269039862, "dur": 536, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389269040569, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389269040774, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/ScriptablePacker.dll"}}, {"pid": 12345, "tid": 13, "ts": 1751389269040860, "dur": 558, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389269041458, "dur": 136809, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389269179704, "dur": 170, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 13, "ts": 1751389269179875, "dur": 1082, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 13, "ts": 1751389269180960, "dur": 51, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 13, "ts": 1751389269178269, "dur": 2744, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751389269181014, "dur": 317428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268947682, "dur": 25629, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268973313, "dur": 486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_8AA31634E5830E2F.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751389268974031, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_A5513E9150BC6315.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751389268974426, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751389268974825, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751389268975427, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268975694, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10484060162025327574.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751389268975774, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12044039279616155683.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751389268975903, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1212262423713005172.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751389268976018, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268976272, "dur": 797, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268977070, "dur": 153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268977224, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268977426, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268977683, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268977885, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268978129, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268978683, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268978911, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268979122, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268979340, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268979579, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268979796, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268980026, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268980240, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268980483, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268980688, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268980907, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268981131, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268981353, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268981581, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268981835, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268982058, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268982300, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268982583, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268982790, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268983018, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268983282, "dur": 732, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268984015, "dur": 502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268984518, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751389268984684, "dur": 774, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268985463, "dur": 503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751389268985967, "dur": 758, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268986734, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268986906, "dur": 500, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268987412, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751389268987540, "dur": 650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751389268988191, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268988443, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751389268988611, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/ScriptablePacker.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751389268988757, "dur": 697, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/ScriptablePacker.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751389268989487, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268989600, "dur": 1691, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268991292, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751389268991442, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751389268991732, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268991814, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268991960, "dur": 3058, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389268995019, "dur": 29821, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389269024842, "dur": 2890, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751389269027733, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389269027846, "dur": 4057, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751389269031903, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389269032141, "dur": 1977, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751389269034119, "dur": 1212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389269035338, "dur": 2363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751389269037702, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389269037846, "dur": 2459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1751389269040389, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389269040471, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389269040719, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389269040889, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.PixelPerfect.Editor.dll"}}, {"pid": 12345, "tid": 14, "ts": 1751389269041448, "dur": 412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751389269041881, "dur": 456544, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389268947697, "dur": 25623, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389268973323, "dur": 422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_7D19F4582D6FA60D.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751389268974028, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_E951570DA9D21231.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751389268974449, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_B364C0B29F0D0D42.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751389268974826, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751389268974998, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751389268975441, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389268975616, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PerformanceTesting.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751389268975672, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10316626440694862930.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751389268975739, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1416584266116165248.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751389268975809, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16873770810471600196.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751389268975991, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389268976208, "dur": 628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389268976836, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389268977489, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389268977717, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389268977931, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389268978175, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389268978711, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389268979023, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389268979254, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389268979509, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389268979741, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389268979969, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389268980195, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389268980452, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389268980798, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389268981015, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389268981247, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389268981460, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389268981694, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389268981907, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389268982154, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389268982373, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389268982602, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389268982804, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389268983015, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389268983291, "dur": 735, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389268984026, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389268984503, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751389268984748, "dur": 739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751389268985487, "dur": 457, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389268985965, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751389268986196, "dur": 1553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751389268987810, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/OutlineFx.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751389268987987, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751389268988281, "dur": 743, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/NativeFilePicker.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751389268989025, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389268989123, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389268989204, "dur": 394, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389268989599, "dur": 405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389268990005, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751389268990188, "dur": 1328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751389268991579, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751389268991709, "dur": 652, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751389268992414, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751389268992516, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751389268992897, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751389268992993, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751389268993294, "dur": 1725, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389268995020, "dur": 29814, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389269024836, "dur": 2277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751389269027147, "dur": 4256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751389269031404, "dur": 631, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389269032079, "dur": 2473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751389269034584, "dur": 2396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751389269036983, "dur": 961, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389269037951, "dur": 2686, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1751389269040638, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389269040818, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389269040937, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389269041234, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389269041459, "dur": 139589, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751389269181048, "dur": 317387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389268947716, "dur": 25629, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389268973347, "dur": 412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_57E453305C48C0D1.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751389268973784, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_AB9848F6D5CE3BFE.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751389268973883, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_DAD3C4523F8649DE.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751389268974045, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389268974118, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_CE214F9E6C036FF9.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751389268975044, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/spine-csharp.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1751389268975155, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.IK.Runtime.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751389268975400, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389268975679, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10054408285822421624.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751389268975761, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14262630423516740202.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751389268976048, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389268976283, "dur": 977, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389268977261, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389268977469, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389268977705, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389268977925, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389268978170, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389268978732, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389268978969, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389268979199, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389268979428, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389268979653, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389268979886, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389268980095, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389268980325, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389268980549, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389268980800, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389268981019, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389268981283, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389268981579, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389268981822, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389268982043, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389268982276, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389268982487, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389268982733, "dur": 481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389268983214, "dur": 74, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389268983288, "dur": 739, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389268984028, "dur": 516, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389268984545, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751389268984748, "dur": 897, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389268985648, "dur": 756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751389268986465, "dur": 512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751389268986978, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389268987159, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751389268987361, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389268987532, "dur": 690, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751389268988222, "dur": 330, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389268988598, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751389268988797, "dur": 528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751389268989374, "dur": 219, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389268989594, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751389268989729, "dur": 407, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389268990140, "dur": 355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751389268990555, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751389268990708, "dur": 667, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751389268991427, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751389268991543, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751389268991951, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751389268992066, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751389268992529, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751389268992648, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751389268993110, "dur": 1915, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389268995025, "dur": 29827, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389269024855, "dur": 2255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751389269027156, "dur": 2197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751389269029357, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389269029591, "dur": 2347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751389269031939, "dur": 1243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389269033189, "dur": 2366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751389269035556, "dur": 1184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389269036744, "dur": 2378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751389269039123, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751389269039377, "dur": 2787, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1300b0aEDbg.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1751389269042224, "dur": 456209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751389269527508, "dur": 1180, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 2364, "tid": 2188, "ts": 1751389269548703, "dur": 7428, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 2364, "tid": 2188, "ts": 1751389269556430, "dur": 1799, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 2364, "tid": 2188, "ts": 1751389269539968, "dur": 19065, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}