Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.36f1 (9fe3b5f71dbb) revision 10478517'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Professional' Language: 'pt' Physical Memory: 16221 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.36f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
D:/Menino Autista/DKG-RPG-Mobile
-logFile
Logs/AssetImportWorker1.log
-srvPort
49859
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: D:/Menino Autista/DKG-RPG-Mobile
D:/Menino Autista/DKG-RPG-Mobile
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [18428]  Target information:

Player connection [18428]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1539340158 [EditorId] 1539340158 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-J26I52U) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [18428] Host joined multi-casting on [***********:54997]...
Player connection [18428] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1.84 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.36f1 (9fe3b5f71dbb)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/Menino Autista/DKG-RPG-Mobile/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1050 Ti (ID=0x1c82)
    Vendor:   NVIDIA
    VRAM:     4004 MB
    Driver:   32.0.15.6094
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56468
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Registered in 0.003094 seconds.
- Loaded All Assemblies, in  0.330 seconds
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 172 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.523 seconds
Domain Reload Profiling: 851ms
	BeginReloadAssembly (108ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (35ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (135ms)
		LoadAssemblies (108ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (132ms)
			TypeCache.Refresh (131ms)
				TypeCache.ScanAssembly (119ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (523ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (476ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (273ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (53ms)
			ProcessInitializeOnLoadAttributes (97ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.013 seconds
Refreshing native plugins compatible for Editor in 0.94 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.026 seconds
Domain Reload Profiling: 2037ms
	BeginReloadAssembly (159ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (27ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (41ms)
	LoadAllAssembliesAndSetupDomain (767ms)
		LoadAssemblies (429ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (432ms)
			TypeCache.Refresh (357ms)
				TypeCache.ScanAssembly (336ms)
			BuildScriptInfoCaches (61ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (1027ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (865ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (14ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (231ms)
			ProcessInitializeOnLoadAttributes (492ms)
			ProcessInitializeOnLoadMethodAttributes (117ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (4ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 1.54 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 324 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7563 unused Assets / (9.9 MB). Loaded Objects now: 8361.
Memory consumption went from 234.3 MB to 224.3 MB.
Total: 10.523800 ms (FindLiveObjects: 0.869200 ms CreateObjectMapping: 1.229600 ms MarkObjects: 4.271600 ms  DeleteObjects: 4.152400 ms)

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.854 seconds
Refreshing native plugins compatible for Editor in 1.55 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.957 seconds
Domain Reload Profiling: 1814ms
	BeginReloadAssembly (191ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (49ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (601ms)
		LoadAssemblies (439ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (247ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (215ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (958ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (752ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (14ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (171ms)
			ProcessInitializeOnLoadAttributes (480ms)
			ProcessInitializeOnLoadMethodAttributes (76ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 0.98 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.4 MB). Loaded Objects now: 8364.
Memory consumption went from 184.0 MB to 173.6 MB.
Total: 10.015700 ms (FindLiveObjects: 0.690300 ms CreateObjectMapping: 0.586900 ms MarkObjects: 3.811300 ms  DeleteObjects: 4.926200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 512.673197 seconds.
  path: Assets/Resources/Scripts/Configs/ImportJsonFile.cs
  artifactKey: Guid(ab6799b1f164de845bef2c0548a11f45) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Scripts/Configs/ImportJsonFile.cs using Guid(ab6799b1f164de845bef2c0548a11f45) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2e480a3ac28166a98fb0917e12d888f6') in 0.0071904 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in 13.307 seconds
Refreshing native plugins compatible for Editor in 1.02 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.867 seconds
Domain Reload Profiling: 14178ms
	BeginReloadAssembly (846ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (73ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (117ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (12391ms)
		LoadAssemblies (12433ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (516ms)
			TypeCache.Refresh (51ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (449ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (868ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (653ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (14ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (177ms)
			ProcessInitializeOnLoadAttributes (393ms)
			ProcessInitializeOnLoadMethodAttributes (58ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 1.81 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.2 MB). Loaded Objects now: 8368.
Memory consumption went from 186.4 MB to 176.2 MB.
Total: 11.757800 ms (FindLiveObjects: 0.719500 ms CreateObjectMapping: 0.636100 ms MarkObjects: 5.886700 ms  DeleteObjects: 4.513000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.841 seconds
Refreshing native plugins compatible for Editor in 1.11 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.784 seconds
Domain Reload Profiling: 1627ms
	BeginReloadAssembly (200ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (15ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (49ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (572ms)
		LoadAssemblies (459ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (209ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (188ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (785ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (593ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (188ms)
			ProcessInitializeOnLoadAttributes (330ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 0.94 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.1 MB). Loaded Objects now: 8370.
Memory consumption went from 188.3 MB to 178.2 MB.
Total: 9.215900 ms (FindLiveObjects: 0.671000 ms CreateObjectMapping: 0.673400 ms MarkObjects: 3.650300 ms  DeleteObjects: 4.219600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  2.796 seconds
Refreshing native plugins compatible for Editor in 0.92 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.849 seconds
Domain Reload Profiling: 3649ms
	BeginReloadAssembly (780ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (124ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (151ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (1954ms)
		LoadAssemblies (2120ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (232ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (199ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (849ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (653ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (162ms)
			ProcessInitializeOnLoadAttributes (409ms)
			ProcessInitializeOnLoadMethodAttributes (58ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 1.10 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.1 MB). Loaded Objects now: 8372.
Memory consumption went from 188.3 MB to 178.2 MB.
Total: 11.346700 ms (FindLiveObjects: 0.762800 ms CreateObjectMapping: 0.696500 ms MarkObjects: 4.877100 ms  DeleteObjects: 5.008200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.761 seconds
Refreshing native plugins compatible for Editor in 0.88 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.803 seconds
Domain Reload Profiling: 1566ms
	BeginReloadAssembly (172ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (43ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (524ms)
		LoadAssemblies (404ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (198ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (177ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (804ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (614ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (168ms)
			ProcessInitializeOnLoadAttributes (359ms)
			ProcessInitializeOnLoadMethodAttributes (65ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 0.96 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.0 MB). Loaded Objects now: 8374.
Memory consumption went from 188.4 MB to 178.3 MB.
Total: 8.577900 ms (FindLiveObjects: 0.638700 ms CreateObjectMapping: 0.555700 ms MarkObjects: 3.735200 ms  DeleteObjects: 3.647500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.28 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7549 unused Assets / (11.7 MB). Loaded Objects now: 8374.
Memory consumption went from 188.5 MB to 176.7 MB.
Total: 54.990800 ms (FindLiveObjects: 0.992000 ms CreateObjectMapping: 0.723800 ms MarkObjects: 46.930600 ms  DeleteObjects: 6.341800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.985 seconds
Refreshing native plugins compatible for Editor in 1.55 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.809 seconds
Domain Reload Profiling: 1797ms
	BeginReloadAssembly (222ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (20ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (71ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (702ms)
		LoadAssemblies (573ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (219ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (189ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (810ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (593ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (15ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (157ms)
			ProcessInitializeOnLoadAttributes (353ms)
			ProcessInitializeOnLoadMethodAttributes (58ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 0.96 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (9.9 MB). Loaded Objects now: 8376.
Memory consumption went from 188.4 MB to 178.4 MB.
Total: 8.387400 ms (FindLiveObjects: 0.621400 ms CreateObjectMapping: 0.559700 ms MarkObjects: 3.817200 ms  DeleteObjects: 3.388500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.731 seconds
Refreshing native plugins compatible for Editor in 1.14 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.799 seconds
Domain Reload Profiling: 1531ms
	BeginReloadAssembly (178ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (43ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (493ms)
		LoadAssemblies (393ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (179ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (159ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (800ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (575ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (151ms)
			ProcessInitializeOnLoadAttributes (351ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.22 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.0 MB). Loaded Objects now: 8378.
Memory consumption went from 188.4 MB to 178.4 MB.
Total: 9.059700 ms (FindLiveObjects: 0.718400 ms CreateObjectMapping: 0.628100 ms MarkObjects: 3.809900 ms  DeleteObjects: 3.902700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.27 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7549 unused Assets / (10.4 MB). Loaded Objects now: 8378.
Memory consumption went from 188.7 MB to 178.3 MB.
Total: 11.965500 ms (FindLiveObjects: 0.828800 ms CreateObjectMapping: 0.702300 ms MarkObjects: 6.190300 ms  DeleteObjects: 4.242800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.771 seconds
Refreshing native plugins compatible for Editor in 0.97 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.786 seconds
Domain Reload Profiling: 1560ms
	BeginReloadAssembly (188ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (43ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (514ms)
		LoadAssemblies (403ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (203ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (172ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (786ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (600ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (167ms)
			ProcessInitializeOnLoadAttributes (348ms)
			ProcessInitializeOnLoadMethodAttributes (63ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.01 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.2 MB). Loaded Objects now: 8380.
Memory consumption went from 188.5 MB to 178.4 MB.
Total: 9.711500 ms (FindLiveObjects: 0.626700 ms CreateObjectMapping: 0.894800 ms MarkObjects: 4.003400 ms  DeleteObjects: 4.184000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.707 seconds
Refreshing native plugins compatible for Editor in 1.21 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.787 seconds
Domain Reload Profiling: 1496ms
	BeginReloadAssembly (173ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (44ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (476ms)
		LoadAssemblies (380ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (176ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (156ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (788ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (609ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (154ms)
			ProcessInitializeOnLoadAttributes (378ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.01 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.0 MB). Loaded Objects now: 8382.
Memory consumption went from 188.5 MB to 178.5 MB.
Total: 8.412300 ms (FindLiveObjects: 0.647400 ms CreateObjectMapping: 0.534200 ms MarkObjects: 3.704200 ms  DeleteObjects: 3.525900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.728 seconds
Refreshing native plugins compatible for Editor in 0.98 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.757 seconds
Domain Reload Profiling: 1486ms
	BeginReloadAssembly (169ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (42ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (499ms)
		LoadAssemblies (394ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (182ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (162ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (757ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (564ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (148ms)
			ProcessInitializeOnLoadAttributes (344ms)
			ProcessInitializeOnLoadMethodAttributes (51ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.57 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.1 MB). Loaded Objects now: 8384.
Memory consumption went from 188.6 MB to 178.5 MB.
Total: 9.072700 ms (FindLiveObjects: 0.744300 ms CreateObjectMapping: 0.705200 ms MarkObjects: 4.112900 ms  DeleteObjects: 3.508900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  2.311 seconds
Refreshing native plugins compatible for Editor in 0.99 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.829 seconds
Domain Reload Profiling: 3141ms
	BeginReloadAssembly (320ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (35ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (87ms)
	RebuildCommonClasses (34ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (1920ms)
		LoadAssemblies (1793ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (270ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (244ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (830ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (643ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (160ms)
			ProcessInitializeOnLoadAttributes (397ms)
			ProcessInitializeOnLoadMethodAttributes (62ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 1.50 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.7 MB). Loaded Objects now: 8386.
Memory consumption went from 188.6 MB to 177.9 MB.
Total: 10.660300 ms (FindLiveObjects: 0.836900 ms CreateObjectMapping: 0.660000 ms MarkObjects: 4.130500 ms  DeleteObjects: 5.031100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.255 seconds
Refreshing native plugins compatible for Editor in 1.19 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.805 seconds
Domain Reload Profiling: 2062ms
	BeginReloadAssembly (216ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (19ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (65ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (976ms)
		LoadAssemblies (854ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (212ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (181ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (805ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (602ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (16ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (158ms)
			ProcessInitializeOnLoadAttributes (355ms)
			ProcessInitializeOnLoadMethodAttributes (62ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 1.00 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.4 MB). Loaded Objects now: 8388.
Memory consumption went from 188.6 MB to 178.1 MB.
Total: 9.805200 ms (FindLiveObjects: 0.849500 ms CreateObjectMapping: 0.595600 ms MarkObjects: 4.297700 ms  DeleteObjects: 4.060600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.730 seconds
Refreshing native plugins compatible for Editor in 0.92 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.767 seconds
Domain Reload Profiling: 1498ms
	BeginReloadAssembly (172ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (44ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (497ms)
		LoadAssemblies (388ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (187ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (166ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (768ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (585ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (161ms)
			ProcessInitializeOnLoadAttributes (349ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.56 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7562 unused Assets / (10.2 MB). Loaded Objects now: 8390.
Memory consumption went from 188.6 MB to 178.4 MB.
Total: 9.270900 ms (FindLiveObjects: 0.689200 ms CreateObjectMapping: 0.609500 ms MarkObjects: 3.797000 ms  DeleteObjects: 4.174200 ms)

Prepare: number of updated asset objects reloaded= 0
