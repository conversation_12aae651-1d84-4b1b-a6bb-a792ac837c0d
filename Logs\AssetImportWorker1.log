Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.36f1 (9fe3b5f71dbb) revision 10478517'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Professional' Language: 'pt' Physical Memory: 16221 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.36f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
D:/Menino Autista/DKG-RPG-Mobile
-logFile
Logs/AssetImportWorker1.log
-srvPort
62192
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: D:/Menino Autista/DKG-RPG-Mobile
D:/Menino Autista/DKG-RPG-Mobile
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [4648]  Target information:

Player connection [4648]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 361773700 [EditorId] 361773700 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-J26I52U) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [4648] Host joined multi-casting on [***********:54997]...
Player connection [4648] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1.76 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.36f1 (9fe3b5f71dbb)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/Menino Autista/DKG-RPG-Mobile/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1050 Ti (ID=0x1c82)
    Vendor:   NVIDIA
    VRAM:     4004 MB
    Driver:   32.0.15.6094
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56764
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.36f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Registered in 0.004268 seconds.
- Loaded All Assemblies, in  0.339 seconds
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 190 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.528 seconds
Domain Reload Profiling: 865ms
	BeginReloadAssembly (115ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (43ms)
	LoadAllAssembliesAndSetupDomain (138ms)
		LoadAssemblies (114ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (134ms)
			TypeCache.Refresh (133ms)
				TypeCache.ScanAssembly (122ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (528ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (481ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (285ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (44ms)
			ProcessInitializeOnLoadAttributes (95ms)
			ProcessInitializeOnLoadMethodAttributes (50ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.851 seconds
Refreshing native plugins compatible for Editor in 0.95 ms, found 4 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.837 seconds
Domain Reload Profiling: 1686ms
	BeginReloadAssembly (153ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (26ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (624ms)
		LoadAssemblies (432ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (284ms)
			TypeCache.Refresh (211ms)
				TypeCache.ScanAssembly (192ms)
			BuildScriptInfoCaches (58ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (837ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (698ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (14ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (165ms)
			ProcessInitializeOnLoadAttributes (411ms)
			ProcessInitializeOnLoadMethodAttributes (97ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.05 seconds
Refreshing native plugins compatible for Editor in 1.06 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 324 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7565 unused Assets / (10.1 MB). Loaded Objects now: 8363.
Memory consumption went from 238.2 MB to 228.1 MB.
Total: 9.464100 ms (FindLiveObjects: 0.733500 ms CreateObjectMapping: 0.634500 ms MarkObjects: 4.132000 ms  DeleteObjects: 3.956600 ms)

========================================================================
Received Import Request.
  Time since last request: 10697.421783 seconds.
  path: Assets/Resources/Scripts/Test/example_skill_values.json
  artifactKey: Guid(1943249f16b43d94db61e2990ea4797f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Scripts/Test/example_skill_values.json using Guid(1943249f16b43d94db61e2990ea4797f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '98b91debd2d039c837672be7b399313f') in 0.0212602 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.65 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7556 unused Assets / (9.9 MB). Loaded Objects now: 8371.
Memory consumption went from 185.0 MB to 175.2 MB.
Total: 104.287300 ms (FindLiveObjects: 1.064300 ms CreateObjectMapping: 0.624200 ms MarkObjects: 98.481800 ms  DeleteObjects: 4.115000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in 11.829 seconds
Refreshing native plugins compatible for Editor in 0.92 ms, found 4 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.812 seconds
Domain Reload Profiling: 12642ms
	BeginReloadAssembly (1150ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (67ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (157ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (10608ms)
		LoadAssemblies (11227ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (219ms)
			TypeCache.Refresh (37ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (168ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (812ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (591ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (153ms)
			ProcessInitializeOnLoadAttributes (363ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 0.98 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7570 unused Assets / (9.8 MB). Loaded Objects now: 8375.
Memory consumption went from 188.5 MB to 178.6 MB.
Total: 7.938900 ms (FindLiveObjects: 0.611100 ms CreateObjectMapping: 0.582200 ms MarkObjects: 3.634700 ms  DeleteObjects: 3.110100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.758 seconds
Refreshing native plugins compatible for Editor in 1.03 ms, found 4 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.816 seconds
Domain Reload Profiling: 1575ms
	BeginReloadAssembly (187ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (47ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (511ms)
		LoadAssemblies (409ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (193ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (172ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (817ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (621ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (174ms)
			ProcessInitializeOnLoadAttributes (376ms)
			ProcessInitializeOnLoadMethodAttributes (50ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.05 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7570 unused Assets / (10.2 MB). Loaded Objects now: 8377.
Memory consumption went from 188.5 MB to 178.3 MB.
Total: 9.038500 ms (FindLiveObjects: 0.656900 ms CreateObjectMapping: 0.596000 ms MarkObjects: 3.973900 ms  DeleteObjects: 3.810900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.729 seconds
Refreshing native plugins compatible for Editor in 0.95 ms, found 4 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.784 seconds
Domain Reload Profiling: 1513ms
	BeginReloadAssembly (174ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (46ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (495ms)
		LoadAssemblies (395ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (181ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (158ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (784ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (611ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (157ms)
			ProcessInitializeOnLoadAttributes (384ms)
			ProcessInitializeOnLoadMethodAttributes (49ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 0.96 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7570 unused Assets / (9.9 MB). Loaded Objects now: 8379.
Memory consumption went from 188.5 MB to 178.6 MB.
Total: 8.028700 ms (FindLiveObjects: 0.622400 ms CreateObjectMapping: 0.532100 ms MarkObjects: 3.533400 ms  DeleteObjects: 3.340100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.35 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7558 unused Assets / (10.1 MB). Loaded Objects now: 8380.
Memory consumption went from 188.6 MB to 178.6 MB.
Total: 26.023700 ms (FindLiveObjects: 0.843500 ms CreateObjectMapping: 0.732200 ms MarkObjects: 19.520200 ms  DeleteObjects: 4.915200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.926 seconds
Refreshing native plugins compatible for Editor in 1.11 ms, found 4 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.816 seconds
Domain Reload Profiling: 1744ms
	BeginReloadAssembly (233ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (22ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (75ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (630ms)
		LoadAssemblies (518ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (204ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (176ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (817ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (614ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (160ms)
			ProcessInitializeOnLoadAttributes (379ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.16 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7571 unused Assets / (10.1 MB). Loaded Objects now: 8382.
Memory consumption went from 188.5 MB to 178.4 MB.
Total: 9.108200 ms (FindLiveObjects: 0.642300 ms CreateObjectMapping: 0.587700 ms MarkObjects: 3.921200 ms  DeleteObjects: 3.955200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.741 seconds
Refreshing native plugins compatible for Editor in 1.27 ms, found 4 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.761 seconds
Domain Reload Profiling: 1503ms
	BeginReloadAssembly (177ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (46ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (502ms)
		LoadAssemblies (402ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (184ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (163ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (762ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (583ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (155ms)
			ProcessInitializeOnLoadAttributes (352ms)
			ProcessInitializeOnLoadMethodAttributes (55ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 0.96 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 41 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7571 unused Assets / (9.9 MB). Loaded Objects now: 8384.
Memory consumption went from 188.5 MB to 178.5 MB.
Total: 8.472500 ms (FindLiveObjects: 0.631800 ms CreateObjectMapping: 0.555900 ms MarkObjects: 3.626400 ms  DeleteObjects: 3.657900 ms)

Prepare: number of updated asset objects reloaded= 0
