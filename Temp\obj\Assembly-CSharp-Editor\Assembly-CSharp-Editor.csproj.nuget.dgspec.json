{"format": 1, "restore": {"d:\\Menino Autista\\DKG-RPG-Mobile\\Assembly-CSharp-Editor.csproj": {}}, "projects": {"d:\\Menino Autista\\DKG-RPG-Mobile\\Assembly-CSharp-Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "d:\\Menino Autista\\DKG-RPG-Mobile\\Assembly-CSharp-Editor.csproj", "projectName": "Assembly-CSharp-Editor", "projectPath": "d:\\Menino Autista\\DKG-RPG-Mobile\\Assembly-CSharp-Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "d:\\Menino Autista\\DKG-RPG-Mobile\\Temp\\obj\\Assembly-CSharp-Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"d:\\Menino Autista\\DKG-RPG-Mobile\\Assembly-CSharp-firstpass.csproj": {"projectPath": "d:\\<PERSON>ino Autista\\DKG-RPG-Mobile\\Assembly-CSharp-firstpass.csproj"}, "d:\\Menino Autista\\DKG-RPG-Mobile\\Assembly-CSharp.csproj": {"projectPath": "d:\\Menino Autista\\DKG-RPG-Mobile\\Assembly-CSharp.csproj"}, "d:\\Menino Autista\\DKG-RPG-Mobile\\NativeFilePicker.Editor.csproj": {"projectPath": "d:\\Menino Autista\\DKG-RPG-Mobile\\NativeFilePicker.Editor.csproj"}, "d:\\Menino Autista\\DKG-RPG-Mobile\\NativeFilePicker.Runtime.csproj": {"projectPath": "d:\\Menino Autista\\DKG-RPG-Mobile\\NativeFilePicker.Runtime.csproj"}, "d:\\Menino Autista\\DKG-RPG-Mobile\\OutlineFx.csproj": {"projectPath": "d:\\Menino Autista\\DKG-RPG-Mobile\\OutlineFx.csproj"}, "d:\\Menino Autista\\DKG-RPG-Mobile\\OutlineFx.Editor.csproj": {"projectPath": "d:\\<PERSON>ino Autista\\DKG-RPG-Mobile\\OutlineFx.Editor.csproj"}, "d:\\Menino Autista\\DKG-RPG-Mobile\\spine-csharp.csproj": {"projectPath": "d:\\<PERSON>ino Autista\\DKG-RPG-Mobile\\spine-csharp.csproj"}, "d:\\Menino Autista\\DKG-RPG-Mobile\\spine-unity-editor.csproj": {"projectPath": "d:\\<PERSON>ino Autista\\DKG-RPG-Mobile\\spine-unity-editor.csproj"}, "d:\\Menino Autista\\DKG-RPG-Mobile\\spine-unity.csproj": {"projectPath": "d:\\<PERSON>ino Autista\\DKG-RPG-Mobile\\spine-unity.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "d:\\Menino Autista\\DKG-RPG-Mobile\\Assembly-CSharp-firstpass.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "d:\\<PERSON>ino Autista\\DKG-RPG-Mobile\\Assembly-CSharp-firstpass.csproj", "projectName": "Assembly-CSharp-firstpass", "projectPath": "d:\\<PERSON>ino Autista\\DKG-RPG-Mobile\\Assembly-CSharp-firstpass.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "d:\\Menino Autista\\DKG-RPG-Mobile\\Temp\\obj\\Assembly-CSharp-firstpass\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"d:\\Menino Autista\\DKG-RPG-Mobile\\NativeFilePicker.Editor.csproj": {"projectPath": "d:\\Menino Autista\\DKG-RPG-Mobile\\NativeFilePicker.Editor.csproj"}, "d:\\Menino Autista\\DKG-RPG-Mobile\\NativeFilePicker.Runtime.csproj": {"projectPath": "d:\\Menino Autista\\DKG-RPG-Mobile\\NativeFilePicker.Runtime.csproj"}, "d:\\Menino Autista\\DKG-RPG-Mobile\\OutlineFx.csproj": {"projectPath": "d:\\Menino Autista\\DKG-RPG-Mobile\\OutlineFx.csproj"}, "d:\\Menino Autista\\DKG-RPG-Mobile\\OutlineFx.Editor.csproj": {"projectPath": "d:\\<PERSON>ino Autista\\DKG-RPG-Mobile\\OutlineFx.Editor.csproj"}, "d:\\Menino Autista\\DKG-RPG-Mobile\\spine-csharp.csproj": {"projectPath": "d:\\<PERSON>ino Autista\\DKG-RPG-Mobile\\spine-csharp.csproj"}, "d:\\Menino Autista\\DKG-RPG-Mobile\\spine-unity-editor.csproj": {"projectPath": "d:\\<PERSON>ino Autista\\DKG-RPG-Mobile\\spine-unity-editor.csproj"}, "d:\\Menino Autista\\DKG-RPG-Mobile\\spine-unity.csproj": {"projectPath": "d:\\<PERSON>ino Autista\\DKG-RPG-Mobile\\spine-unity.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "d:\\Menino Autista\\DKG-RPG-Mobile\\Assembly-CSharp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "d:\\Menino Autista\\DKG-RPG-Mobile\\Assembly-CSharp.csproj", "projectName": "Assembly-CSharp", "projectPath": "d:\\Menino Autista\\DKG-RPG-Mobile\\Assembly-CSharp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "d:\\Menino Autista\\DKG-RPG-Mobile\\Temp\\obj\\Assembly-CSharp\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"d:\\Menino Autista\\DKG-RPG-Mobile\\Assembly-CSharp-firstpass.csproj": {"projectPath": "d:\\<PERSON>ino Autista\\DKG-RPG-Mobile\\Assembly-CSharp-firstpass.csproj"}, "d:\\Menino Autista\\DKG-RPG-Mobile\\NativeFilePicker.Editor.csproj": {"projectPath": "d:\\Menino Autista\\DKG-RPG-Mobile\\NativeFilePicker.Editor.csproj"}, "d:\\Menino Autista\\DKG-RPG-Mobile\\NativeFilePicker.Runtime.csproj": {"projectPath": "d:\\Menino Autista\\DKG-RPG-Mobile\\NativeFilePicker.Runtime.csproj"}, "d:\\Menino Autista\\DKG-RPG-Mobile\\OutlineFx.csproj": {"projectPath": "d:\\Menino Autista\\DKG-RPG-Mobile\\OutlineFx.csproj"}, "d:\\Menino Autista\\DKG-RPG-Mobile\\OutlineFx.Editor.csproj": {"projectPath": "d:\\<PERSON>ino Autista\\DKG-RPG-Mobile\\OutlineFx.Editor.csproj"}, "d:\\Menino Autista\\DKG-RPG-Mobile\\spine-csharp.csproj": {"projectPath": "d:\\<PERSON>ino Autista\\DKG-RPG-Mobile\\spine-csharp.csproj"}, "d:\\Menino Autista\\DKG-RPG-Mobile\\spine-unity-editor.csproj": {"projectPath": "d:\\<PERSON>ino Autista\\DKG-RPG-Mobile\\spine-unity-editor.csproj"}, "d:\\Menino Autista\\DKG-RPG-Mobile\\spine-unity.csproj": {"projectPath": "d:\\<PERSON>ino Autista\\DKG-RPG-Mobile\\spine-unity.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "d:\\Menino Autista\\DKG-RPG-Mobile\\NativeFilePicker.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "d:\\Menino Autista\\DKG-RPG-Mobile\\NativeFilePicker.Editor.csproj", "projectName": "NativeFilePicker.Editor", "projectPath": "d:\\Menino Autista\\DKG-RPG-Mobile\\NativeFilePicker.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "d:\\Menino Autista\\DKG-RPG-Mobile\\Temp\\obj\\NativeFilePicker.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "d:\\Menino Autista\\DKG-RPG-Mobile\\NativeFilePicker.Runtime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "d:\\Menino Autista\\DKG-RPG-Mobile\\NativeFilePicker.Runtime.csproj", "projectName": "NativeFilePicker.Runtime", "projectPath": "d:\\Menino Autista\\DKG-RPG-Mobile\\NativeFilePicker.Runtime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "d:\\Menino Autista\\DKG-RPG-Mobile\\Temp\\obj\\NativeFilePicker.Runtime\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "d:\\Menino Autista\\DKG-RPG-Mobile\\OutlineFx.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "d:\\Menino Autista\\DKG-RPG-Mobile\\OutlineFx.csproj", "projectName": "OutlineFx", "projectPath": "d:\\Menino Autista\\DKG-RPG-Mobile\\OutlineFx.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "d:\\Menino Autista\\DKG-RPG-Mobile\\Temp\\obj\\OutlineFx\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "d:\\Menino Autista\\DKG-RPG-Mobile\\OutlineFx.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "d:\\<PERSON>ino Autista\\DKG-RPG-Mobile\\OutlineFx.Editor.csproj", "projectName": "OutlineFx.Editor", "projectPath": "d:\\<PERSON>ino Autista\\DKG-RPG-Mobile\\OutlineFx.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "d:\\Menino Autista\\DKG-RPG-Mobile\\Temp\\obj\\OutlineFx.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"d:\\Menino Autista\\DKG-RPG-Mobile\\OutlineFx.csproj": {"projectPath": "d:\\Menino Autista\\DKG-RPG-Mobile\\OutlineFx.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "d:\\Menino Autista\\DKG-RPG-Mobile\\spine-csharp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "d:\\<PERSON>ino Autista\\DKG-RPG-Mobile\\spine-csharp.csproj", "projectName": "spine-csharp", "projectPath": "d:\\<PERSON>ino Autista\\DKG-RPG-Mobile\\spine-csharp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "d:\\Menino Autista\\DKG-RPG-Mobile\\Temp\\obj\\spine-csharp\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "d:\\Menino Autista\\DKG-RPG-Mobile\\spine-unity-editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "d:\\<PERSON>ino Autista\\DKG-RPG-Mobile\\spine-unity-editor.csproj", "projectName": "spine-unity-editor", "projectPath": "d:\\<PERSON>ino Autista\\DKG-RPG-Mobile\\spine-unity-editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "d:\\<PERSON>ino Autista\\DKG-RPG-Mobile\\Temp\\obj\\spine-unity-editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"d:\\Menino Autista\\DKG-RPG-Mobile\\spine-csharp.csproj": {"projectPath": "d:\\<PERSON>ino Autista\\DKG-RPG-Mobile\\spine-csharp.csproj"}, "d:\\Menino Autista\\DKG-RPG-Mobile\\spine-unity.csproj": {"projectPath": "d:\\<PERSON>ino Autista\\DKG-RPG-Mobile\\spine-unity.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "d:\\Menino Autista\\DKG-RPG-Mobile\\spine-unity.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "d:\\<PERSON>ino Autista\\DKG-RPG-Mobile\\spine-unity.csproj", "projectName": "spine-unity", "projectPath": "d:\\<PERSON>ino Autista\\DKG-RPG-Mobile\\spine-unity.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "d:\\Menino Autista\\DKG-RPG-Mobile\\Temp\\obj\\spine-unity\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"d:\\Menino Autista\\DKG-RPG-Mobile\\spine-csharp.csproj": {"projectPath": "d:\\<PERSON>ino Autista\\DKG-RPG-Mobile\\spine-csharp.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}}}