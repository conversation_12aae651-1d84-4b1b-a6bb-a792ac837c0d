using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Debug helper for testing virtualization issues
/// </summary>
public class VirtualizationDebugger : MonoBehaviour
{
    [Header("References")]
    public VirtualizedCharacterList virtualizedList;
    public ScrollRect scrollRect;
    public RectTransform content;
    
    [Header("Debug Controls")]
    [SerializeField] private bool enableDebugLogging = true;
    
    [Header("Manual Test Controls")]
    [Range(0, 100)]
    public int testScrollToIndex = 0;
    
    void Start()
    {
        // Auto-find components if not assigned
        if (virtualizedList == null)
            virtualizedList = FindObjectOfType<VirtualizedCharacterList>();
            
        if (scrollRect == null)
            scrollRect = FindObjectOfType<ScrollRect>();
            
        if (content == null && scrollRect != null)
            content = scrollRect.content;
    }
    
    void Update()
    {
        if (enableDebugLogging && Input.GetKeyDown(KeyCode.D))
        {
            DebugVirtualizationState();
        }
        
        if (Input.GetKeyDown(KeyCode.T))
        {
            TestScrollToIndex();
        }
        
        if (Input.GetKeyDown(KeyCode.R))
        {
            TestForceRefresh();
        }
    }
    
    [ContextMenu("Debug Virtualization State")]
    public void DebugVirtualizationState()
    {
        Debug.Log("=== VIRTUALIZATION DEBUG STATE ===");
        
        if (virtualizedList != null)
        {
            Debug.Log($"VirtualizedList: {virtualizedList.name}");
            Debug.Log($"Filtered Characters: {virtualizedList.GetFilteredCharacterCount()}");
            Debug.Log($"Visible Items: {virtualizedList.GetVisibleItemCount()}");
        }
        else
        {
            Debug.LogError("VirtualizedCharacterList not found!");
        }
        
        if (scrollRect != null)
        {
            Debug.Log($"ScrollRect: {scrollRect.name}");
            Debug.Log($"ScrollRect.normalizedPosition: {scrollRect.normalizedPosition}");
            Debug.Log($"ScrollRect.velocity: {scrollRect.velocity}");
        }
        else
        {
            Debug.LogError("ScrollRect not found!");
        }
        
        if (content != null)
        {
            Debug.Log($"Content: {content.name}");
            Debug.Log($"Content.anchoredPosition: {content.anchoredPosition}");
            Debug.Log($"Content.sizeDelta: {content.sizeDelta}");
            Debug.Log($"Content.rect: {content.rect}");
        }
        else
        {
            Debug.LogError("Content not found!");
        }
        
        Debug.Log("=== END DEBUG STATE ===");
    }
    
    [ContextMenu("Test Scroll To Index")]
    public void TestScrollToIndex()
    {
        if (virtualizedList != null)
        {
            Debug.Log($"Testing scroll to index: {testScrollToIndex}");
            virtualizedList.ScrollToIndex(testScrollToIndex);
        }
    }
    
    [ContextMenu("Test Force Refresh")]
    public void TestForceRefresh()
    {
        if (virtualizedList != null)
        {
            Debug.Log("Testing force refresh...");
            virtualizedList.ForceRefresh();
        }
    }
    
    [ContextMenu("Test Manual Scroll Update")]
    public void TestManualScrollUpdate()
    {
        if (virtualizedList != null)
        {
            Debug.Log("Testing manual scroll update...");
            virtualizedList.TestScrollUpdate();
        }
    }
    
    [ContextMenu("Simulate Scroll Events")]
    public void SimulateScrollEvents()
    {
        if (content != null)
        {
            Debug.Log("Simulating scroll events...");
            
            // Test different scroll positions
            Vector2[] testPositions = {
                new Vector2(0, 0),      // Top
                new Vector2(0, 500),    // Middle
                new Vector2(0, 1000),   // Further down
                new Vector2(0, 2000)    // Far down
            };
            
            foreach (var pos in testPositions)
            {
                Debug.Log($"Setting content position to: {pos}");
                content.anchoredPosition = pos;
                
                if (virtualizedList != null)
                {
                    virtualizedList.TestScrollUpdate();
                }
                
                // Wait a frame
                break; // Only test first position for now
            }
        }
    }
}
