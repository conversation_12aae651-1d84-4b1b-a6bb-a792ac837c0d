using UnityEngine;

public class VirtualizedCharacterItem : MonoBehaviour
{
    [Header("Components")]
    public CharConfUI charConfUI;
    
    // State tracking
    private VirtualizedCharacterList parentList;
    private BattleCharacter character;
    private int _characterIndex = -1;
    private bool isActive = false;
    private ConfigsHandler configsHandler;
    
    // Properties
    public int characterIndex 
    { 
        get { return _characterIndex; } 
        private set { _characterIndex = value; } 
    }
    
    public bool IsActive 
    { 
        get { return isActive; } 
    }
    
    void Awake()
    {
        // Get the CharConfUI component
        charConfUI = GetComponent<CharConfUI>();
        if (charConfUI == null)
        {
            Debug.LogError("VirtualizedCharacterItem: CharConfUI component not found!");
        }
    }
    
    public void Initialize(VirtualizedCharacterList parentList)
    {
        this.parentList = parentList;
        
        // Ensure we have the CharConfUI component
        if (charConfUI == null)
        {
            charConfUI = GetComponent<CharConfUI>();
        }
        
        // Disable the CharConfUI Update method to prevent manual positioning
        if (charConfUI != null)
        {
            charConfUI.enabled = false;
        }
    }
    
    public void BindCharacter(BattleCharacter character, int index, ConfigsHandler handler)
    {
        this.character = character;
        this.characterIndex = index;
        this.configsHandler = handler;
        this.isActive = true;
        
        // Enable and configure the CharConfUI
        if (charConfUI != null)
        {
            charConfUI.enabled = true;
            charConfUI.character = character;
            charConfUI.useVirtualizedPositioning = true; // Disable manual positioning

            // Set the ConfigsHandler reference
            SetCharConfUIConfigsHandler(handler);

            // Initialize the CharConfUI with the character data
            InitializeCharConfUI();
        }
        
        // Update the GameObject name for debugging
        gameObject.name = $"VirtualChar_{index}_{character.name}";
    }
    
    public void UnbindCharacter()
    {
        // Preserve any important state before unbinding
        PreserveState();
        
        // Clear references
        this.character = null;
        this.characterIndex = -1;
        this.configsHandler = null;
        this.isActive = false;
        
        // Disable CharConfUI to stop its Update calls
        if (charConfUI != null)
        {
            charConfUI.enabled = false;
            charConfUI.character = null;
            charConfUI.useVirtualizedPositioning = false; // Reset positioning flag
        }
        
        // Reset GameObject name
        gameObject.name = "PooledCharacterItem";
    }
    
    void SetCharConfUIConfigsHandler(ConfigsHandler handler)
    {
        // Use reflection to set the private configsHandler field in CharConfUI
        var field = typeof(CharConfUI).GetField("configsHandler", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        
        if (field != null)
        {
            field.SetValue(charConfUI, handler);
        }
        else
        {
            Debug.LogWarning("VirtualizedCharacterItem: Could not find configsHandler field in CharConfUI");
        }
    }
    
    void InitializeCharConfUI()
    {
        if (charConfUI == null || character == null) return;
        
        // Manually call the initialization logic that would normally happen in CharConfUI.Start()
        // We need to replicate the essential parts without the positioning logic
        
        // Find and assign the UI elements (this mirrors CharConfUI.Start())
        Transform[] children = GetComponentsInChildren<Transform>();
        
        // The CharacterValues prefab has a specific structure:
        // [0] = this transform, [1] = Enemy, [2] = Name, [3] = Stats, [4] = Skills, [5] = Mods, [6] = Level, [7] = Remove
        if (children.Length >= 8)
        {
            // Set up the UI elements using reflection to access private fields
            SetCharConfUIField("enemy", children[1].gameObject);
            SetCharConfUIField("_name", children[2].gameObject);
            SetCharConfUIField("stats", children[3].gameObject);
            SetCharConfUIField("skills", children[4].gameObject);
            SetCharConfUIField("mods", children[5].gameObject);
            SetCharConfUIField("level", children[6].gameObject);
            SetCharConfUIField("rem", children[7].gameObject);
            
            // Set the father object to the content transform for positioning reference
            SetCharConfUIField("fatherObject", parentList.content.gameObject);
        }
        
        // Trigger the CharConfUI initialization
        InvokeCharConfUIMethod("InitializeUI");
    }
    
    void SetCharConfUIField(string fieldName, object value)
    {
        var field = typeof(CharConfUI).GetField(fieldName, 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        
        if (field != null)
        {
            field.SetValue(charConfUI, value);
        }
    }
    
    void InvokeCharConfUIMethod(string methodName)
    {
        var method = typeof(CharConfUI).GetMethod(methodName, 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        
        if (method != null)
        {
            method.Invoke(charConfUI, null);
        }
    }
    
    void PreserveState()
    {
        // Preserve any UI state that should persist when the item is pooled
        if (character != null && charConfUI != null)
        {
            CharacterUIStateManager.Instance.SaveCharacterState(character.id, this);
        }
    }
    
    // Custom Update method that replaces CharConfUI.Update() positioning logic
    void Update()
    {
        if (!isActive || charConfUI == null || character == null) return;
        
        // Handle character updates without the positioning logic
        // This replicates the essential parts of CharConfUI.Update() minus positioning
        
        // Update character reference if it changed
        if (configsHandler != null)
        {
            if (configsHandler.GetCharacter(character) == -1)
            {
                character = configsHandler.GetCharacterByID(character.id);
                charConfUI.character = character;
            }
        }
        
        // Update enemy button state (this is from the original CharConfUI.Update())
        var enemyField = typeof(CharConfUI).GetField("enemy", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        
        if (enemyField != null)
        {
            GameObject enemy = (GameObject)enemyField.GetValue(charConfUI);
            if (enemy != null && character != null)
            {
                enemy.transform.GetChild(0).gameObject.SetActive(character.isEnemy);
            }
        }
        
        // Check if character was deleted
        if (character == null)
        {
            // Notify parent list that this character was removed
            if (parentList != null)
            {
                parentList.RefreshCharacterList();
            }
        }
    }
    
    // Public interface
    public BattleCharacter GetCharacter()
    {
        return character;
    }
    
    public CharConfUI GetCharConfUI()
    {
        return charConfUI;
    }
    
    public void RefreshCharacterData()
    {
        if (isActive && character != null && configsHandler != null)
        {
            // Refresh the character data from the ConfigsHandler
            character = configsHandler.GetCharacterByID(character.id);
            if (charConfUI != null)
            {
                charConfUI.character = character;
            }
        }
    }
    
    // Handle character removal
    public void OnCharacterRemoved()
    {
        if (parentList != null)
        {
            parentList.RefreshCharacterList();
        }
    }
    
    // Position management (called by VirtualizedCharacterList)
    public void SetPosition(Vector2 position)
    {
        RectTransform rectTransform = GetComponent<RectTransform>();
        if (rectTransform != null)
        {
            rectTransform.anchoredPosition = position;
        }
    }
    
    // Debugging
    void OnValidate()
    {
        if (charConfUI == null)
        {
            charConfUI = GetComponent<CharConfUI>();
        }
    }
}
