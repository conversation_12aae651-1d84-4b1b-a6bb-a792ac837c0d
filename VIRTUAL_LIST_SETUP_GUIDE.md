# Virtual Character List Setup Guide

## Overview

This guide explains how to set up the virtual character list system in your Unity scene to replace the legacy character UI creation that was causing performance issues with 618 characters.

## Expected Performance Improvements

- **90% reduction in UI GameObject count** (from 618 to ~12 visible elements)
- **80% reduction in memory usage** for character UI
- **95% faster UI creation** (no individual GameObject instantiation)
- **Smooth scrolling performance** regardless of character count
- **Cached filtering** eliminates per-frame LINQ operations

## Scene Setup Instructions

### 1. Create Virtual Character List GameObject

1. **Create main container:**
   ```
   GameObject: "VirtualCharacterListContainer"
   - Add Component: VirtualCharacterListManager
   ```

2. **Create scroll view structure:**
   ```
   VirtualCharacterListContainer/
   ├── ScrollView (ScrollRect component)
   │   ├── Viewport (RectTransform + Mask + Image)
   │   │   └── Content (RectTransform)
   │   └── Scrollbar Vertical (optional)
   ```

3. **Add VirtualCharacterList component to ScrollView:**
   ```
   ScrollView GameObject:
   - Add Component: VirtualCharacterList
   - Configure settings in inspector
   ```

### 2. Configure VirtualCharacterList Component

**Required Settings:**
- **Scroll Rect**: Assign the ScrollRect component
- **Content**: Assign the Content RectTransform
- **Viewport**: Assign the Viewport RectTransform  
- **Character UI Prefab**: Assign "Prefabs/CharacterValues"

**Layout Settings:**
- **Item Height**: 100f (adjust based on your character UI height)
- **Item Spacing**: 10f
- **Visible Item Count**: 12 (number of items visible at once)
- **Buffer Item Count**: 2 (extra items above/below for smooth scrolling)

**Performance Settings:**
- **Enable Cached Filtering**: true
- **Max Pool Size**: 20 (should be > visible item count + buffer)

### 3. Configure ScrollRect Component

**ScrollRect Settings:**
- **Content**: Assign Content RectTransform
- **Viewport**: Assign Viewport RectTransform
- **Movement Type**: Clamped
- **Inertia**: true (for smooth scrolling)
- **Deceleration Rate**: 0.135
- **Scroll Sensitivity**: 1
- **Horizontal**: false
- **Vertical**: true

### 4. Setup Content RectTransform

**Content RectTransform Settings:**
- **Anchor**: Top-Left (0, 1)
- **Pivot**: Top-Left (0, 1)
- **Anchored Position**: (0, 0)
- **Size Delta**: Will be set automatically by VirtualCharacterList

### 5. Configure VirtualCharacterListManager

**Manager Settings:**
- **Virtual List**: Assign VirtualCharacterList component
- **Search Input**: Assign search TMP_InputField (optional)
- **Refresh Button**: Assign refresh Button (optional)
- **Stats Text**: Assign TextMeshProUGUI for performance stats (optional)

**Integration Settings:**
- **Enable Performance Logging**: true (for debugging)
- **Stats Update Interval**: 1f (seconds)

### 6. Optional UI Elements

**Search Functionality:**
```
GameObject: "CharacterSearchInput" (TMP_InputField)
- Placeholder: "Search characters..."
- Auto-connects to VirtualCharacterListManager
```

**Refresh Button:**
```
GameObject: "RefreshCharactersButton" (Button)
- Text: "Refresh List"
- Auto-connects to VirtualCharacterListManager
```

**Performance Stats Display:**
```
GameObject: "VirtualListStatsText" (TextMeshProUGUI)
- Shows real-time performance metrics
- Auto-updates every second
```

## Code Integration

### 1. Update ConfigsHandler

The ConfigsHandler has been updated to automatically detect and use the virtual list system:

```csharp
// In ConfigsHandler.LoadCharacters()
VirtualCharacterListManager virtualListManager = 
    GameObject.Find("VirtualCharacterListManager")?.GetComponent<VirtualCharacterListManager>();

if (virtualListManager != null)
{
    // Use virtual list - no individual UI creation
    virtualListManager.RefreshCharacterList();
}
else
{
    // Fallback to legacy system
    // Individual AddCharacter() calls
}
```

### 2. Character Modification Integration

When characters are modified, the virtual list automatically updates:

```csharp
// Mark character as modified for incremental saving
configsHandler.MarkCharacterModified(character);

// Refresh virtual list display if needed
virtualListManager?.RefreshCharacter(character);
```

## Testing and Verification

### 1. Performance Test Script

Add the `VirtualListPerformanceTest` component to test performance:

```csharp
// Attach to any GameObject
VirtualListPerformanceTest testScript;

// Run test manually
testScript.RunTestManually();

// Or enable "Run Test On Start" in inspector
```

### 2. Expected Test Results

**With 618 characters:**
- Memory reduction: ~90%
- Visible UI elements: 12-15 (instead of 618)
- UI creation time: <100ms (instead of 2-5 seconds)
- Smooth scrolling at 60 FPS

### 3. Debug Information

Enable detailed logging to monitor performance:

```csharp
[VirtualCharacterList] Loaded 618 characters
[VirtualCharacterList] Displaying 12 items (indices 0-11)
[VirtualCharacterListManager] Performance: 12/618 UI elements (98.1% reduction)
```

## Troubleshooting

### Common Issues

1. **Virtual list not found:**
   - Ensure VirtualCharacterListManager GameObject exists
   - Check component is properly attached
   - Verify GameObject name matches "VirtualCharacterListManager"

2. **UI elements not displaying:**
   - Check CharacterValues prefab path
   - Verify ScrollRect and Content are properly assigned
   - Ensure Content RectTransform anchors are correct

3. **Poor scrolling performance:**
   - Reduce visible item count
   - Increase buffer item count
   - Check for expensive operations in Update()

4. **Filtering not working:**
   - Verify search input is connected
   - Check filter cache is enabled
   - Monitor filter performance in logs

### Performance Monitoring

Monitor these metrics for optimal performance:

- **Memory reduction**: Should be >85%
- **Visible items**: Should be 10-15 regardless of total characters
- **Frame rate**: Should maintain 60 FPS during scrolling
- **Filter time**: Should be <10ms per filter operation

## Migration from Legacy System

### 1. Backup Current Setup

Before implementing virtual list:
- Backup current scene
- Export character data
- Document current UI hierarchy

### 2. Gradual Migration

1. **Phase 1**: Set up virtual list alongside legacy system
2. **Phase 2**: Test with subset of characters
3. **Phase 3**: Switch ConfigsHandler to use virtual list
4. **Phase 4**: Remove legacy UI creation code

### 3. Rollback Plan

If issues occur:
- Disable VirtualCharacterListManager GameObject
- ConfigsHandler will automatically fall back to legacy system
- No data loss occurs

## Advanced Configuration

### Custom Item Heights

For variable item heights:

```csharp
// In VirtualCharacterList
public void SetItemHeight(int index, float height)
{
    // Custom implementation for variable heights
}
```

### Custom Filtering

For advanced filtering logic:

```csharp
// In VirtualCharacterList
public void ApplyCustomFilter(System.Func<BattleCharacter, bool> filterFunc)
{
    filteredCharacters = allCharacters.Where(filterFunc).ToList();
    UpdateContentSize();
    RefreshVisibleItems();
}
```

### Performance Tuning

Adjust these values based on your needs:

- **Update Frequency**: Lower for better performance
- **Buffer Item Count**: Higher for smoother scrolling
- **Max Pool Size**: Higher for less object creation
- **Cache Size**: Monitor memory usage vs performance

## Support

For issues or questions:
1. Check Unity console for error messages
2. Enable detailed logging in VirtualCharacterList
3. Run performance test to identify bottlenecks
4. Monitor memory usage in Unity Profiler
