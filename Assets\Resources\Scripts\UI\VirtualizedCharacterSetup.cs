using UnityEngine;
using UnityEngine.UI;
using TMPro;

[System.Serializable]
public class VirtualizedSetupConfiguration
{
    [Header("Performance Settings")]
    public int maxVisibleItems = 12;
    public int poolSize = 15;
    public float itemHeight = 100f;
    public float itemSpacing = 10f;
    
    [Header("Scroll Settings")]
    public float scrollSensitivity = 1.0f;
    public bool enableInertia = true;
    public float decelerationRate = 0.135f;
    
    [Header("Compatibility")]
    public bool enableLegacyScrollBehavior = true;
    public float legacyScrollBoundary = 0.45f;
    
    [Header("Debug")]
    public bool enableDebugLogging = false;
    public bool showPerformanceStats = false;
}

public class VirtualizedCharacterSetup : MonoBehaviour
{
    [Header("Setup Configuration")]
    public VirtualizedSetupConfiguration config = new VirtualizedSetupConfiguration();
    
    [Header("Required References")]
    public GameObject characterValuesPrefab;
    public Transform charactersParent; // "CharactersInfo" GameObject
    public ScrollRect scrollRect;
    
    [Header("Auto-Setup")]
    public bool autoSetupOnStart = true;
    public bool replaceExistingSystem = true;
    
    // Component references
    private VirtualizedCharacterList virtualizedList;
    private VirtualizedCharacterIntegration integration;
    private VirtualizedScrollSetup scrollSetup;
    private CharacterUIStateManager stateManager;
    private ConfigsHandler configsHandler;
    
    // Setup status
    private bool isSetupComplete = false;
    private string setupStatus = "Not Started";
    
    void Start()
    {
        if (autoSetupOnStart)
        {
            SetupVirtualizedSystem();
        }
    }
    
    [ContextMenu("Setup Virtualized System")]
    public void SetupVirtualizedSystem()
    {
        if (isSetupComplete)
        {
            Debug.LogWarning("VirtualizedCharacterSetup: System already setup!");
            return;
        }
        
        setupStatus = "Setting up...";
        
        try
        {
            // Step 1: Find required components
            if (!FindRequiredComponents())
            {
                setupStatus = "Failed - Missing Components";
                return;
            }
            
            // Step 2: Setup ScrollRect
            if (!SetupScrollRect())
            {
                setupStatus = "Failed - ScrollRect Setup";
                return;
            }
            
            // Step 3: Create virtualized components
            if (!CreateVirtualizedComponents())
            {
                setupStatus = "Failed - Component Creation";
                return;
            }
            
            // Step 4: Configure components
            if (!ConfigureComponents())
            {
                setupStatus = "Failed - Component Configuration";
                return;
            }
            
            // Step 5: Initialize system
            if (!InitializeSystem())
            {
                setupStatus = "Failed - System Initialization";
                return;
            }
            
            isSetupComplete = true;
            setupStatus = "Complete";
            
            Debug.Log("VirtualizedCharacterSetup: System setup completed successfully!");
            
            if (config.showPerformanceStats)
            {
                LogPerformanceStats();
            }
        }
        catch (System.Exception e)
        {
            setupStatus = $"Failed - Exception: {e.Message}";
            Debug.LogError($"VirtualizedCharacterSetup: Setup failed with exception: {e}");
        }
    }
    
    bool FindRequiredComponents()
    {
        // Find ConfigsHandler
        configsHandler = FindObjectOfType<ConfigsHandler>();
        if (configsHandler == null)
        {
            Debug.LogError("VirtualizedCharacterSetup: ConfigsHandler not found!");
            return false;
        }
        
        // Find or create CharactersInfo parent
        if (charactersParent == null)
        {
            GameObject parentObj = GameObject.Find("CharactersInfo");
            if (parentObj != null)
            {
                charactersParent = parentObj.transform;
            }
            else
            {
                Debug.LogError("VirtualizedCharacterSetup: CharactersInfo parent not found!");
                return false;
            }
        }
        
        // Find or load CharacterValues prefab
        if (characterValuesPrefab == null)
        {
            characterValuesPrefab = Resources.Load<GameObject>("Prefabs/CharacterValues");
            if (characterValuesPrefab == null)
            {
                Debug.LogError("VirtualizedCharacterSetup: CharacterValues prefab not found!");
                return false;
            }
        }
        
        // Find or create ScrollRect
        if (scrollRect == null)
        {
            scrollRect = charactersParent.GetComponent<ScrollRect>();
            if (scrollRect == null)
            {
                scrollRect = charactersParent.gameObject.AddComponent<ScrollRect>();
            }
        }
        
        return true;
    }
    
    bool SetupScrollRect()
    {
        if (scrollRect == null) return false;
        
        // Configure ScrollRect
        scrollRect.horizontal = false;
        scrollRect.vertical = true;
        scrollRect.movementType = ScrollRect.MovementType.Clamped;
        scrollRect.inertia = config.enableInertia;
        scrollRect.decelerationRate = config.decelerationRate;
        scrollRect.scrollSensitivity = config.scrollSensitivity;
        
        // Setup viewport if needed
        if (scrollRect.viewport == null)
        {
            CreateViewport();
        }
        
        // Setup content if needed
        if (scrollRect.content == null)
        {
            CreateContent();
        }
        
        return true;
    }
    
    void CreateViewport()
    {
        GameObject viewportObj = new GameObject("Viewport");
        viewportObj.transform.SetParent(scrollRect.transform);
        
        RectTransform viewportRect = viewportObj.AddComponent<RectTransform>();
        viewportRect.anchorMin = Vector2.zero;
        viewportRect.anchorMax = Vector2.one;
        viewportRect.sizeDelta = Vector2.zero;
        viewportRect.anchoredPosition = Vector2.zero;
        
        Mask mask = viewportObj.AddComponent<Mask>();
        mask.showMaskGraphic = false;
        
        Image maskImage = viewportObj.AddComponent<Image>();
        maskImage.color = new Color(1, 1, 1, 0.01f);
        
        scrollRect.viewport = viewportRect;
    }
    
    void CreateContent()
    {
        GameObject contentObj = new GameObject("Content");
        contentObj.transform.SetParent(scrollRect.viewport);
        
        RectTransform contentRect = contentObj.AddComponent<RectTransform>();
        contentRect.anchorMin = new Vector2(0, 1);
        contentRect.anchorMax = new Vector2(1, 1);
        contentRect.pivot = new Vector2(0.5f, 1);
        contentRect.anchoredPosition = Vector2.zero;
        
        // Add ContentSizeFitter for dynamic sizing
        ContentSizeFitter sizeFitter = contentObj.AddComponent<ContentSizeFitter>();
        sizeFitter.verticalFit = ContentSizeFitter.FitMode.PreferredSize;
        
        scrollRect.content = contentRect;
    }
    
    bool CreateVirtualizedComponents()
    {
        // Add VirtualizedCharacterList
        virtualizedList = charactersParent.gameObject.GetComponent<VirtualizedCharacterList>();
        if (virtualizedList == null)
        {
            virtualizedList = charactersParent.gameObject.AddComponent<VirtualizedCharacterList>();
        }
        
        // Add VirtualizedCharacterIntegration
        integration = charactersParent.gameObject.GetComponent<VirtualizedCharacterIntegration>();
        if (integration == null)
        {
            integration = charactersParent.gameObject.AddComponent<VirtualizedCharacterIntegration>();
        }
        
        // Add VirtualizedScrollSetup
        scrollSetup = charactersParent.gameObject.GetComponent<VirtualizedScrollSetup>();
        if (scrollSetup == null)
        {
            scrollSetup = charactersParent.gameObject.AddComponent<VirtualizedScrollSetup>();
        }
        
        // Ensure CharacterUIStateManager exists
        stateManager = CharacterUIStateManager.Instance;
        
        return virtualizedList != null && integration != null && scrollSetup != null && stateManager != null;
    }
    
    bool ConfigureComponents()
    {
        // Configure VirtualizedCharacterList
        virtualizedList.characterValuesPrefab = characterValuesPrefab;
        virtualizedList.content = scrollRect.content;
        virtualizedList.viewport = scrollRect.viewport;
        virtualizedList.maxVisibleItems = config.maxVisibleItems;
        virtualizedList.poolSize = config.poolSize;
        virtualizedList.itemHeight = config.itemHeight;
        virtualizedList.itemSpacing = config.itemSpacing;
        
        // Configure VirtualizedCharacterIntegration
        integration.useVirtualizedSystem = true;
        integration.virtualizedList = virtualizedList;
        integration.legacyCharactersParent = charactersParent;
        
        // Configure VirtualizedScrollSetup
        scrollSetup.scrollSensitivity = config.scrollSensitivity;
        scrollSetup.enableInertia = config.enableInertia;
        scrollSetup.decelerationRate = config.decelerationRate;
        scrollSetup.legacyScrollBoundary = config.legacyScrollBoundary;
        scrollSetup.EnableLegacyScrollBehavior(config.enableLegacyScrollBehavior);
        
        return true;
    }
    
    bool InitializeSystem()
    {
        // Clean up existing legacy UI if requested
        if (replaceExistingSystem)
        {
            CleanupLegacyUI();
        }
        
        // Initialize the virtualized system
        virtualizedList.InitializeVirtualizedList();
        
        return true;
    }
    
    void CleanupLegacyUI()
    {
        CharConfUI[] existingUIs = charactersParent.GetComponentsInChildren<CharConfUI>();
        foreach (CharConfUI ui in existingUIs)
        {
            if (ui != null && ui.gameObject != null)
            {
                DestroyImmediate(ui.gameObject);
            }
        }
        
        if (config.enableDebugLogging)
        {
            Debug.Log($"VirtualizedCharacterSetup: Cleaned up {existingUIs.Length} legacy UI elements");
        }
    }
    
    void LogPerformanceStats()
    {
        if (configsHandler?.characters != null && virtualizedList != null)
        {
            int totalCharacters = configsHandler.characters.Count;
            int activeItems = virtualizedList.GetActiveItemCount();
            int pooledItems = virtualizedList.GetPooledItemCount();
            
            Debug.Log($"VirtualizedCharacterSetup Performance Stats:\n" +
                     $"Total Characters: {totalCharacters}\n" +
                     $"Active UI Items: {activeItems}\n" +
                     $"Pooled UI Items: {pooledItems}\n" +
                     $"Memory Reduction: {((float)(totalCharacters - activeItems) / totalCharacters * 100):F1}%");
        }
    }
    
    // Public interface
    public bool IsSetupComplete => isSetupComplete;
    public string GetSetupStatus() => setupStatus;
    
    [ContextMenu("Reset System")]
    public void ResetSystem()
    {
        isSetupComplete = false;
        setupStatus = "Reset";
        
        if (integration != null)
        {
            integration.SwitchToLegacySystem();
        }
    }
    
    [ContextMenu("Show Performance Stats")]
    public void ShowPerformanceStats()
    {
        LogPerformanceStats();
    }
    
    // Validation
    void OnValidate()
    {
        if (config.maxVisibleItems < 1) config.maxVisibleItems = 1;
        if (config.poolSize < config.maxVisibleItems) config.poolSize = config.maxVisibleItems + 3;
        if (config.itemHeight < 10f) config.itemHeight = 10f;
    }
}
