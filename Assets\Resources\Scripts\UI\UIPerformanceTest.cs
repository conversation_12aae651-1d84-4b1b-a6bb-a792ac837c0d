using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Profiling;

/// <summary>
/// UI Performance testing script to verify virtualized character list improvements
/// Tests frame rate, memory usage, and UI responsiveness with 618 characters
/// </summary>
public class UIPerformanceTest : MonoBehaviour
{
    [Header("Test Configuration")]
    [SerializeField] private bool runTestOnStart = false;
    [SerializeField] private bool enableDetailedLogging = true;
    [SerializeField] private int testDurationSeconds = 10;
    
    [Header("Performance Monitoring")]
    [SerializeField] private bool showRealTimeStats = true;
    [SerializeField] private float statsUpdateInterval = 1f;
    
    // Performance tracking
    private List<float> frameRates = new List<float>();
    private List<long> memoryUsages = new List<long>();
    private float testStartTime;
    private bool isTestRunning = false;
    
    // UI References
    private VirtualizedCharacterList virtualizedList;
    private VirtualizedAddCharacter virtualizedAddChar;
    private ConfigsHandler configsHandler;
    
    // Test results
    private float averageFPS = 0f;
    private float minFPS = float.MaxValue;
    private float maxFPS = 0f;
    private long averageMemoryUsage = 0;
    private long peakMemoryUsage = 0;
    
    void Start()
    {
        configsHandler = GameObject.Find("GameConfigsHandler")?.GetComponent<ConfigsHandler>();
        
        if (runTestOnStart)
        {
            StartCoroutine(RunUIPerformanceTest());
        }
        
        if (showRealTimeStats)
        {
            InvokeRepeating(nameof(LogRealTimeStats), 1f, statsUpdateInterval);
        }
    }
    
    /// <summary>
    /// Runs comprehensive UI performance test
    /// </summary>
    public IEnumerator RunUIPerformanceTest()
    {
        Debug.Log("=== UI PERFORMANCE TEST STARTED ===");
        
        // Test 1: Initialization Performance
        yield return StartCoroutine(TestInitializationPerformance());
        
        // Test 2: Scrolling Performance
        yield return StartCoroutine(TestScrollingPerformance());
        
        // Test 3: Memory Usage Test
        yield return StartCoroutine(TestMemoryUsage());
        
        // Test 4: Frame Rate Stability Test
        yield return StartCoroutine(TestFrameRateStability());
        
        // Generate final report
        GeneratePerformanceReport();
        
        Debug.Log("=== UI PERFORMANCE TEST COMPLETED ===");
    }
    
    /// <summary>
    /// Tests UI initialization performance
    /// </summary>
    private IEnumerator TestInitializationPerformance()
    {
        Debug.Log("[UI_TEST] 🚀 Testing UI initialization performance...");
        
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        
        // Find virtualized components
        virtualizedList = FindObjectOfType<VirtualizedCharacterList>();
        virtualizedAddChar = FindObjectOfType<VirtualizedAddCharacter>();
        
        if (virtualizedList == null || virtualizedAddChar == null)
        {
            Debug.LogWarning("[UI_TEST] ⚠️ Virtualized components not found - test may be incomplete");
        }
        
        stopwatch.Stop();
        
        Debug.Log($"[UI_TEST] ✅ UI initialization completed in {stopwatch.ElapsedMilliseconds}ms");
        
        if (virtualizedList != null)
        {
            Debug.Log($"[UI_TEST] 📊 Active UI elements: {virtualizedList.GetActiveUIElementCount()}");
            Debug.Log($"[UI_TEST] 📊 Total characters: {virtualizedList.GetFilteredCharacterCount()}");
        }
        
        yield return null;
    }
    
    /// <summary>
    /// Tests scrolling performance with virtualized list
    /// </summary>
    private IEnumerator TestScrollingPerformance()
    {
        Debug.Log("[UI_TEST] 📜 Testing scrolling performance...");
        
        if (virtualizedList == null)
        {
            Debug.LogWarning("[UI_TEST] ⚠️ VirtualizedCharacterList not found for scrolling test");
            yield break;
        }
        
        float scrollTestDuration = 5f;
        float scrollSpeed = 100f;
        float startTime = Time.time;
        
        List<float> scrollFrameRates = new List<float>();
        
        while (Time.time - startTime < scrollTestDuration)
        {
            // Simulate scrolling
            int randomIndex = Random.Range(0, virtualizedList.GetFilteredCharacterCount());
            virtualizedList.ScrollToIndex(randomIndex);
            
            // Record frame rate
            float currentFPS = 1f / Time.unscaledDeltaTime;
            scrollFrameRates.Add(currentFPS);
            
            yield return new WaitForSeconds(0.1f);
        }
        
        float averageScrollFPS = 0f;
        foreach (float fps in scrollFrameRates)
        {
            averageScrollFPS += fps;
        }
        averageScrollFPS /= scrollFrameRates.Count;
        
        Debug.Log($"[UI_TEST] ✅ Scrolling test completed");
        Debug.Log($"[UI_TEST] 📊 Average FPS during scrolling: {averageScrollFPS:F1}");
        Debug.Log($"[UI_TEST] 📊 Scroll operations tested: {scrollFrameRates.Count}");
    }
    
    /// <summary>
    /// Tests memory usage with virtualized system
    /// </summary>
    private IEnumerator TestMemoryUsage()
    {
        Debug.Log("[UI_TEST] 💾 Testing memory usage...");
        
        // Force garbage collection before test
        System.GC.Collect();
        yield return new WaitForSeconds(0.5f);
        
        long memoryBefore = Profiler.GetTotalAllocatedMemory(false);
        
        // Simulate heavy UI operations
        for (int i = 0; i < 10; i++)
        {
            if (virtualizedList != null)
            {
                virtualizedList.ForceRefresh();
            }
            yield return new WaitForSeconds(0.1f);
        }
        
        long memoryAfter = Profiler.GetTotalAllocatedMemory(false);
        long memoryDifference = memoryAfter - memoryBefore;
        
        Debug.Log($"[UI_TEST] ✅ Memory usage test completed");
        Debug.Log($"[UI_TEST] 📊 Memory before: {memoryBefore / 1024 / 1024:F2} MB");
        Debug.Log($"[UI_TEST] 📊 Memory after: {memoryAfter / 1024 / 1024:F2} MB");
        Debug.Log($"[UI_TEST] 📊 Memory difference: {memoryDifference / 1024 / 1024:F2} MB");
        
        // Calculate theoretical old system memory usage
        if (configsHandler != null)
        {
            int characterCount = configsHandler.GetCharacters().Count;
            int activeUIElements = virtualizedList?.GetActiveUIElementCount() ?? 0;
            float memoryReduction = characterCount > 0 ? (1f - (float)activeUIElements / characterCount) * 100f : 0f;
            
            Debug.Log($"[UI_TEST] 📊 Estimated memory reduction: {memoryReduction:F1}%");
        }
    }
    
    /// <summary>
    /// Tests frame rate stability over time
    /// </summary>
    private IEnumerator TestFrameRateStability()
    {
        Debug.Log($"[UI_TEST] 🎯 Testing frame rate stability for {testDurationSeconds} seconds...");
        
        isTestRunning = true;
        testStartTime = Time.time;
        frameRates.Clear();
        memoryUsages.Clear();
        
        while (Time.time - testStartTime < testDurationSeconds)
        {
            float currentFPS = 1f / Time.unscaledDeltaTime;
            long currentMemory = Profiler.GetTotalAllocatedMemory(false);
            
            frameRates.Add(currentFPS);
            memoryUsages.Add(currentMemory);
            
            // Update min/max values
            if (currentFPS < minFPS) minFPS = currentFPS;
            if (currentFPS > maxFPS) maxFPS = currentFPS;
            if (currentMemory > peakMemoryUsage) peakMemoryUsage = currentMemory;
            
            yield return null;
        }
        
        isTestRunning = false;
        
        // Calculate averages
        float totalFPS = 0f;
        foreach (float fps in frameRates)
        {
            totalFPS += fps;
        }
        averageFPS = totalFPS / frameRates.Count;
        
        long totalMemory = 0;
        foreach (long memory in memoryUsages)
        {
            totalMemory += memory;
        }
        averageMemoryUsage = totalMemory / memoryUsages.Count;
        
        Debug.Log($"[UI_TEST] ✅ Frame rate stability test completed");
        Debug.Log($"[UI_TEST] 📊 Average FPS: {averageFPS:F1}");
        Debug.Log($"[UI_TEST] 📊 Min FPS: {minFPS:F1}");
        Debug.Log($"[UI_TEST] 📊 Max FPS: {maxFPS:F1}");
    }
    
    /// <summary>
    /// Generates comprehensive performance report
    /// </summary>
    private void GeneratePerformanceReport()
    {
        Debug.Log("=== UI PERFORMANCE REPORT ===");
        
        if (configsHandler != null)
        {
            int totalCharacters = configsHandler.GetCharacters().Count;
            int activeUIElements = virtualizedList?.GetActiveUIElementCount() ?? 0;
            
            Debug.Log($"📊 CHARACTER DATA:");
            Debug.Log($"  Total Characters: {totalCharacters}");
            Debug.Log($"  Active UI Elements: {activeUIElements}");
            Debug.Log($"  UI Memory Reduction: {(1f - (float)activeUIElements / totalCharacters) * 100f:F1}%");
        }
        
        Debug.Log($"📊 FRAME RATE PERFORMANCE:");
        Debug.Log($"  Average FPS: {averageFPS:F1}");
        Debug.Log($"  Minimum FPS: {minFPS:F1}");
        Debug.Log($"  Maximum FPS: {maxFPS:F1}");
        Debug.Log($"  FPS Stability: {((maxFPS - minFPS) / averageFPS < 0.3f ? "EXCELLENT" : "GOOD")}");
        
        Debug.Log($"📊 MEMORY USAGE:");
        Debug.Log($"  Average Memory: {averageMemoryUsage / 1024 / 1024:F2} MB");
        Debug.Log($"  Peak Memory: {peakMemoryUsage / 1024 / 1024:F2} MB");
        
        Debug.Log($"📊 PERFORMANCE TARGETS:");
        Debug.Log($"  Target: 60+ FPS - {(averageFPS >= 60 ? "✅ ACHIEVED" : "❌ NOT ACHIEVED")}");
        Debug.Log($"  Target: 95% UI Memory Reduction - {(virtualizedList != null && configsHandler != null && (1f - (float)virtualizedList.GetActiveUIElementCount() / configsHandler.GetCharacters().Count) >= 0.95f ? "✅ ACHIEVED" : "❌ NOT ACHIEVED")}");
        
        Debug.Log("=== END PERFORMANCE REPORT ===");
    }
    
    /// <summary>
    /// Logs real-time performance statistics
    /// </summary>
    private void LogRealTimeStats()
    {
        if (!showRealTimeStats) return;
        
        float currentFPS = 1f / Time.unscaledDeltaTime;
        long currentMemory = Profiler.GetTotalAllocatedMemory(false);
        
        Debug.Log($"[UI_STATS] FPS: {currentFPS:F1} | Memory: {currentMemory / 1024 / 1024:F1}MB | Active UI: {virtualizedList?.GetActiveUIElementCount() ?? 0}");
    }
    
    /// <summary>
    /// Manual test trigger
    /// </summary>
    [ContextMenu("Run UI Performance Test")]
    public void RunTestManually()
    {
        StartCoroutine(RunUIPerformanceTest());
    }
    
    /// <summary>
    /// Toggle real-time stats display
    /// </summary>
    [ContextMenu("Toggle Real-Time Stats")]
    public void ToggleRealTimeStats()
    {
        showRealTimeStats = !showRealTimeStats;
        Debug.Log($"[UI_TEST] Real-time stats: {(showRealTimeStats ? "ENABLED" : "DISABLED")}");
    }
}
