using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using System;

public class StatsValueDisplay : MonoBehaviour
{
    public GameObject apmin, bl, hp, atk, def, atkLim; // stats values gameobjects

    readonly float pos = 0.45f; // point where to start scrolling

    CharConfUI confUI; // character configuration UI


    Camera confCamera;
    private void Start()
    {
        // gets the configs camera
        confCamera = GameObject.Find("Configs Camera").GetComponent<Camera>();
    }

    private void Update()
    {
        // checks if the camera is active and there is a screen touch
        if (confCamera != null && confCamera.enabled && Input.touchCount > 0 && confCamera.ScreenToViewportPoint(Input.GetTouch(0).position).y > pos)
        {
            Vector3 movement = new(0f, Input.GetTouch(0).deltaPosition.y / (confCamera.pixelHeight / 10f), 0f);

            transform.Translate(movement);
        }
    }
    
    public void SetValues(<PERSON><PERSON><PERSON><PERSON> character, CharConfUI confUI) // Updates the values of the stats UI
    {
        this.confUI = confUI;

        apmin.GetComponent<TextMeshProUGUI>().text = "Apmin\n" + string.Join("\n", character.stats.GetApMin());
        bl.GetComponent<TextMeshProUGUI>().text = "BL\n" + string.Join("\n", character.stats.GetBl());
        hp.GetComponent<TextMeshProUGUI>().text = "HP\n" + string.Join("\n", character.stats.GetHp());
        atk.GetComponent<TextMeshProUGUI>().text = "Atk\n" + string.Join("\n", character.stats.GetAtk());
        def.GetComponent<TextMeshProUGUI>().text = "Def\n" + string.Join("\n", character.stats.GetDef());
        atkLim.GetComponent<TextMeshProUGUI>().text = "AtkLim\n" + string.Join("\n", character.stats.GetAtkLim());
    }
}
