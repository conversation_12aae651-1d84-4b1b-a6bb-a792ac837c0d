using UnityEngine;
using UnityEngine.UI;
using TMPro;

/// <summary>
/// Virtual wrapper for character UI items in the pooled list
/// Manages the display and interaction of a single character UI element
/// </summary>
public class VirtualCharacterUIItem : MonoBehaviour
{
    [Header("UI References")]
    [SerializeField] private RectTransform rectTransform;
    [SerializeField] private Button enemyButton;
    [SerializeField] private TMP_InputField nameInput;
    [SerializeField] private Button statsButton;
    [SerializeField] private Button skillsButton;
    [SerializeField] private Button modsButton;
    [SerializeField] private TMP_InputField levelInput;
    
    [Header("Visual Elements")]
    [SerializeField] private GameObject enemyIndicator;
    [SerializeField] private Image backgroundImage;
    
    // Current character data
    private BattleCharacter currentCharacter;
    private int currentIndex;
    private bool isInitialized = false;
    
    // Callbacks for interactions
    private System.Action<BattleCharacter> onCharacterSelected;
    private System.Action<BattleCharacter> onStatsClicked;
    private System.Action<BattleCharacter> onSkillsClicked;
    private System.Action<BattleCharacter> onModsClicked;
    
    // ConfigsHandler reference
    private ConfigsHandler configsHandler;
    
    /// <summary>
    /// Initialize the UI item and find all required components
    /// </summary>
    public void Initialize()
    {
        if (isInitialized) return;
        
        // Get RectTransform
        rectTransform = GetComponent<RectTransform>();
        
        // Find UI components based on the existing CharacterValues prefab structure
        FindUIComponents();
        
        // Setup button listeners
        SetupButtonListeners();
        
        // Get ConfigsHandler reference
        configsHandler = GameObject.Find("GameConfigsHandler")?.GetComponent<ConfigsHandler>();
        
        isInitialized = true;
        
        Debug.Log($"[VirtualCharacterUIItem] Initialized: {gameObject.name}");
    }
    
    /// <summary>
    /// Find all UI components in the character prefab
    /// </summary>
    private void FindUIComponents()
    {
        // Based on CharConfUI structure: Enemy(0), Name(1), Stats(2), Skills(3), Mods(4), Level(5)
        if (transform.childCount >= 6)
        {
            // Enemy button (child 0)
            var enemyObj = transform.GetChild(0).gameObject;
            enemyButton = enemyObj.GetComponent<Button>();
            enemyIndicator = enemyObj.transform.childCount > 0 ? enemyObj.transform.GetChild(0).gameObject : null;
            
            // Name input (child 1)
            nameInput = transform.GetChild(1).GetComponent<TMP_InputField>();
            
            // Stats button (child 2)
            statsButton = transform.GetChild(2).GetComponent<Button>();
            
            // Skills button (child 3)
            skillsButton = transform.GetChild(3).GetComponent<Button>();
            
            // Mods button (child 4)
            modsButton = transform.GetChild(4).GetComponent<Button>();
            
            // Level input (child 5)
            levelInput = transform.GetChild(5).GetComponent<TMP_InputField>();
        }
        
        // Get background image if available
        backgroundImage = GetComponent<Image>();
        
        // Validate required components
        if (nameInput == null || levelInput == null)
        {
            Debug.LogError($"[VirtualCharacterUIItem] Missing required components on {gameObject.name}");
        }
    }
    
    /// <summary>
    /// Setup button event listeners
    /// </summary>
    private void SetupButtonListeners()
    {
        if (enemyButton != null)
        {
            enemyButton.onClick.RemoveAllListeners();
            enemyButton.onClick.AddListener(() => OnEnemyButtonClicked());
        }
        
        if (statsButton != null)
        {
            statsButton.onClick.RemoveAllListeners();
            statsButton.onClick.AddListener(() => OnStatsButtonClicked());
        }
        
        if (skillsButton != null)
        {
            skillsButton.onClick.RemoveAllListeners();
            skillsButton.onClick.AddListener(() => OnSkillsButtonClicked());
        }
        
        if (modsButton != null)
        {
            modsButton.onClick.RemoveAllListeners();
            modsButton.onClick.AddListener(() => OnModsButtonClicked());
        }
        
        // Setup input field listeners
        if (nameInput != null)
        {
            nameInput.onEndEdit.RemoveAllListeners();
            nameInput.onEndEdit.AddListener(OnNameChanged);
        }
        
        if (levelInput != null)
        {
            levelInput.onEndEdit.RemoveAllListeners();
            levelInput.onEndEdit.AddListener(OnLevelChanged);
        }
    }
    
    /// <summary>
    /// Set the character data for this UI item
    /// </summary>
    public void SetCharacter(BattleCharacter character, int index)
    {
        currentCharacter = character;
        currentIndex = index;
        
        RefreshDisplay();
    }
    
    /// <summary>
    /// Set the position of this UI item in the virtual list
    /// </summary>
    public void SetPosition(float yPosition)
    {
        if (rectTransform != null)
        {
            rectTransform.anchoredPosition = new Vector2(0, -yPosition);
        }
    }
    
    /// <summary>
    /// Set callback functions for UI interactions
    /// </summary>
    public void SetCallbacks(
        System.Action<BattleCharacter> onSelected,
        System.Action<BattleCharacter> onStats,
        System.Action<BattleCharacter> onSkills,
        System.Action<BattleCharacter> onMods)
    {
        onCharacterSelected = onSelected;
        onStatsClicked = onStats;
        onSkillsClicked = onSkills;
        onModsClicked = onMods;
    }
    
    /// <summary>
    /// Refresh the visual display with current character data
    /// </summary>
    public void RefreshDisplay()
    {
        if (currentCharacter == null) return;
        
        // Update name
        if (nameInput != null)
        {
            nameInput.text = currentCharacter.name;
        }
        
        // Update level
        if (levelInput != null)
        {
            levelInput.text = currentCharacter.level.ToString();
        }
        
        // Update enemy indicator
        if (enemyIndicator != null)
        {
            enemyIndicator.SetActive(currentCharacter.isEnemy);
        }
        
        // Update background color based on character type or selection
        UpdateBackgroundColor();
    }
    
    /// <summary>
    /// Update background color based on character state
    /// </summary>
    private void UpdateBackgroundColor()
    {
        if (backgroundImage == null) return;
        
        // You can customize colors based on character properties
        Color backgroundColor = Color.white;
        
        if (currentCharacter.isEnemy)
        {
            backgroundColor = new Color(1f, 0.8f, 0.8f, 1f); // Light red for enemies
        }
        
        backgroundImage.color = backgroundColor;
    }
    
    /// <summary>
    /// Reset the item state when returning to pool
    /// </summary>
    public void ResetState()
    {
        currentCharacter = null;
        currentIndex = -1;
        
        // Clear input fields
        if (nameInput != null) nameInput.text = "";
        if (levelInput != null) levelInput.text = "";
        
        // Hide enemy indicator
        if (enemyIndicator != null) enemyIndicator.SetActive(false);
        
        // Reset background color
        if (backgroundImage != null) backgroundImage.color = Color.white;
    }
    
    /// <summary>
    /// Get the current character
    /// </summary>
    public BattleCharacter GetCharacter() => currentCharacter;
    
    /// <summary>
    /// Get the current index
    /// </summary>
    public int GetIndex() => currentIndex;
    
    // Button event handlers
    private void OnEnemyButtonClicked()
    {
        if (currentCharacter != null && configsHandler != null)
        {
            currentCharacter.isEnemy = !currentCharacter.isEnemy;
            configsHandler.MarkCharacterModified(currentCharacter);
            RefreshDisplay();
        }
        
        onCharacterSelected?.Invoke(currentCharacter);
    }
    
    private void OnStatsButtonClicked()
    {
        onStatsClicked?.Invoke(currentCharacter);
    }
    
    private void OnSkillsButtonClicked()
    {
        onSkillsClicked?.Invoke(currentCharacter);
    }
    
    private void OnModsButtonClicked()
    {
        onModsClicked?.Invoke(currentCharacter);
    }
    
    // Input field event handlers
    private void OnNameChanged(string newName)
    {
        if (currentCharacter != null && configsHandler != null)
        {
            currentCharacter.name = newName;
            configsHandler.MarkCharacterModified(currentCharacter);
        }
    }
    
    private void OnLevelChanged(string newLevelStr)
    {
        if (currentCharacter != null && configsHandler != null && int.TryParse(newLevelStr, out int newLevel))
        {
            currentCharacter.SetLevel(newLevel);
            configsHandler.MarkCharacterModified(currentCharacter);
        }
    }
}
