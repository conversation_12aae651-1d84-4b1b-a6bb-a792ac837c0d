{"version": 3, "targets": {".NETStandard,Version=v2.1": {"Assembly-CSharp-firstpass/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"NativeFilePicker.Editor": "1.0.0", "NativeFilePicker.Runtime": "1.0.0", "OutlineFx": "1.0.0", "OutlineFx.Editor": "1.0.0", "spine-csharp": "1.0.0", "spine-unity": "1.0.0", "spine-unity-editor": "1.0.0"}, "compile": {"bin/placeholder/Assembly-CSharp-firstpass.dll": {}}, "runtime": {"bin/placeholder/Assembly-CSharp-firstpass.dll": {}}}, "NativeFilePicker.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/NativeFilePicker.Editor.dll": {}}, "runtime": {"bin/placeholder/NativeFilePicker.Editor.dll": {}}}, "NativeFilePicker.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/NativeFilePicker.Runtime.dll": {}}, "runtime": {"bin/placeholder/NativeFilePicker.Runtime.dll": {}}}, "OutlineFx/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/OutlineFx.dll": {}}, "runtime": {"bin/placeholder/OutlineFx.dll": {}}}, "OutlineFx.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"OutlineFx": "1.0.0"}, "compile": {"bin/placeholder/OutlineFx.Editor.dll": {}}, "runtime": {"bin/placeholder/OutlineFx.Editor.dll": {}}}, "spine-csharp/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/spine-csharp.dll": {}}, "runtime": {"bin/placeholder/spine-csharp.dll": {}}}, "spine-unity/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"spine-csharp": "1.0.0"}, "compile": {"bin/placeholder/spine-unity.dll": {}}, "runtime": {"bin/placeholder/spine-unity.dll": {}}}, "spine-unity-editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"spine-csharp": "1.0.0", "spine-unity": "1.0.0"}, "compile": {"bin/placeholder/spine-unity-editor.dll": {}}, "runtime": {"bin/placeholder/spine-unity-editor.dll": {}}}}}, "libraries": {"Assembly-CSharp-firstpass/1.0.0": {"type": "project", "path": "Assembly-CSharp-firstpass.csproj", "msbuildProject": "Assembly-CSharp-firstpass.csproj"}, "NativeFilePicker.Editor/1.0.0": {"type": "project", "path": "NativeFilePicker.Editor.csproj", "msbuildProject": "NativeFilePicker.Editor.csproj"}, "NativeFilePicker.Runtime/1.0.0": {"type": "project", "path": "NativeFilePicker.Runtime.csproj", "msbuildProject": "NativeFilePicker.Runtime.csproj"}, "OutlineFx/1.0.0": {"type": "project", "path": "OutlineFx.csproj", "msbuildProject": "OutlineFx.csproj"}, "OutlineFx.Editor/1.0.0": {"type": "project", "path": "OutlineFx.Editor.csproj", "msbuildProject": "OutlineFx.Editor.csproj"}, "spine-csharp/1.0.0": {"type": "project", "path": "spine-csharp.csproj", "msbuildProject": "spine-csharp.csproj"}, "spine-unity/1.0.0": {"type": "project", "path": "spine-unity.csproj", "msbuildProject": "spine-unity.csproj"}, "spine-unity-editor/1.0.0": {"type": "project", "path": "spine-unity-editor.c<PERSON><PERSON>j", "msbuildProject": "spine-unity-editor.c<PERSON><PERSON>j"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["Assembly-CSharp-firstpass >= 1.0.0", "NativeFilePicker.Editor >= 1.0.0", "NativeFilePicker.Runtime >= 1.0.0", "OutlineFx >= 1.0.0", "OutlineFx.Editor >= 1.0.0", "spine-csharp >= 1.0.0", "spine-unity >= 1.0.0", "spine-unity-editor >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "d:\\Menino Autista\\DKG-RPG-Mobile\\Assembly-CSharp.csproj", "projectName": "Assembly-CSharp", "projectPath": "d:\\Menino Autista\\DKG-RPG-Mobile\\Assembly-CSharp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "d:\\Menino Autista\\DKG-RPG-Mobile\\Temp\\obj\\Assembly-CSharp\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"d:\\Menino Autista\\DKG-RPG-Mobile\\Assembly-CSharp-firstpass.csproj": {"projectPath": "d:\\<PERSON>ino Autista\\DKG-RPG-Mobile\\Assembly-CSharp-firstpass.csproj"}, "d:\\Menino Autista\\DKG-RPG-Mobile\\NativeFilePicker.Editor.csproj": {"projectPath": "d:\\Menino Autista\\DKG-RPG-Mobile\\NativeFilePicker.Editor.csproj"}, "d:\\Menino Autista\\DKG-RPG-Mobile\\NativeFilePicker.Runtime.csproj": {"projectPath": "d:\\Menino Autista\\DKG-RPG-Mobile\\NativeFilePicker.Runtime.csproj"}, "d:\\Menino Autista\\DKG-RPG-Mobile\\OutlineFx.csproj": {"projectPath": "d:\\Menino Autista\\DKG-RPG-Mobile\\OutlineFx.csproj"}, "d:\\Menino Autista\\DKG-RPG-Mobile\\OutlineFx.Editor.csproj": {"projectPath": "d:\\<PERSON>ino Autista\\DKG-RPG-Mobile\\OutlineFx.Editor.csproj"}, "d:\\Menino Autista\\DKG-RPG-Mobile\\spine-csharp.csproj": {"projectPath": "d:\\<PERSON>ino Autista\\DKG-RPG-Mobile\\spine-csharp.csproj"}, "d:\\Menino Autista\\DKG-RPG-Mobile\\spine-unity-editor.csproj": {"projectPath": "d:\\<PERSON>ino Autista\\DKG-RPG-Mobile\\spine-unity-editor.csproj"}, "d:\\Menino Autista\\DKG-RPG-Mobile\\spine-unity.csproj": {"projectPath": "d:\\<PERSON>ino Autista\\DKG-RPG-Mobile\\spine-unity.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}}