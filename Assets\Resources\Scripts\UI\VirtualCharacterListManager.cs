using UnityEngine;
using UnityEngine.UI;
using TMPro;

/// <summary>
/// Manager component that integrates the virtual character list with the existing UI system
/// Replaces the old character UI creation loop with virtual scrolling
/// </summary>
public class VirtualCharacterListManager : MonoBehaviour
{
    [Header("UI References")]
    [SerializeField] private VirtualCharacterList virtualList;
    [SerializeField] private TMP_InputField searchInput;
    [SerializeField] private Button refreshButton;
    [SerializeField] private TextMeshProUGUI statsText;
    
    [Header("Integration Settings")]
    [SerializeField] private bool enablePerformanceLogging = true;
    [SerializeField] private float statsUpdateInterval = 1f;
    
    // References to existing UI systems
    private ConfigsHandler configsHandler;
    private GameObject statsValues;
    private GameObject skillsValues;
    private GameObject modsValues;
    
    // Performance tracking
    private float lastStatsUpdate = 0f;
    private int lastCharacterCount = 0;
    
    private void Awake()
    {
        InitializeComponents();
    }
    
    private void Start()
    {
        InitializeReferences();
        SetupEventHandlers();
        
        // Initialize virtual list after Configs<PERSON><PERSON><PERSON> is ready
        if (configsHandler != null)
        {
            InitializeVirtualList();
        }
    }
    
    private void Update()
    {
        UpdatePerformanceStats();
    }
    
    /// <summary>
    /// Initialize UI components
    /// </summary>
    private void InitializeComponents()
    {
        // Auto-find virtual list if not assigned
        if (virtualList == null)
        {
            virtualList = GetComponentInChildren<VirtualCharacterList>();
        }
        
        // Auto-find search input
        if (searchInput == null)
        {
            searchInput = GameObject.Find("CharacterSearchInput")?.GetComponent<TMP_InputField>();
        }
        
        // Auto-find refresh button
        if (refreshButton == null)
        {
            refreshButton = GameObject.Find("RefreshCharactersButton")?.GetComponent<Button>();
        }
        
        // Auto-find stats text
        if (statsText == null)
        {
            statsText = GameObject.Find("VirtualListStatsText")?.GetComponent<TextMeshProUGUI>();
        }
    }
    
    /// <summary>
    /// Initialize references to existing systems
    /// </summary>
    private void InitializeReferences()
    {
        configsHandler = GameObject.Find("GameConfigsHandler")?.GetComponent<ConfigsHandler>();
        
        if (configsHandler != null)
        {
            statsValues = configsHandler.StatsValues;
            skillsValues = configsHandler.SkillsValues;
            modsValues = configsHandler.ModsValues;
        }
        else
        {
            Debug.LogError("[VirtualCharacterListManager] ConfigsHandler not found!");
        }
    }
    
    /// <summary>
    /// Setup event handlers for UI interactions
    /// </summary>
    private void SetupEventHandlers()
    {
        // Setup search functionality
        if (searchInput != null)
        {
            searchInput.onValueChanged.AddListener(OnSearchChanged);
        }
        
        // Setup refresh button
        if (refreshButton != null)
        {
            refreshButton.onClick.AddListener(RefreshCharacterList);
        }
        
        // Setup virtual list callbacks
        if (virtualList != null)
        {
            virtualList.OnCharacterSelected += OnCharacterSelected;
            virtualList.OnCharacterStatsClicked += OnCharacterStatsClicked;
            virtualList.OnCharacterSkillsClicked += OnCharacterSkillsClicked;
            virtualList.OnCharacterModsClicked += OnCharacterModsClicked;
        }
    }
    
    /// <summary>
    /// Initialize the virtual list system
    /// </summary>
    private void InitializeVirtualList()
    {
        if (virtualList == null)
        {
            Debug.LogError("[VirtualCharacterListManager] VirtualCharacterList component not found!");
            return;
        }
        
        Debug.Log("[VirtualCharacterListManager] Initializing virtual character list...");
        
        // Load characters into virtual list
        virtualList.LoadCharacters();
        
        // Update initial stats
        UpdatePerformanceStats();
        
        Debug.Log("[VirtualCharacterListManager] Virtual character list initialized successfully");
    }
    
    /// <summary>
    /// Handle search input changes
    /// </summary>
    private void OnSearchChanged(string searchText)
    {
        if (virtualList != null)
        {
            virtualList.ApplyFilter(searchText);
        }
    }
    
    /// <summary>
    /// Refresh the character list
    /// </summary>
    public void RefreshCharacterList()
    {
        if (virtualList != null)
        {
            Debug.Log("[VirtualCharacterListManager] Refreshing character list...");
            virtualList.LoadCharacters();
        }
    }
    
    /// <summary>
    /// Handle character selection
    /// </summary>
    private void OnCharacterSelected(BattleCharacter character)
    {
        if (character == null) return;
        
        Debug.Log($"[VirtualCharacterListManager] Character selected: {character.name}");
        
        // You can add additional selection logic here
        // For example, highlighting the selected character
    }
    
    /// <summary>
    /// Handle stats button click
    /// </summary>
    private void OnCharacterStatsClicked(BattleCharacter character)
    {
        if (character == null || statsValues == null) return;
        
        Debug.Log($"[VirtualCharacterListManager] Stats clicked for: {character.name}");
        
        // Hide other UI panels
        HideAllValuePanels();
        
        // Show stats panel
        statsValues.SetActive(true);
        var statsDisplay = statsValues.GetComponent<StatsValueDisplay>();
        if (statsDisplay != null)
        {
            // Note: This requires updating StatsValueDisplay to work without CharConfUI
            // statsDisplay.SetValues(character, null);
        }
    }
    
    /// <summary>
    /// Handle skills button click
    /// </summary>
    private void OnCharacterSkillsClicked(BattleCharacter character)
    {
        if (character == null || skillsValues == null) return;
        
        Debug.Log($"[VirtualCharacterListManager] Skills clicked for: {character.name}");
        
        // Hide other UI panels
        HideAllValuePanels();
        
        // Show skills panel
        skillsValues.SetActive(true);
        var skillsDisplay = skillsValues.GetComponent<SkillsValueDisplay>();
        if (skillsDisplay != null)
        {
            // Note: This requires updating SkillsValueDisplay to work without CharConfUI
            // skillsDisplay.SetValues(character, null);
        }
    }
    
    /// <summary>
    /// Handle mods button click
    /// </summary>
    private void OnCharacterModsClicked(BattleCharacter character)
    {
        if (character == null || modsValues == null) return;
        
        Debug.Log($"[VirtualCharacterListManager] Mods clicked for: {character.name}");
        
        // Hide other UI panels
        HideAllValuePanels();
        
        // Show mods panel
        modsValues.SetActive(true);
        var modsDisplay = modsValues.GetComponent<ModsValueDisplay>();
        if (modsDisplay != null)
        {
            // Note: This requires updating ModsValueDisplay to work without CharConfUI
            // modsDisplay.SetValues(character, null);
        }
    }
    
    /// <summary>
    /// Hide all value display panels
    /// </summary>
    private void HideAllValuePanels()
    {
        if (statsValues != null) statsValues.SetActive(false);
        if (skillsValues != null) skillsValues.SetActive(false);
        if (modsValues != null) modsValues.SetActive(false);
    }
    
    /// <summary>
    /// Update performance statistics display
    /// </summary>
    private void UpdatePerformanceStats()
    {
        if (!enablePerformanceLogging || statsText == null || virtualList == null) return;
        
        if (Time.time - lastStatsUpdate >= statsUpdateInterval)
        {
            lastStatsUpdate = Time.time;
            
            var stats = virtualList.GetStats();
            var poolStats = virtualList.GetComponent<VirtualCharacterList>();
            
            string statsDisplay = $"Virtual List Performance:\n" +
                                 $"Total Characters: {stats.TotalCharacters}\n" +
                                 $"Filtered: {stats.FilteredCharacters}\n" +
                                 $"Visible UI Elements: {stats.VisibleItems}\n" +
                                 $"Memory Reduction: {stats.MemoryReduction:P1}\n" +
                                 $"Cache Entries: {stats.CacheEntries}";
            
            statsText.text = statsDisplay;
            
            // Log performance improvements
            if (stats.TotalCharacters != lastCharacterCount)
            {
                lastCharacterCount = stats.TotalCharacters;
                
                if (enablePerformanceLogging)
                {
                    Debug.Log($"[VirtualCharacterListManager] Performance: " +
                             $"{stats.VisibleItems}/{stats.TotalCharacters} UI elements " +
                             $"({stats.MemoryReduction:P1} reduction)");
                }
            }
        }
    }
    
    /// <summary>
    /// Scroll to a specific character
    /// </summary>
    public void ScrollToCharacter(BattleCharacter character)
    {
        if (virtualList != null)
        {
            virtualList.ScrollToCharacter(character);
        }
    }
    
    /// <summary>
    /// Refresh a specific character's display
    /// </summary>
    public void RefreshCharacter(BattleCharacter character)
    {
        if (virtualList != null)
        {
            virtualList.RefreshCharacter(character);
        }
    }
    
    /// <summary>
    /// Get current performance statistics
    /// </summary>
    public VirtualListStats GetPerformanceStats()
    {
        return virtualList?.GetStats() ?? new VirtualListStats();
    }
    
    private void OnDestroy()
    {
        // Clean up event handlers
        if (searchInput != null)
        {
            searchInput.onValueChanged.RemoveListener(OnSearchChanged);
        }
        
        if (refreshButton != null)
        {
            refreshButton.onClick.RemoveListener(RefreshCharacterList);
        }
        
        if (virtualList != null)
        {
            virtualList.OnCharacterSelected -= OnCharacterSelected;
            virtualList.OnCharacterStatsClicked -= OnCharacterStatsClicked;
            virtualList.OnCharacterSkillsClicked -= OnCharacterSkillsClicked;
            virtualList.OnCharacterModsClicked -= OnCharacterModsClicked;
        }
    }
}
