using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using UnityEngine.UI;

public class CharatersForPartyUIHandler : MonoBehaviour
{
    ConfigsHandler configsHandler;

    ToggleGroup editParty; // Toggle group of the party to edit

    public int selectedParty = 0; // the selected party to edit index

    // Performance optimization: cached character filtering
    private CachedCharacterFilter characterFilter;
    private List<BattleCharacter> cachedNonEnemyCharacters = new List<BattleCharacter>();
    private int lastSelectedParty = -1;
    private int lastCachedCharacterCount = -1;

    void Start()
    {
        // gets the ConfigsHandler script
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>();

        // gets the toggle group of the party to edit
        editParty = transform.parent.parent.GetChild(0).GetComponent<ToggleGroup>();

        // Initialize cached character filter
        characterFilter = gameObject.AddComponent<CachedCharacterFilter>();
        characterFilter.OnCacheUpdated += OnCharacterCacheUpdated;
    }

    void Update()
    {
        // Get selected party index (this is lightweight)
        selectedParty = editParty.ActiveToggles().FirstOrDefault().transform.GetSiblingIndex();

        // Only update UI if something actually changed
        if (ShouldUpdateUI())
        {
            UpdateCharacterUI();
        }

        // Update button states (only if party selection changed)
        if (selectedParty != lastSelectedParty)
        {
            UpdateButtonStates();
            lastSelectedParty = selectedParty;
        }
    }

    /// <summary>
    /// Checks if UI needs updating based on cached character changes
    /// </summary>
    private bool ShouldUpdateUI()
    {
        // Use cached character count to avoid expensive operations
        int currentCharacterCount = characterFilter.GetNonEnemyCharacters().Count;
        int currentUICount = GetComponentsInChildren<CharacterValuesParty>().Length;

        return currentCharacterCount != currentUICount || currentCharacterCount != lastCachedCharacterCount;
    }

    /// <summary>
    /// Updates character UI elements using cached data
    /// </summary>
    private void UpdateCharacterUI()
    {
        // Get cached non-enemy characters (much faster than LINQ filtering)
        cachedNonEnemyCharacters = characterFilter.GetNonEnemyCharacters();
        lastCachedCharacterCount = cachedNonEnemyCharacters.Count;

        var existingCharacterUIs = GetComponentsInChildren<CharacterValuesParty>().ToList();
        var existingCharacterIds = existingCharacterUIs.Select(c => c.character?.id).ToHashSet();

        // Add new characters that don't have UI yet
        foreach (BattleCharacter character in cachedNonEnemyCharacters)
        {
            if (!existingCharacterIds.Contains(character.id))
            {
                CreateCharacterUI(character);
            }
        }

        // Remove UI for characters that no longer exist
        var currentCharacterIds = cachedNonEnemyCharacters.Select(c => c.id).ToHashSet();
        foreach (var charParty in existingCharacterUIs)
        {
            if (charParty.character == null || !currentCharacterIds.Contains(charParty.character.id))
            {
                Destroy(charParty.gameObject);
            }
        }
    }

    /// <summary>
    /// Creates UI for a single character
    /// </summary>
    private void CreateCharacterUI(BattleCharacter character)
    {
        GameObject characterUI = Instantiate(Resources.Load<GameObject>("Prefabs/CharacterPartyList"), transform);
        characterUI.name = "Char" + cachedNonEnemyCharacters.IndexOf(character);
        characterUI.GetComponent<CharacterValuesParty>().character = character;
    }

    /// <summary>
    /// Updates button interactable states for current party
    /// </summary>
    private void UpdateButtonStates()
    {
        foreach (CharacterValuesParty charParty in GetComponentsInChildren<CharacterValuesParty>())
        {
            if (charParty.character != null)
            {
                bool isOnParty = configsHandler.IsCharacterOnParty(charParty.character, selectedParty);
                charParty.GetComponent<Button>().interactable = !isOnParty;
            }
        }
    }

    /// <summary>
    /// Called when character cache is updated
    /// </summary>
    private void OnCharacterCacheUpdated()
    {
        // Force UI update on next frame
        lastCachedCharacterCount = -1;
    }

    public int GetSelectedSlot(out bool isActive) // gets the selected slot and if is an active slot or stock slot
    {
        // gets the party object
        GameObject party = transform.parent.parent.GetChild(1).GetChild(0).GetChild(selectedParty).gameObject;

        // gets all the Selected Buttons Borders
        Image[] buttons = party.GetComponentsInChildren<Image>().Where(b => b.name == "SB").ToArray();
        int index = 0;

        isActive = false;

        if (buttons.FirstOrDefault(b => b.enabled)) // if there is a selected button
        {
            // gets the index of the selected button
            index = buttons.FirstOrDefault(b => b.enabled).transform.parent.GetSiblingIndex();
        }
        // if there ara no selected slots, it just uses the first slot in the stock party

        return index;
    }
}
