using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using UnityEngine.UI;

public class CharatersForPartyUIHandler : MonoBehaviour
{
    [Header("Virtualization Settings")]
    [SerializeField] private bool useVirtualization = true;
    [SerializeField] private bool debugMode = false;

    ConfigsHandler configsHandler;
    ToggleGroup editParty; // Toggle group of the party to edit

    // Virtualization components
    private VirtualizedCharacterList virtualizedList;
    private CachedCharacterFilter characterFilter;

    // Original system components (fallback)
    private bool originalSystemActive = false;

    public int selectedParty = 0; // the selected party to edit index

    // Performance tracking
    private int framesSinceLastUpdate = 0;
    private const int UPDATE_FREQUENCY = 5; // Update every 5 frames for better performance

    void Start()
    {
        // gets the ConfigsHandler script
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>();

        // gets the toggle group of the party to edit
        editParty = transform.parent.parent.GetChild(0).GetComponent<ToggleGroup>();

        // Initialize virtualization system
        if (useVirtualization)
        {
            InitializeVirtualization();
        }
        else
        {
            originalSystemActive = true;
        }
    }

    void Update()
    {
        // Throttle updates for performance
        framesSinceLastUpdate++;
        if (framesSinceLastUpdate < UPDATE_FREQUENCY) return;
        framesSinceLastUpdate = 0;

        // Update selected party index
        UpdateSelectedParty();

        if (useVirtualization && virtualizedList != null)
        {
            UpdateVirtualizedSystem();
        }
        else
        {
            UpdateOriginalSystem();
        }
    }

    /// <summary>
    /// Initialize the virtualization system
    /// </summary>
    private void InitializeVirtualization()
    {
        // Setup virtualized list if not already present
        virtualizedList = GetComponent<VirtualizedCharacterList>();
        if (virtualizedList == null)
        {
            virtualizedList = gameObject.AddComponent<VirtualizedCharacterList>();
        }

        // Setup cached character filter
        characterFilter = new CachedCharacterFilter(configsHandler);
        characterFilter.SetFilter(CachedCharacterFilter.FilterType.NonEnemies);

        if (debugMode)
            Debug.Log("[PARTY_UI_HANDLER] ✅ Virtualization initialized");
    }

    /// <summary>
    /// Update selected party index efficiently
    /// </summary>
    private void UpdateSelectedParty()
    {
        if (editParty != null)
        {
            var activeToggle = editParty.ActiveToggles().FirstOrDefault();
            if (activeToggle != null)
            {
                selectedParty = activeToggle.transform.GetSiblingIndex();
            }
        }
    }

    /// <summary>
    /// Update virtualized character list system
    /// </summary>
    private void UpdateVirtualizedSystem()
    {
        // The virtualized list handles most of the work automatically
        // We just need to refresh if character data changed
        if (characterFilter != null)
        {
            virtualizedList?.RefreshCharacterList();
        }

        // Update button states for visible characters
        UpdateVisibleCharacterStates();
    }

    /// <summary>
    /// Update button states for currently visible characters
    /// </summary>
    private void UpdateVisibleCharacterStates()
    {
        var visibleCharacterUIs = GetComponentsInChildren<CharacterValuesParty>();

        foreach (CharacterValuesParty charParty in visibleCharacterUIs)
        {
            if (charParty.character != null)
            {
                // Update button interactability based on party membership
                var button = charParty.GetComponent<Button>();
                if (button != null)
                {
                    bool isInParty = configsHandler.IsCharacterOnParty(charParty.character, selectedParty);
                    button.interactable = !isInParty;
                }
            }
        }
    }

    /// <summary>
    /// Update original character list system (fallback)
    /// </summary>
    private void UpdateOriginalSystem()
    {
        // gets all the characters that are not enemies
        List<BattleCharacter> characters = configsHandler.GetCharacters().FindAll(c => !c.isEnemy);

        // updates the chacaters for the parties if there are new characters
        if (GetComponentsInChildren<CharacterValuesParty>().Length != characters.Count)
        {
            foreach (BattleCharacter character in characters.Except(GetComponentsInChildren<CharacterValuesParty>().Select(c => c.character).ToList()))
            {
                // instantiates the character UI
                GameObject characterUI = Instantiate(Resources.Load<GameObject>("Prefabs/CharacterPartyList"), transform);
                // sets the name
                characterUI.name = "Char" + characters.IndexOf(character);
                // sets the character
                characterUI.GetComponent<CharacterValuesParty>().character = character;
            }
        }

        foreach (CharacterValuesParty charParty in GetComponentsInChildren<CharacterValuesParty>()) // loops through all the characters
        {
            if (characters.IndexOf(characters.Find(c => c.id == charParty.character.id)) == -1) // if the character is not in the list destroy it
            {
                Destroy(charParty.gameObject);
                break;
            }
            // disables the button if the character is already in the party
            if (configsHandler.IsCharacterOnParty(charParty.character, selectedParty)) charParty.GetComponent<Button>().interactable = false;
            else charParty.GetComponent<Button>().interactable = true; // otherwise enables the button
        }
    }

    public int GetSelectedSlot(out bool isActive) // gets the selected slot and if is an active slot or stock slot
    {
        // gets the party object
        GameObject party = transform.parent.parent.GetChild(1).GetChild(0).GetChild(selectedParty).gameObject;

        // gets all the Selected Buttons Borders
        Image[] buttons = party.GetComponentsInChildren<Image>().Where(b => b.name == "SB").ToArray();
        int index = 0;

        isActive = false;

        if (buttons.FirstOrDefault(b => b.enabled)) // if there is a selected button
        {
            // gets the index of the selected button
            index = buttons.FirstOrDefault(b => b.enabled).transform.parent.GetSiblingIndex();
        }
        // if there ara no selected slots, it just uses the first slot in the stock party

        return index;
    }

    /// <summary>
    /// Toggle between virtualized and original system
    /// </summary>
    public void SetVirtualizationEnabled(bool enabled)
    {
        if (enabled != useVirtualization)
        {
            useVirtualization = enabled;

            if (enabled)
            {
                // Clear original system elements
                ClearOriginalSystemElements();
                InitializeVirtualization();
            }
            else
            {
                // Disable virtualization
                if (virtualizedList != null)
                    virtualizedList.enabled = false;
                originalSystemActive = true;
            }

            if (debugMode)
                Debug.Log($"[PARTY_UI_HANDLER] 🔄 Virtualization {(enabled ? "enabled" : "disabled")}");
        }
    }

    /// <summary>
    /// Clear elements created by the original system
    /// </summary>
    private void ClearOriginalSystemElements()
    {
        var existingElements = GetComponentsInChildren<CharacterValuesParty>();
        foreach (var element in existingElements)
        {
            if (element.gameObject != null)
                Destroy(element.gameObject);
        }
    }

    /// <summary>
    /// Force refresh of character list
    /// </summary>
    public void RefreshCharacterList()
    {
        if (useVirtualization && virtualizedList != null)
        {
            virtualizedList.ForceRefresh();
        }
        // Original system refreshes automatically in Update()
    }

    /// <summary>
    /// Get current system status
    /// </summary>
    public PartyUIStatus GetStatus()
    {
        return new PartyUIStatus
        {
            UseVirtualization = useVirtualization,
            VirtualizedListActive = virtualizedList != null && virtualizedList.enabled,
            OriginalSystemActive = originalSystemActive,
            SelectedParty = selectedParty,
            CharacterCount = useVirtualization && virtualizedList != null ?
                           virtualizedList.TotalElementCount :
                           GetComponentsInChildren<CharacterValuesParty>().Length
        };
    }
}

/// <summary>
/// Status information for the party UI handler
/// </summary>
public struct PartyUIStatus
{
    public bool UseVirtualization;
    public bool VirtualizedListActive;
    public bool OriginalSystemActive;
    public int SelectedParty;
    public int CharacterCount;

    public override string ToString()
    {
        return $"PartyUI Status - Virtualization: {UseVirtualization}, " +
               $"Active: {VirtualizedListActive}, Original: {OriginalSystemActive}, " +
               $"Party: {SelectedParty}, Characters: {CharacterCount}";
    }
}
