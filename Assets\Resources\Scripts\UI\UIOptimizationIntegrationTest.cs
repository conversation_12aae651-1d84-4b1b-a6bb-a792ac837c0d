using System.Collections;
using UnityEngine;

/// <summary>
/// Integration test to verify all UI optimization components work together
/// Tests the complete virtualized UI system with 618 characters
/// </summary>
public class UIOptimizationIntegrationTest : MonoBehaviour
{
    [Header("Integration Test Settings")]
    [SerializeField] private bool runTestOnStart = false;
    [SerializeField] private bool enableDetailedLogging = true;
    
    // Component references
    private ConfigsHandler configsHandler;
    private VirtualizedCharacterList virtualizedList;
    private VirtualizedAddCharacter virtualizedAddChar;
    private CachedCharacterFilter characterFilter;
    private UIPerformanceTest performanceTest;
    
    void Start()
    {
        if (runTestOnStart)
        {
            StartCoroutine(RunIntegrationTest());
        }
    }
    
    /// <summary>
    /// Runs comprehensive integration test of all UI optimization components
    /// </summary>
    public IEnumerator RunIntegrationTest()
    {
        Debug.Log("=== UI OPTIMIZATION INTEGRATION TEST ===");
        
        // Test 1: Component Discovery
        yield return StartCoroutine(TestComponentDiscovery());
        
        // Test 2: Virtualized System Integration
        yield return StartCoroutine(TestVirtualizedSystemIntegration());
        
        // Test 3: Cached Filtering Integration
        yield return StartCoroutine(TestCachedFilteringIntegration());
        
        // Test 4: Performance Verification
        yield return StartCoroutine(TestPerformanceVerification());
        
        // Test 5: Backward Compatibility
        yield return StartCoroutine(TestBackwardCompatibility());
        
        Debug.Log("=== INTEGRATION TEST COMPLETED ===");
        GenerateIntegrationReport();
    }
    
    /// <summary>
    /// Tests that all required components are properly discovered
    /// </summary>
    private IEnumerator TestComponentDiscovery()
    {
        Debug.Log("[INTEGRATION] 🔍 Testing component discovery...");
        
        // Find ConfigsHandler
        configsHandler = GameObject.Find("GameConfigsHandler")?.GetComponent<ConfigsHandler>();
        if (configsHandler == null)
        {
            Debug.LogError("[INTEGRATION] ❌ ConfigsHandler not found!");
            yield break;
        }
        
        // Find or create virtualized components
        GameObject charactersInfo = GameObject.Find("CharactersInfo");
        if (charactersInfo != null)
        {
            virtualizedList = charactersInfo.GetComponent<VirtualizedCharacterList>();
            virtualizedAddChar = charactersInfo.GetComponent<VirtualizedAddCharacter>();
            characterFilter = FindObjectOfType<CachedCharacterFilter>();
        }
        
        Debug.Log($"[INTEGRATION] ✅ Component discovery completed:");
        Debug.Log($"  ConfigsHandler: {(configsHandler != null ? "✅" : "❌")}");
        Debug.Log($"  VirtualizedCharacterList: {(virtualizedList != null ? "✅" : "❌")}");
        Debug.Log($"  VirtualizedAddCharacter: {(virtualizedAddChar != null ? "✅" : "❌")}");
        Debug.Log($"  CachedCharacterFilter: {(characterFilter != null ? "✅" : "❌")}");
        
        yield return null;
    }
    
    /// <summary>
    /// Tests virtualized system integration
    /// </summary>
    private IEnumerator TestVirtualizedSystemIntegration()
    {
        Debug.Log("[INTEGRATION] 🔗 Testing virtualized system integration...");
        
        if (configsHandler == null)
        {
            Debug.LogError("[INTEGRATION] ❌ Cannot test without ConfigsHandler");
            yield break;
        }
        
        var characters = configsHandler.GetCharacters();
        Debug.Log($"[INTEGRATION] 📊 Testing with {characters.Count} characters");
        
        // Test virtualized list functionality
        if (virtualizedList != null)
        {
            virtualizedList.RefreshCharacterList();
            int activeElements = virtualizedList.GetActiveUIElementCount();
            int totalCharacters = virtualizedList.GetFilteredCharacterCount();
            
            Debug.Log($"[INTEGRATION] 📊 Virtualized List Results:");
            Debug.Log($"  Active UI Elements: {activeElements}");
            Debug.Log($"  Total Characters: {totalCharacters}");
            Debug.Log($"  Memory Reduction: {(1f - (float)activeElements / totalCharacters) * 100f:F1}%");
            
            // Test scrolling
            if (totalCharacters > 0)
            {
                virtualizedList.ScrollToIndex(totalCharacters / 2);
                yield return new WaitForSeconds(0.1f);
                virtualizedList.ScrollToIndex(0);
            }
        }
        
        yield return null;
    }
    
    /// <summary>
    /// Tests cached filtering integration
    /// </summary>
    private IEnumerator TestCachedFilteringIntegration()
    {
        Debug.Log("[INTEGRATION] 🗂️ Testing cached filtering integration...");
        
        if (characterFilter == null)
        {
            Debug.LogWarning("[INTEGRATION] ⚠️ CachedCharacterFilter not found - creating temporary instance");
            characterFilter = gameObject.AddComponent<CachedCharacterFilter>();
            yield return new WaitForSeconds(0.1f);
        }
        
        // Test cached filtering performance
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        
        for (int i = 0; i < 10; i++)
        {
            var nonEnemyCharacters = characterFilter.GetNonEnemyCharacters();
            var allCharacters = characterFilter.GetAllCharacters();
        }
        
        stopwatch.Stop();
        
        Debug.Log($"[INTEGRATION] ✅ Cached filtering test completed:");
        Debug.Log($"  10 filter operations in {stopwatch.ElapsedMilliseconds}ms");
        Debug.Log($"  Cache hit rate: {characterFilter.GetCacheHitRate() * 100f:F1}%");
        
        // Test cache invalidation
        characterFilter.ForceUpdateCache();
        Debug.Log($"  Cache update forced successfully");
        
        yield return null;
    }
    
    /// <summary>
    /// Tests performance verification
    /// </summary>
    private IEnumerator TestPerformanceVerification()
    {
        Debug.Log("[INTEGRATION] ⚡ Testing performance verification...");
        
        // Create performance test if not exists
        performanceTest = FindObjectOfType<UIPerformanceTest>();
        if (performanceTest == null)
        {
            performanceTest = gameObject.AddComponent<UIPerformanceTest>();
        }
        
        // Run quick performance check
        float startTime = Time.realtimeSinceStartup;
        float initialFPS = 1f / Time.unscaledDeltaTime;
        
        // Simulate UI operations
        for (int i = 0; i < 5; i++)
        {
            if (virtualizedList != null)
            {
                virtualizedList.ForceRefresh();
                virtualizedList.ScrollToIndex(Random.Range(0, virtualizedList.GetFilteredCharacterCount()));
            }
            yield return new WaitForSeconds(0.1f);
        }
        
        float endTime = Time.realtimeSinceStartup;
        float finalFPS = 1f / Time.unscaledDeltaTime;
        float operationTime = (endTime - startTime) * 1000f;
        
        Debug.Log($"[INTEGRATION] ✅ Performance verification completed:");
        Debug.Log($"  Initial FPS: {initialFPS:F1}");
        Debug.Log($"  Final FPS: {finalFPS:F1}");
        Debug.Log($"  Operation time: {operationTime:F2}ms");
        Debug.Log($"  FPS stability: {(Mathf.Abs(finalFPS - initialFPS) < 5f ? "EXCELLENT" : "GOOD")}");
        
        yield return null;
    }
    
    /// <summary>
    /// Tests backward compatibility
    /// </summary>
    private IEnumerator TestBackwardCompatibility()
    {
        Debug.Log("[INTEGRATION] 🔄 Testing backward compatibility...");
        
        if (configsHandler == null)
        {
            Debug.LogError("[INTEGRATION] ❌ Cannot test backward compatibility without ConfigsHandler");
            yield break;
        }
        
        // Test character access methods
        var characters = configsHandler.GetCharacters();
        if (characters.Count > 0)
        {
            var firstCharacter = configsHandler.GetCharacter(0);
            var characterById = configsHandler.GetCharacterByID(firstCharacter.id);
            
            bool characterAccessWorking = firstCharacter != null && characterById != null && firstCharacter.id == characterById.id;
            Debug.Log($"  Character access methods: {(characterAccessWorking ? "✅" : "❌")}");
        }
        
        // Test character modification methods
        if (characters.Count > 0)
        {
            var testCharacter = characters[0];
            configsHandler.MarkCharacterModified(testCharacter);
            configsHandler.MarkCharacterModified(0);
            
            Debug.Log($"  Character modification methods: ✅");
        }
        
        // Test party management (if available)
        try
        {
            var partyHandler = FindObjectOfType<CharatersForPartyUIHandler>();
            if (partyHandler != null)
            {
                Debug.Log($"  Party management integration: ✅");
            }
            else
            {
                Debug.Log($"  Party management integration: ⚠️ (Not found, but not required)");
            }
        }
        catch (System.Exception e)
        {
            Debug.LogWarning($"[INTEGRATION] ⚠️ Party management test failed: {e.Message}");
        }
        
        Debug.Log("[INTEGRATION] ✅ Backward compatibility verification completed");
        
        yield return null;
    }
    
    /// <summary>
    /// Generates comprehensive integration report
    /// </summary>
    private void GenerateIntegrationReport()
    {
        Debug.Log("=== UI OPTIMIZATION INTEGRATION REPORT ===");
        
        // System Status
        Debug.Log("📊 SYSTEM STATUS:");
        Debug.Log($"  ConfigsHandler: {(configsHandler != null ? "✅ ACTIVE" : "❌ MISSING")}");
        Debug.Log($"  VirtualizedCharacterList: {(virtualizedList != null ? "✅ ACTIVE" : "❌ MISSING")}");
        Debug.Log($"  VirtualizedAddCharacter: {(virtualizedAddChar != null ? "✅ ACTIVE" : "❌ MISSING")}");
        Debug.Log($"  CachedCharacterFilter: {(characterFilter != null ? "✅ ACTIVE" : "❌ MISSING")}");
        
        // Performance Metrics
        if (configsHandler != null && virtualizedList != null)
        {
            int totalCharacters = configsHandler.GetCharacters().Count;
            int activeUIElements = virtualizedList.GetActiveUIElementCount();
            float memoryReduction = totalCharacters > 0 ? (1f - (float)activeUIElements / totalCharacters) * 100f : 0f;
            
            Debug.Log("📊 PERFORMANCE METRICS:");
            Debug.Log($"  Total Characters: {totalCharacters}");
            Debug.Log($"  Active UI Elements: {activeUIElements}");
            Debug.Log($"  Memory Reduction: {memoryReduction:F1}%");
            Debug.Log($"  Target Achievement: {(memoryReduction >= 95f ? "✅ ACHIEVED" : "❌ NOT ACHIEVED")}");
        }
        
        // Cache Performance
        if (characterFilter != null)
        {
            Debug.Log("📊 CACHE PERFORMANCE:");
            Debug.Log($"  Cache Hit Rate: {characterFilter.GetCacheHitRate() * 100f:F1}%");
            Debug.Log($"  Cache Efficiency: {(characterFilter.GetCacheHitRate() > 0.8f ? "✅ EXCELLENT" : "⚠️ NEEDS IMPROVEMENT")}");
        }
        
        // Overall Status
        bool systemWorking = configsHandler != null && virtualizedList != null;
        Debug.Log($"📊 OVERALL STATUS: {(systemWorking ? "✅ SYSTEM OPERATIONAL" : "❌ SYSTEM ISSUES DETECTED")}");
        
        Debug.Log("=== END INTEGRATION REPORT ===");
    }
    
    /// <summary>
    /// Manual test trigger
    /// </summary>
    [ContextMenu("Run Integration Test")]
    public void RunTestManually()
    {
        StartCoroutine(RunIntegrationTest());
    }
    
    /// <summary>
    /// Quick status check
    /// </summary>
    [ContextMenu("Quick Status Check")]
    public void QuickStatusCheck()
    {
        StartCoroutine(TestComponentDiscovery());
    }
}
