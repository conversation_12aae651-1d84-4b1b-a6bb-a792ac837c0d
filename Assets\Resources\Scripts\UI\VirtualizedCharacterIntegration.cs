using UnityEngine;
using System.Collections.Generic;

public class VirtualizedCharacterIntegration : MonoBehaviour
{
    [Header("System Configuration")]
    public bool useVirtualizedSystem = true;
    public VirtualizedCharacterList virtualizedList;
    
    [Header("Legacy System References")]
    public Transform legacyCharactersParent; // "CharactersInfo" GameObject
    
    private ConfigsHandler configsHandler;
    private bool isInitialized = false;
    
    // Track legacy UI elements for cleanup
    private List<GameObject> legacyCharacterUIs = new List<GameObject>();
    
    void Awake()
    {
        // Find required components
        configsHandler = FindFirstObjectByType<ConfigsHandler>();
        
        if (virtualizedList == null)
        {
            virtualizedList = GetComponent<VirtualizedCharacterList>();
        }
        
        if (legacyCharactersParent == null)
        {
            GameObject legacyParent = GameObject.Find("CharactersInfo");
            if (legacyParent != null)
            {
                legacyCharactersParent = legacyParent.transform;
            }
        }
    }
    
    void Start()
    {
        // Initialize the integration after all components are ready
        if (configsHandler != null)
        {
            InitializeIntegration();
        }
        else
        {
            Debug.LogError("VirtualizedCharacterIntegration: ConfigsHandler not found!");
        }
    }
    
    void InitializeIntegration()
    {
        if (isInitialized) return;
        
        if (useVirtualizedSystem)
        {
            // Clean up any existing legacy UI elements
            CleanupLegacyUI();
            
            // Initialize the virtualized system
            if (virtualizedList != null)
            {
                virtualizedList.InitializeVirtualizedList();
                Debug.Log("VirtualizedCharacterIntegration: Virtualized system initialized");
            }
            else
            {
                Debug.LogError("VirtualizedCharacterIntegration: VirtualizedCharacterList not found!");
                return;
            }
        }
        else
        {
            // Use legacy system - create individual CharConfUI instances
            InitializeLegacySystem();
        }
        
        isInitialized = true;
    }
    
    void CleanupLegacyUI()
    {
        if (legacyCharactersParent == null) return;
        
        // Find and destroy all existing character UI elements
        CharConfUI[] existingUIs = legacyCharactersParent.GetComponentsInChildren<CharConfUI>();
        
        foreach (CharConfUI ui in existingUIs)
        {
            if (ui != null && ui.gameObject != null)
            {
                DestroyImmediate(ui.gameObject);
            }
        }
        
        // Clear the tracking list
        legacyCharacterUIs.Clear();
        
        Debug.Log($"VirtualizedCharacterIntegration: Cleaned up {existingUIs.Length} legacy UI elements");
    }
    
    void InitializeLegacySystem()
    {
        if (configsHandler == null || configsHandler.GetCharacters() == null) return;

        // Create legacy UI elements using the original AddCharacter system
        var characters = configsHandler.GetCharacters();
        for (int i = 0; i < characters.Count; i++)
        {
            AddCharacter addChar = new AddCharacter(i, configsHandler);
            // The AddCharacter constructor handles the UI creation
        }

        Debug.Log($"VirtualizedCharacterIntegration: Legacy system initialized with {characters.Count} characters");
    }
    
    // Public interface for ConfigsHandler integration
    public void OnCharacterAdded(BattleCharacter character)
    {
        if (!isInitialized) return;
        
        if (useVirtualizedSystem && virtualizedList != null)
        {
            // Refresh the virtualized list to include the new character
            virtualizedList.RefreshCharacterList();
            
            // Optionally scroll to the new character
            int characterIndex = configsHandler.GetCharacters().IndexOf(character);
            if (characterIndex >= 0)
            {
                virtualizedList.ScrollToCharacter(characterIndex);
            }
        }
        else
        {
            // Handle legacy system character addition
            int characterIndex = configsHandler.GetCharacters().IndexOf(character);
            if (characterIndex >= 0)
            {
                AddCharacter addChar = new AddCharacter(characterIndex, configsHandler);
            }
        }
    }
    
    public void OnCharacterRemoved(BattleCharacter character)
    {
        if (!isInitialized) return;
        
        if (useVirtualizedSystem && virtualizedList != null)
        {
            // Refresh the virtualized list to remove the character
            virtualizedList.RefreshCharacterList();
        }
        else
        {
            // Legacy system handles removal through CharConfUI.Update() -> Destroy()
            // No additional action needed
        }
    }
    
    public void OnCharacterListChanged()
    {
        if (!isInitialized) return;
        
        if (useVirtualizedSystem && virtualizedList != null)
        {
            virtualizedList.RefreshCharacterList();
        }
        else
        {
            // For legacy system, recreate all UI elements
            CleanupLegacyUI();
            InitializeLegacySystem();
        }
    }
    
    // System switching (for testing/debugging)
    public void SwitchToVirtualizedSystem()
    {
        if (useVirtualizedSystem) return;
        
        useVirtualizedSystem = true;
        CleanupLegacyUI();
        
        if (virtualizedList != null)
        {
            virtualizedList.InitializeVirtualizedList();
        }
        
        Debug.Log("VirtualizedCharacterIntegration: Switched to virtualized system");
    }
    
    public void SwitchToLegacySystem()
    {
        if (!useVirtualizedSystem) return;
        
        useVirtualizedSystem = false;
        
        // Disable virtualized system
        if (virtualizedList != null)
        {
            virtualizedList.gameObject.SetActive(false);
        }
        
        InitializeLegacySystem();
        
        Debug.Log("VirtualizedCharacterIntegration: Switched to legacy system");
    }
    
    // Performance monitoring
    public void GetPerformanceStats(out int activeUIElements, out int totalCharacters)
    {
        totalCharacters = configsHandler?.characters?.Count ?? 0;
        
        if (useVirtualizedSystem && virtualizedList != null)
        {
            // Count active virtualized items
            activeUIElements = virtualizedList.GetActiveItemCount();
        }
        else
        {
            // Count legacy UI elements
            if (legacyCharactersParent != null)
            {
                activeUIElements = legacyCharactersParent.GetComponentsInChildren<CharConfUI>().Length;
            }
            else
            {
                activeUIElements = 0;
            }
        }
    }
    
    // Debugging and validation
    void OnValidate()
    {
        if (virtualizedList == null)
        {
            virtualizedList = GetComponent<VirtualizedCharacterList>();
        }
    }
    
    // Public properties for external access
    public bool IsUsingVirtualizedSystem => useVirtualizedSystem;
    public bool IsInitialized => isInitialized;
    public int CharacterCount => configsHandler?.characters?.Count ?? 0;
    
    // Event handlers for ConfigsHandler integration
    public void HandleCharacterDataChanged(BattleCharacter character)
    {
        if (useVirtualizedSystem && virtualizedList != null)
        {
            // The virtualized system automatically handles data changes through CharConfUI
            // No additional action needed as the UI updates through existing mechanisms
        }
    }
    
    public void HandleCharacterOrderChanged()
    {
        if (useVirtualizedSystem && virtualizedList != null)
        {
            virtualizedList.RefreshCharacterList();
        }
        else
        {
            // Legacy system needs full recreation for order changes
            OnCharacterListChanged();
        }
    }
}
