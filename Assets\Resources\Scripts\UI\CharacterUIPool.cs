using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// Object pooling system for character UI elements
/// Manages reusable GameObjects to avoid constant instantiation/destruction
/// </summary>
public class CharacterUIPool : System.IDisposable
{
    private readonly Queue<GameObject> availableElements = new Queue<GameObject>();
    private readonly HashSet<GameObject> activeElements = new HashSet<GameObject>();
    private readonly Transform parentTransform;
    private readonly GameObject prefab;
    private readonly int maxPoolSize;
    
    // Performance tracking
    private int totalCreated = 0;
    private int totalReused = 0;
    
    /// <summary>
    /// Initialize the character UI pool
    /// </summary>
    /// <param name="initialSize">Initial number of elements to create</param>
    /// <param name="parent">Parent transform for UI elements</param>
    /// <param name="maxSize">Maximum pool size (0 = unlimited)</param>
    public CharacterUIPool(int initialSize, Transform parent, int maxSize = 0)
    {
        parentTransform = parent;
        maxPoolSize = maxSize;
        
        // Load the character UI prefab
        prefab = Resources.Load<GameObject>("Prefabs/CharacterValues");
        if (prefab == null)
        {
            Debug.LogError("[CHARACTER_UI_POOL] ❌ Failed to load CharacterValues prefab!");
            return;
        }
        
        // Pre-populate the pool
        for (int i = 0; i < initialSize; i++)
        {
            CreateNewElement();
        }
        
        Debug.Log($"[CHARACTER_UI_POOL] ✅ Initialized with {initialSize} elements");
    }
    
    /// <summary>
    /// Get an available UI element from the pool
    /// </summary>
    /// <returns>GameObject ready for use, or null if failed</returns>
    public GameObject GetElement()
    {
        GameObject element;
        
        if (availableElements.Count > 0)
        {
            // Reuse existing element
            element = availableElements.Dequeue();
            totalReused++;
        }
        else
        {
            // Create new element if pool is empty
            element = CreateNewElement();
            if (element == null) return null;
        }
        
        // Mark as active
        activeElements.Add(element);
        element.SetActive(true);
        
        return element;
    }
    
    /// <summary>
    /// Return an element to the pool for reuse
    /// </summary>
    /// <param name="element">GameObject to return to pool</param>
    public void ReturnElement(GameObject element)
    {
        if (element == null) return;
        
        // Remove from active set
        if (!activeElements.Remove(element))
        {
            Debug.LogWarning("[CHARACTER_UI_POOL] ⚠️ Attempted to return element not from this pool");
            return;
        }
        
        // Reset element state
        ResetElement(element);
        element.SetActive(false);
        
        // Return to pool if under max size
        if (maxPoolSize == 0 || availableElements.Count < maxPoolSize)
        {
            availableElements.Enqueue(element);
        }
        else
        {
            // Destroy excess elements
            Object.Destroy(element);
        }
    }
    
    /// <summary>
    /// Create a new UI element
    /// </summary>
    /// <returns>New GameObject or null if failed</returns>
    private GameObject CreateNewElement()
    {
        if (prefab == null || parentTransform == null) return null;
        
        try
        {
            GameObject newElement = Object.Instantiate(prefab, parentTransform);
            newElement.name = $"PooledCharacterUI_{totalCreated}";
            newElement.SetActive(false);
            
            // Initialize the element for pooling
            InitializeElementForPooling(newElement);
            
            totalCreated++;
            availableElements.Enqueue(newElement);
            
            return newElement;
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"[CHARACTER_UI_POOL] ❌ Failed to create element: {ex.Message}");
            return null;
        }
    }
    
    /// <summary>
    /// Initialize element for efficient pooling
    /// </summary>
    /// <param name="element">Element to initialize</param>
    private void InitializeElementForPooling(GameObject element)
    {
        // Get CharConfUI component and disable its Update method
        var charConfUI = element.GetComponent<CharConfUI>();
        if (charConfUI != null)
        {
            // We'll manage positioning manually in the virtualized list
            charConfUI.enabled = false;
        }
        
        // Ensure element starts in correct state
        ResetElement(element);
    }
    
    /// <summary>
    /// Reset element to default state for reuse
    /// </summary>
    /// <param name="element">Element to reset</param>
    private void ResetElement(GameObject element)
    {
        if (element == null) return;
        
        // Reset transform
        element.transform.localPosition = Vector3.zero;
        element.transform.localRotation = Quaternion.identity;
        element.transform.localScale = Vector3.one;
        
        // Reset CharConfUI component
        var charConfUI = element.GetComponent<CharConfUI>();
        if (charConfUI != null)
        {
            charConfUI.character = null;
            // Re-enable for configuration if needed
            charConfUI.enabled = false;
        }
        
        // Reset any UI state
        ResetUIComponents(element);
    }
    
    /// <summary>
    /// Reset UI components to default state
    /// </summary>
    /// <param name="element">Element containing UI components</param>
    private void ResetUIComponents(GameObject element)
    {
        // Reset button states
        var buttons = element.GetComponentsInChildren<UnityEngine.UI.Button>();
        foreach (var button in buttons)
        {
            button.interactable = true;
            // Reset any visual states
        }
        
        // Reset input fields
        var inputFields = element.GetComponentsInChildren<TMPro.TMP_InputField>();
        foreach (var inputField in inputFields)
        {
            inputField.text = "";
        }
        
        // Reset text components
        var textComponents = element.GetComponentsInChildren<TMPro.TextMeshProUGUI>();
        foreach (var text in textComponents)
        {
            text.text = "";
        }
    }
    
    /// <summary>
    /// Get current pool statistics
    /// </summary>
    /// <returns>Pool statistics for debugging</returns>
    public PoolStatistics GetStatistics()
    {
        return new PoolStatistics
        {
            TotalCreated = totalCreated,
            TotalReused = totalReused,
            AvailableCount = availableElements.Count,
            ActiveCount = activeElements.Count,
            ReuseRatio = totalCreated > 0 ? (float)totalReused / totalCreated : 0f
        };
    }
    
    /// <summary>
    /// Clear all elements from the pool
    /// </summary>
    public void Clear()
    {
        // Destroy all available elements
        while (availableElements.Count > 0)
        {
            var element = availableElements.Dequeue();
            if (element != null)
                Object.Destroy(element);
        }
        
        // Destroy all active elements
        foreach (var element in activeElements)
        {
            if (element != null)
                Object.Destroy(element);
        }
        
        activeElements.Clear();
        totalCreated = 0;
        totalReused = 0;
        
        Debug.Log("[CHARACTER_UI_POOL] 🧹 Pool cleared");
    }
    
    /// <summary>
    /// Dispose of the pool and clean up resources
    /// </summary>
    public void Dispose()
    {
        Clear();
    }
    
    /// <summary>
    /// Force return all active elements to the pool
    /// </summary>
    public void ReturnAllActiveElements()
    {
        var activeElementsCopy = new List<GameObject>(activeElements);
        foreach (var element in activeElementsCopy)
        {
            ReturnElement(element);
        }
    }
    
    /// <summary>
    /// Warm up the pool by pre-creating elements
    /// </summary>
    /// <param name="count">Number of elements to pre-create</param>
    public void WarmUp(int count)
    {
        for (int i = 0; i < count; i++)
        {
            CreateNewElement();
        }
        
        Debug.Log($"[CHARACTER_UI_POOL] 🔥 Warmed up with {count} additional elements");
    }
}

/// <summary>
/// Statistics for pool performance monitoring
/// </summary>
public struct PoolStatistics
{
    public int TotalCreated;
    public int TotalReused;
    public int AvailableCount;
    public int ActiveCount;
    public float ReuseRatio;
    
    public override string ToString()
    {
        return $"Pool Stats - Created: {TotalCreated}, Reused: {TotalReused}, " +
               $"Available: {AvailableCount}, Active: {ActiveCount}, " +
               $"Reuse Ratio: {ReuseRatio:P1}";
    }
}
