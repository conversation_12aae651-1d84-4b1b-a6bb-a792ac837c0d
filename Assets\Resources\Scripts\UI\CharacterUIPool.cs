using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// Object pool for character UI elements to optimize memory usage and performance
/// Reuses GameObjects instead of creating/destroying them
/// </summary>
public class CharacterUIPool
{
    private readonly GameObject prefab;
    private readonly Transform parent;
    private readonly int maxPoolSize;
    
    private readonly Queue<VirtualCharacterUIItem> availableItems = new Queue<VirtualCharacterUIItem>();
    private readonly List<VirtualCharacterUIItem> allItems = new List<VirtualCharacterUIItem>();
    
    private int createdCount = 0;
    
    public CharacterUIPool(GameObject prefab, Transform parent, int maxPoolSize = 20)
    {
        this.prefab = prefab;
        this.parent = parent;
        this.maxPoolSize = maxPoolSize;
        
        Debug.Log($"[CharacterUIPool] Initialized with max size: {maxPoolSize}");
    }
    
    /// <summary>
    /// Get a character UI item from the pool
    /// Creates new item if pool is empty and under max size
    /// </summary>
    public VirtualCharacterUIItem GetItem()
    {
        VirtualCharacterUIItem item = null;
        
        if (availableItems.Count > 0)
        {
            // Reuse existing item from pool
            item = availableItems.Dequeue();
            item.gameObject.SetActive(true);
        }
        else if (createdCount < maxPoolSize)
        {
            // Create new item if under max pool size
            item = CreateNewItem();
        }
        else
        {
            Debug.LogWarning($"[CharacterUIPool] Pool exhausted! Max size: {maxPoolSize}");
            return null;
        }
        
        return item;
    }
    
    /// <summary>
    /// Return a character UI item to the pool
    /// </summary>
    public void ReturnItem(VirtualCharacterUIItem item)
    {
        if (item == null) return;
        
        // Reset item state
        item.ResetState();
        item.gameObject.SetActive(false);
        
        // Add back to available pool
        availableItems.Enqueue(item);
    }
    
    /// <summary>
    /// Create a new character UI item
    /// </summary>
    private VirtualCharacterUIItem CreateNewItem()
    {
        GameObject itemObject = Object.Instantiate(prefab, parent);
        itemObject.name = $"PooledCharacterUI_{createdCount}";
        
        // Add or get the virtual UI item component
        VirtualCharacterUIItem item = itemObject.GetComponent<VirtualCharacterUIItem>();
        if (item == null)
        {
            item = itemObject.AddComponent<VirtualCharacterUIItem>();
        }
        
        // Initialize the item
        item.Initialize();
        
        allItems.Add(item);
        createdCount++;
        
        Debug.Log($"[CharacterUIPool] Created new item {createdCount}/{maxPoolSize}");
        
        return item;
    }
    
    /// <summary>
    /// Pre-warm the pool by creating initial items
    /// </summary>
    public void PreWarm(int count)
    {
        count = Mathf.Min(count, maxPoolSize);
        
        for (int i = 0; i < count; i++)
        {
            var item = CreateNewItem();
            ReturnItem(item);
        }
        
        Debug.Log($"[CharacterUIPool] Pre-warmed with {count} items");
    }
    
    /// <summary>
    /// Get current pool statistics
    /// </summary>
    public int GetPoolSize() => createdCount;
    public int GetAvailableCount() => availableItems.Count;
    public int GetActiveCount() => createdCount - availableItems.Count;
    
    /// <summary>
    /// Clear and dispose of all pooled items
    /// </summary>
    public void Dispose()
    {
        foreach (var item in allItems)
        {
            if (item != null && item.gameObject != null)
            {
                Object.Destroy(item.gameObject);
            }
        }
        
        availableItems.Clear();
        allItems.Clear();
        createdCount = 0;
        
        Debug.Log("[CharacterUIPool] Disposed all items");
    }
    
    /// <summary>
    /// Get detailed pool statistics
    /// </summary>
    public PoolStats GetStats()
    {
        return new PoolStats
        {
            MaxSize = maxPoolSize,
            CreatedCount = createdCount,
            AvailableCount = availableItems.Count,
            ActiveCount = GetActiveCount(),
            UtilizationPercent = (float)GetActiveCount() / maxPoolSize * 100f
        };
    }
}

/// <summary>
/// Statistics for the character UI pool
/// </summary>
[System.Serializable]
public struct PoolStats
{
    public int MaxSize;
    public int CreatedCount;
    public int AvailableCount;
    public int ActiveCount;
    public float UtilizationPercent;
    
    public override string ToString()
    {
        return $"Pool: {ActiveCount}/{CreatedCount}/{MaxSize} (Active/Created/Max), {UtilizationPercent:F1}% utilized";
    }
}
