using UnityEngine;

/// <summary>
/// Calculates visible elements and viewport positioning for virtualized lists
/// Handles viewport culling and element positioning calculations
/// </summary>
public class ViewportCalculator
{
    private readonly float elementHeight;
    private readonly RectTransform viewport;
    
    // Cached viewport properties
    private float viewportHeight;
    private float lastViewportHeight = -1f;
    
    // Performance optimization
    private VisibleRange lastCalculatedRange;
    private float lastScrollPosition = float.MinValue;
    private int lastTotalElements = -1;
    
    /// <summary>
    /// Initialize viewport calculator
    /// </summary>
    /// <param name="elementHeight">Height of each list element</param>
    /// <param name="viewport">Viewport RectTransform for calculations</param>
    public ViewportCalculator(float elementHeight, RectTransform viewport)
    {
        this.elementHeight = elementHeight;
        this.viewport = viewport;
        UpdateViewportHeight();
    }
    
    /// <summary>
    /// Calculate which elements should be visible based on scroll position
    /// </summary>
    /// <param name="scrollPosition">Current scroll position (Y coordinate)</param>
    /// <param name="totalElements">Total number of elements in the list</param>
    /// <returns>Range of visible element indices</returns>
    public VisibleRange CalculateVisibleRange(float scrollPosition, int totalElements)
    {
        // Check if we can use cached result
        if (Mathf.Approximately(scrollPosition, lastScrollPosition) && 
            totalElements == lastTotalElements)
        {
            return lastCalculatedRange;
        }
        
        UpdateViewportHeight();
        
        if (totalElements == 0 || elementHeight <= 0)
        {
            return new VisibleRange(0, -1, 0);
        }
        
        // Calculate visible range with buffer
        int elementsInViewport = Mathf.CeilToInt(viewportHeight / elementHeight);
        int bufferElements = 2; // Extra elements above and below for smooth scrolling
        
        // Calculate first visible element index
        int firstVisibleIndex = Mathf.FloorToInt(scrollPosition / elementHeight);
        firstVisibleIndex = Mathf.Max(0, firstVisibleIndex - bufferElements);
        
        // Calculate last visible element index
        int lastVisibleIndex = firstVisibleIndex + elementsInViewport + (bufferElements * 2);
        lastVisibleIndex = Mathf.Min(totalElements - 1, lastVisibleIndex);
        
        // Ensure valid range
        if (firstVisibleIndex > lastVisibleIndex)
        {
            firstVisibleIndex = lastVisibleIndex = 0;
        }
        
        int visibleCount = lastVisibleIndex - firstVisibleIndex + 1;
        
        // Cache the result
        lastCalculatedRange = new VisibleRange(firstVisibleIndex, lastVisibleIndex, visibleCount);
        lastScrollPosition = scrollPosition;
        lastTotalElements = totalElements;
        
        return lastCalculatedRange;
    }
    
    /// <summary>
    /// Calculate the world position for an element at given index
    /// </summary>
    /// <param name="elementIndex">Index of the element</param>
    /// <param name="parentTransform">Parent transform for positioning</param>
    /// <returns>World position for the element</returns>
    public Vector3 CalculateElementPosition(int elementIndex, Transform parentTransform)
    {
        if (parentTransform == null) return Vector3.zero;
        
        float yOffset = -elementIndex * elementHeight;
        return parentTransform.position + new Vector3(0, yOffset, 0);
    }
    
    /// <summary>
    /// Calculate local position for an element at given index
    /// </summary>
    /// <param name="elementIndex">Index of the element</param>
    /// <returns>Local position for the element</returns>
    public Vector3 CalculateElementLocalPosition(int elementIndex)
    {
        float yOffset = -elementIndex * elementHeight;
        return new Vector3(0, yOffset, 0);
    }
    
    /// <summary>
    /// Calculate the total content height for all elements
    /// </summary>
    /// <param name="totalElements">Total number of elements</param>
    /// <returns>Total height needed for all elements</returns>
    public float CalculateContentHeight(int totalElements)
    {
        return totalElements * elementHeight;
    }
    
    /// <summary>
    /// Calculate scroll position to center an element in the viewport
    /// </summary>
    /// <param name="elementIndex">Index of element to center</param>
    /// <param name="totalElements">Total number of elements</param>
    /// <returns>Scroll position to center the element</returns>
    public float CalculateScrollPositionToCenter(int elementIndex, int totalElements)
    {
        UpdateViewportHeight();
        
        float elementPosition = elementIndex * elementHeight;
        float centerOffset = viewportHeight * 0.5f - elementHeight * 0.5f;
        float targetScrollPosition = elementPosition - centerOffset;
        
        // Clamp to valid scroll range
        float maxScrollPosition = CalculateMaxScrollPosition(totalElements);
        return Mathf.Clamp(targetScrollPosition, 0f, maxScrollPosition);
    }
    
    /// <summary>
    /// Calculate the maximum valid scroll position
    /// </summary>
    /// <param name="totalElements">Total number of elements</param>
    /// <returns>Maximum scroll position</returns>
    public float CalculateMaxScrollPosition(int totalElements)
    {
        UpdateViewportHeight();
        
        float contentHeight = CalculateContentHeight(totalElements);
        return Mathf.Max(0f, contentHeight - viewportHeight);
    }
    
    /// <summary>
    /// Check if an element at given index is currently visible
    /// </summary>
    /// <param name="elementIndex">Index to check</param>
    /// <param name="scrollPosition">Current scroll position</param>
    /// <param name="totalElements">Total number of elements</param>
    /// <returns>True if element is visible</returns>
    public bool IsElementVisible(int elementIndex, float scrollPosition, int totalElements)
    {
        var visibleRange = CalculateVisibleRange(scrollPosition, totalElements);
        return elementIndex >= visibleRange.firstIndex && elementIndex <= visibleRange.lastIndex;
    }
    
    /// <summary>
    /// Calculate scroll percentage (0-1) based on position
    /// </summary>
    /// <param name="scrollPosition">Current scroll position</param>
    /// <param name="totalElements">Total number of elements</param>
    /// <returns>Scroll percentage from 0 to 1</returns>
    public float CalculateScrollPercentage(float scrollPosition, int totalElements)
    {
        float maxScrollPosition = CalculateMaxScrollPosition(totalElements);
        if (maxScrollPosition <= 0f) return 0f;
        
        return Mathf.Clamp01(scrollPosition / maxScrollPosition);
    }
    
    /// <summary>
    /// Calculate scroll position from percentage (0-1)
    /// </summary>
    /// <param name="percentage">Scroll percentage from 0 to 1</param>
    /// <param name="totalElements">Total number of elements</param>
    /// <returns>Scroll position</returns>
    public float CalculateScrollPositionFromPercentage(float percentage, int totalElements)
    {
        float maxScrollPosition = CalculateMaxScrollPosition(totalElements);
        return Mathf.Clamp(percentage * maxScrollPosition, 0f, maxScrollPosition);
    }
    
    /// <summary>
    /// Get the element index at a specific scroll position
    /// </summary>
    /// <param name="scrollPosition">Scroll position to check</param>
    /// <returns>Element index at the top of the viewport</returns>
    public int GetElementIndexAtScrollPosition(float scrollPosition)
    {
        return Mathf.FloorToInt(scrollPosition / elementHeight);
    }
    
    /// <summary>
    /// Update cached viewport height
    /// </summary>
    private void UpdateViewportHeight()
    {
        if (viewport != null)
        {
            float currentHeight = viewport.rect.height;
            if (!Mathf.Approximately(currentHeight, lastViewportHeight))
            {
                viewportHeight = currentHeight;
                lastViewportHeight = currentHeight;
                
                // Invalidate cached calculations when viewport changes
                lastScrollPosition = float.MinValue;
                lastTotalElements = -1;
            }
        }
        else
        {
            // Fallback to screen height if no viewport
            viewportHeight = Screen.height;
        }
    }
    
    /// <summary>
    /// Get current viewport dimensions
    /// </summary>
    /// <returns>Viewport dimensions</returns>
    public ViewportDimensions GetViewportDimensions()
    {
        UpdateViewportHeight();
        
        return new ViewportDimensions
        {
            Height = viewportHeight,
            Width = viewport?.rect.width ?? Screen.width,
            ElementHeight = elementHeight,
            ElementsInViewport = Mathf.CeilToInt(viewportHeight / elementHeight)
        };
    }
    
    /// <summary>
    /// Reset cached calculations (call when list changes significantly)
    /// </summary>
    public void ResetCache()
    {
        lastScrollPosition = float.MinValue;
        lastTotalElements = -1;
        lastViewportHeight = -1f;
    }
}

/// <summary>
/// Represents a range of visible elements
/// </summary>
public struct VisibleRange
{
    public readonly int firstIndex;
    public readonly int lastIndex;
    public readonly int count;
    
    public VisibleRange(int firstIndex, int lastIndex, int count)
    {
        this.firstIndex = firstIndex;
        this.lastIndex = lastIndex;
        this.count = count;
    }
    
    public bool IsValid => firstIndex >= 0 && lastIndex >= firstIndex;
    
    public bool Contains(int index)
    {
        return index >= firstIndex && index <= lastIndex;
    }
    
    public override string ToString()
    {
        return $"VisibleRange({firstIndex}-{lastIndex}, count: {count})";
    }
}

/// <summary>
/// Viewport dimension information
/// </summary>
public struct ViewportDimensions
{
    public float Height;
    public float Width;
    public float ElementHeight;
    public int ElementsInViewport;
    
    public override string ToString()
    {
        return $"Viewport({Width}x{Height}, ElementHeight: {ElementHeight}, Elements: {ElementsInViewport})";
    }
}
