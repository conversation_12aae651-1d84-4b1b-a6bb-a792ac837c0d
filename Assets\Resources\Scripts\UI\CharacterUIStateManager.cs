using System.Collections.Generic;
using UnityEngine;
using TMPro;

[System.Serializable]
public class CharacterUIState
{
    public string characterId;
    public bool isStatsOpen;
    public bool isSkillsOpen;
    public bool isModsOpen;
    public bool isSelected;
    public string nameFieldText;
    public string levelFieldText;
    public bool nameFieldFocused;
    public bool levelFieldFocused;
    public Vector2 scrollPosition;
    
    // Stats panel state
    public bool statsRandomizePressed;
    public bool statsPastePressed;
    
    // Skills panel state
    public bool skillsRandomizePressed;
    public bool skillsPastePressed;
    
    // Mods panel state
    public bool modsRandomizePressed;
    public bool modsPastePressed;
    
    public CharacterUIState(string id)
    {
        characterId = id;
        isStatsOpen = false;
        isSkillsOpen = false;
        isModsOpen = false;
        isSelected = false;
        nameFieldText = "";
        levelFieldText = "";
        nameFieldFocused = false;
        levelFieldFocused = false;
        scrollPosition = Vector2.zero;
        statsRandomizePressed = false;
        statsPastePressed = false;
        skillsRandomizePressed = false;
        skillsPastePressed = false;
        modsRandomizePressed = false;
        modsPastePressed = false;
    }
}

public class CharacterUIStateManager : MonoBehaviour
{
    private static CharacterUIStateManager _instance;
    public static CharacterUIStateManager Instance
    {
        get
        {
            if (_instance == null)
            {
                _instance = FindObjectOfType<CharacterUIStateManager>();
                if (_instance == null)
                {
                    GameObject go = new GameObject("CharacterUIStateManager");
                    _instance = go.AddComponent<CharacterUIStateManager>();
                    DontDestroyOnLoad(go);
                }
            }
            return _instance;
        }
    }
    
    [Header("State Management")]
    public int maxStatesStored = 100; // Limit memory usage
    
    private Dictionary<string, CharacterUIState> characterStates;
    private string currentSelectedCharacterId;
    private GameObject currentOpenPanel; // Stats, Skills, or Mods panel
    
    // Panel references for state restoration
    private GameObject statsPanel;
    private GameObject skillsPanel;
    private GameObject modsPanel;
    
    void Awake()
    {
        if (_instance == null)
        {
            _instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeStateManager();
        }
        else if (_instance != this)
        {
            Destroy(gameObject);
        }
    }
    
    void InitializeStateManager()
    {
        characterStates = new Dictionary<string, CharacterUIState>();
        
        // Find panel references
        FindPanelReferences();
    }
    
    void FindPanelReferences()
    {
        // Find the Stats, Skills, and Mods panels
        statsPanel = GameObject.Find("StatsValues");
        skillsPanel = GameObject.Find("SkillsValues");
        modsPanel = GameObject.Find("ModsValues");
        
        if (statsPanel == null) Debug.LogWarning("CharacterUIStateManager: StatsValues panel not found");
        if (skillsPanel == null) Debug.LogWarning("CharacterUIStateManager: SkillsValues panel not found");
        if (modsPanel == null) Debug.LogWarning("CharacterUIStateManager: ModsValues panel not found");
    }
    
    // Save character UI state
    public void SaveCharacterState(string characterId, VirtualizedCharacterItem item)
    {
        if (string.IsNullOrEmpty(characterId) || item == null) return;
        
        CharacterUIState state = GetOrCreateState(characterId);
        CharConfUI charConfUI = item.GetCharConfUI();
        
        if (charConfUI != null)
        {
            // Save input field states
            SaveInputFieldStates(state, charConfUI);
            
            // Save panel states
            SavePanelStates(state);
            
            // Save selection state
            state.isSelected = (currentSelectedCharacterId == characterId);
        }
        
        // Limit memory usage
        LimitStoredStates();
    }
    
    void SaveInputFieldStates(CharacterUIState state, CharConfUI charConfUI)
    {
        // Use reflection to access private fields
        var nameField = GetCharConfUIField<TMP_InputField>(charConfUI, "nameI");
        var levelField = GetCharConfUIField<TMP_InputField>(charConfUI, "levelI");
        
        if (nameField != null)
        {
            state.nameFieldText = nameField.text;
            state.nameFieldFocused = nameField.isFocused;
        }
        
        if (levelField != null)
        {
            state.levelFieldText = levelField.text;
            state.levelFieldFocused = levelField.isFocused;
        }
    }
    
    void SavePanelStates(CharacterUIState state)
    {
        state.isStatsOpen = (statsPanel != null && statsPanel.activeInHierarchy);
        state.isSkillsOpen = (skillsPanel != null && skillsPanel.activeInHierarchy);
        state.isModsOpen = (modsPanel != null && modsPanel.activeInHierarchy);
        
        // Track which panel is currently open
        if (state.isStatsOpen) currentOpenPanel = statsPanel;
        else if (state.isSkillsOpen) currentOpenPanel = skillsPanel;
        else if (state.isModsOpen) currentOpenPanel = modsPanel;
        else currentOpenPanel = null;
    }
    
    // Restore character UI state
    public void RestoreCharacterState(string characterId, VirtualizedCharacterItem item)
    {
        if (string.IsNullOrEmpty(characterId) || item == null) return;
        
        if (characterStates.TryGetValue(characterId, out CharacterUIState state))
        {
            CharConfUI charConfUI = item.GetCharConfUI();
            
            if (charConfUI != null)
            {
                // Restore input field states
                RestoreInputFieldStates(state, charConfUI);
                
                // Restore selection state
                if (state.isSelected)
                {
                    SetSelectedCharacter(characterId);
                }
                
                // Restore panel states (with delay to ensure UI is ready)
                StartCoroutine(RestorePanelStatesDelayed(state, charConfUI));
            }
        }
    }
    
    void RestoreInputFieldStates(CharacterUIState state, CharConfUI charConfUI)
    {
        var nameField = GetCharConfUIField<TMP_InputField>(charConfUI, "nameI");
        var levelField = GetCharConfUIField<TMP_InputField>(charConfUI, "levelI");
        
        if (nameField != null && !string.IsNullOrEmpty(state.nameFieldText))
        {
            nameField.text = state.nameFieldText;
            if (state.nameFieldFocused)
            {
                nameField.Select();
            }
        }
        
        if (levelField != null && !string.IsNullOrEmpty(state.levelFieldText))
        {
            levelField.text = state.levelFieldText;
            if (state.levelFieldFocused)
            {
                levelField.Select();
            }
        }
    }
    
    System.Collections.IEnumerator RestorePanelStatesDelayed(CharacterUIState state, CharConfUI charConfUI)
    {
        yield return null; // Wait one frame for UI to be ready
        
        // Close any currently open panels first
        CloseAllPanels();
        
        // Open the appropriate panel if one was open
        if (state.isStatsOpen)
        {
            OpenStatsPanel(charConfUI);
        }
        else if (state.isSkillsOpen)
        {
            OpenSkillsPanel(charConfUI);
        }
        else if (state.isModsOpen)
        {
            OpenModsPanel(charConfUI);
        }
    }
    
    void OpenStatsPanel(CharConfUI charConfUI)
    {
        // Simulate stats button click
        var statsButton = GetCharConfUIField<UnityEngine.UI.Button>(charConfUI, "statsB");
        if (statsButton != null)
        {
            statsButton.onClick.Invoke();
        }
    }
    
    void OpenSkillsPanel(CharConfUI charConfUI)
    {
        // Simulate skills button click
        var skillsButton = GetCharConfUIField<UnityEngine.UI.Button>(charConfUI, "skillsB");
        if (skillsButton != null)
        {
            skillsButton.onClick.Invoke();
        }
    }
    
    void OpenModsPanel(CharConfUI charConfUI)
    {
        // Simulate mods button click
        var modsButton = GetCharConfUIField<UnityEngine.UI.Button>(charConfUI, "modsB");
        if (modsButton != null)
        {
            modsButton.onClick.Invoke();
        }
    }
    
    void CloseAllPanels()
    {
        if (statsPanel != null) statsPanel.SetActive(false);
        if (skillsPanel != null) skillsPanel.SetActive(false);
        if (modsPanel != null) modsPanel.SetActive(false);
        currentOpenPanel = null;
    }
    
    // Helper method to access private fields using reflection
    T GetCharConfUIField<T>(CharConfUI charConfUI, string fieldName) where T : class
    {
        var field = typeof(CharConfUI).GetField(fieldName, 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        
        if (field != null)
        {
            return field.GetValue(charConfUI) as T;
        }
        
        return null;
    }
    
    // Public interface
    public void SetSelectedCharacter(string characterId)
    {
        // Clear previous selection
        if (!string.IsNullOrEmpty(currentSelectedCharacterId) && 
            characterStates.TryGetValue(currentSelectedCharacterId, out CharacterUIState prevState))
        {
            prevState.isSelected = false;
        }
        
        // Set new selection
        currentSelectedCharacterId = characterId;
        if (!string.IsNullOrEmpty(characterId))
        {
            CharacterUIState state = GetOrCreateState(characterId);
            state.isSelected = true;
        }
    }
    
    public string GetSelectedCharacterId()
    {
        return currentSelectedCharacterId;
    }
    
    public void ClearCharacterState(string characterId)
    {
        if (characterStates.ContainsKey(characterId))
        {
            characterStates.Remove(characterId);
        }
        
        if (currentSelectedCharacterId == characterId)
        {
            currentSelectedCharacterId = null;
        }
    }
    
    public void ClearAllStates()
    {
        characterStates.Clear();
        currentSelectedCharacterId = null;
        currentOpenPanel = null;
    }
    
    CharacterUIState GetOrCreateState(string characterId)
    {
        if (!characterStates.TryGetValue(characterId, out CharacterUIState state))
        {
            state = new CharacterUIState(characterId);
            characterStates[characterId] = state;
        }
        return state;
    }
    
    void LimitStoredStates()
    {
        if (characterStates.Count > maxStatesStored)
        {
            // Remove oldest states (simple FIFO approach)
            var keysToRemove = new List<string>();
            int removeCount = characterStates.Count - maxStatesStored;
            
            foreach (var key in characterStates.Keys)
            {
                if (removeCount <= 0) break;
                if (key != currentSelectedCharacterId) // Don't remove selected character state
                {
                    keysToRemove.Add(key);
                    removeCount--;
                }
            }
            
            foreach (var key in keysToRemove)
            {
                characterStates.Remove(key);
            }
        }
    }
    
    // Debug information
    public int GetStoredStateCount()
    {
        return characterStates.Count;
    }
}
