using UnityEngine;
using UnityEngine.UI;

[RequireComponent(typeof(ScrollRect))]
public class VirtualizedScrollSetup : MonoBehaviour
{
    [Header("Scroll Configuration")]
    public float scrollSensitivity = 1.0f;
    public bool enableInertia = true;
    public float decelerationRate = 0.135f;
    
    [Header("Compatibility")]
    public float legacyScrollBoundary = 0.45f; // matches ValuesScroll.pos
    
    private ScrollRect scrollRect;
    private Camera configsCamera;
    private VirtualizedCharacterList characterList;
    
    // Legacy scroll compatibility
    private bool useLegacyScrollBehavior = true;
    
    void Awake()
    {
        scrollRect = GetComponent<ScrollRect>();
        characterList = GetComponent<VirtualizedCharacterList>();
        
        // Configure ScrollRect for optimal performance
        ConfigureScrollRect();
    }
    
    void Start()
    {
        // Find the configs camera
        configsCamera = GameObject.Find("Configs Camera")?.GetComponent<Camera>();
        
        if (configsCamera == null)
        {
            Debug.LogWarning("VirtualizedScrollSetup: Configs Camera not found, using main camera");
            configsCamera = Camera.main;
        }
    }
    
    void ConfigureScrollRect()
    {
        if (scrollRect == null) return;
        
        // Configure for vertical scrolling only
        scrollRect.horizontal = false;
        scrollRect.vertical = true;
        
        // Set movement type for smooth scrolling
        scrollRect.movementType = ScrollRect.MovementType.Clamped;
        
        // Configure inertia
        scrollRect.inertia = enableInertia;
        scrollRect.decelerationRate = decelerationRate;
        
        // Set scroll sensitivity
        scrollRect.scrollSensitivity = scrollSensitivity;
        
        // Ensure proper viewport setup
        if (scrollRect.viewport == null)
        {
            // Create viewport if it doesn't exist
            CreateViewport();
        }
        
        // Ensure content is properly configured
        if (scrollRect.content == null && characterList != null)
        {
            scrollRect.content = characterList.content;
        }
    }
    
    void CreateViewport()
    {
        // Create a viewport GameObject if one doesn't exist
        GameObject viewportObj = new GameObject("Viewport");
        viewportObj.transform.SetParent(transform);
        
        // Add RectTransform and configure it
        RectTransform viewportRect = viewportObj.AddComponent<RectTransform>();
        viewportRect.anchorMin = Vector2.zero;
        viewportRect.anchorMax = Vector2.one;
        viewportRect.sizeDelta = Vector2.zero;
        viewportRect.anchoredPosition = Vector2.zero;
        
        // Add Mask component for clipping
        Mask mask = viewportObj.AddComponent<Mask>();
        mask.showMaskGraphic = false;
        
        // Add Image component (required for Mask)
        Image maskImage = viewportObj.AddComponent<Image>();
        maskImage.color = new Color(1, 1, 1, 0.01f); // Nearly transparent
        
        // Assign to ScrollRect
        scrollRect.viewport = viewportRect;
        
        Debug.Log("VirtualizedScrollSetup: Created viewport for ScrollRect");
    }
    
    void Update()
    {
        // Handle legacy scroll behavior for compatibility
        if (useLegacyScrollBehavior)
        {
            HandleLegacyScrollInput();
        }
    }
    
    void HandleLegacyScrollInput()
    {
        if (configsCamera == null || !configsCamera.enabled) return;
        
        // Handle touch input (mobile compatibility)
        if (Input.touchCount > 0)
        {
            Touch touch = Input.GetTouch(0);
            Vector2 viewportPoint = configsCamera.ScreenToViewportPoint(touch.position);
            
            // Only handle touch input in the designated scroll area
            if (viewportPoint.y < legacyScrollBoundary)
            {
                HandleTouchScroll(touch);
            }
        }
        
        // Handle mouse scroll wheel (desktop compatibility)
        if (Input.mousePosition.x / configsCamera.pixelWidth > legacyScrollBoundary)
        {
            float scrollDelta = Input.GetAxis("Mouse ScrollWheel");
            if (Mathf.Abs(scrollDelta) > 0.01f)
            {
                HandleMouseScroll(scrollDelta);
            }
        }
    }
    
    void HandleTouchScroll(Touch touch)
    {
        if (touch.phase == TouchPhase.Moved)
        {
            // Convert touch delta to scroll movement
            float deltaY = touch.deltaPosition.y / (configsCamera.pixelHeight / 10f);
            
            // Apply to ScrollRect content
            if (scrollRect.content != null)
            {
                Vector2 currentPos = scrollRect.content.anchoredPosition;
                currentPos.y += deltaY * scrollSensitivity;
                
                // Clamp to valid scroll bounds
                float maxY = Mathf.Max(0, scrollRect.content.sizeDelta.y - scrollRect.viewport.rect.height);
                currentPos.y = Mathf.Clamp(currentPos.y, 0, maxY);
                
                scrollRect.content.anchoredPosition = currentPos;
            }
        }
    }
    
    void HandleMouseScroll(float scrollDelta)
    {
        // Apply mouse scroll to ScrollRect
        if (scrollRect.content != null)
        {
            Vector2 currentPos = scrollRect.content.anchoredPosition;
            currentPos.y -= scrollDelta * scrollSensitivity * 100f; // Scale for mouse wheel
            
            // Clamp to valid scroll bounds
            float maxY = Mathf.Max(0, scrollRect.content.sizeDelta.y - scrollRect.viewport.rect.height);
            currentPos.y = Mathf.Clamp(currentPos.y, 0, maxY);
            
            scrollRect.content.anchoredPosition = currentPos;
        }
    }
    
    // Public interface for configuration
    public void SetScrollSensitivity(float sensitivity)
    {
        scrollSensitivity = sensitivity;
        if (scrollRect != null)
        {
            scrollRect.scrollSensitivity = sensitivity;
        }
    }
    
    public void SetInertiaEnabled(bool enabled)
    {
        enableInertia = enabled;
        if (scrollRect != null)
        {
            scrollRect.inertia = enabled;
        }
    }
    
    public void SetDecelerationRate(float rate)
    {
        decelerationRate = rate;
        if (scrollRect != null)
        {
            scrollRect.decelerationRate = rate;
        }
    }
    
    public void EnableLegacyScrollBehavior(bool enable)
    {
        useLegacyScrollBehavior = enable;
    }
    
    // Scroll to specific position
    public void ScrollToPosition(float normalizedPosition)
    {
        if (scrollRect != null)
        {
            scrollRect.verticalNormalizedPosition = 1f - normalizedPosition; // Invert for top-to-bottom
        }
    }
    
    public void ScrollToTop()
    {
        ScrollToPosition(0f);
    }
    
    public void ScrollToBottom()
    {
        ScrollToPosition(1f);
    }
    
    // Get current scroll position
    public float GetScrollPosition()
    {
        if (scrollRect != null)
        {
            return 1f - scrollRect.verticalNormalizedPosition; // Invert for top-to-bottom
        }
        return 0f;
    }
    
    // Debugging and validation
    void OnValidate()
    {
        if (scrollRect == null)
        {
            scrollRect = GetComponent<ScrollRect>();
        }
        
        // Apply settings in editor
        if (scrollRect != null && Application.isPlaying)
        {
            ConfigureScrollRect();
        }
    }
    
    // Gizmos for debugging scroll area
    void OnDrawGizmosSelected()
    {
        if (configsCamera != null && scrollRect != null && scrollRect.viewport != null)
        {
            // Draw the scroll boundary area
            Gizmos.color = Color.yellow;
            Vector3 boundaryPos = configsCamera.ViewportToWorldPoint(new Vector3(legacyScrollBoundary, 0.5f, configsCamera.nearClipPlane));
            Gizmos.DrawLine(boundaryPos + Vector3.up * 5, boundaryPos + Vector3.down * 5);
        }
    }
}
