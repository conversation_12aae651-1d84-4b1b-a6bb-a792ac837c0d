using System.Collections.Generic;
using System.Linq;
using UnityEngine;

/// <summary>
/// Cached character filtering system to eliminate per-frame LINQ operations
/// Replaces the expensive Update() filtering in CharatersForPartyUIHandler
/// Provides significant performance improvement by caching filtered results
/// </summary>
public class CachedCharacterFilter : MonoBehaviour
{
    [Header("Cache Settings")]
    [SerializeField] private bool enablePerformanceLogging = false;
    
    // Cached character lists
    private List<BattleCharacter> cachedNonEnemyCharacters = new List<BattleCharacter>();
    private List<BattleCharacter> cachedAllCharacters = new List<BattleCharacter>();
    
    // Cache invalidation tracking
    private int lastCharacterCount = -1;
    private int lastNonEnemyCount = -1;
    private string lastCharacterListHash = "";
    
    // Performance tracking
    private int cacheHits = 0;
    private int cacheMisses = 0;
    private float lastCacheUpdateTime = 0f;
    
    // References
    private ConfigsHandler configsHandler;
    
    // Events for cache updates
    public System.Action OnCacheUpdated;
    
    void Awake()
    {
        configsHandler = GameObject.Find("GameConfigsHandler")?.GetComponent<ConfigsHandler>();
        
        if (configsHandler == null)
        {
            Debug.LogError("[CACHED_FILTER] ❌ ConfigsHandler not found!");
        }
    }
    
    void Start()
    {
        // Initial cache population
        UpdateCache();
    }
    
    /// <summary>
    /// Gets cached non-enemy characters, updating cache if necessary
    /// </summary>
    public List<BattleCharacter> GetNonEnemyCharacters()
    {
        if (ShouldUpdateCache())
        {
            UpdateCache();
            cacheMisses++;
        }
        else
        {
            cacheHits++;
        }
        
        return new List<BattleCharacter>(cachedNonEnemyCharacters);
    }
    
    /// <summary>
    /// Gets cached all characters, updating cache if necessary
    /// </summary>
    public List<BattleCharacter> GetAllCharacters()
    {
        if (ShouldUpdateCache())
        {
            UpdateCache();
            cacheMisses++;
        }
        else
        {
            cacheHits++;
        }
        
        return new List<BattleCharacter>(cachedAllCharacters);
    }
    
    /// <summary>
    /// Gets cached characters filtered by a custom predicate
    /// </summary>
    public List<BattleCharacter> GetFilteredCharacters(System.Func<BattleCharacter, bool> filter)
    {
        if (ShouldUpdateCache())
        {
            UpdateCache();
            cacheMisses++;
        }
        else
        {
            cacheHits++;
        }
        
        return cachedAllCharacters.Where(filter).ToList();
    }
    
    /// <summary>
    /// Checks if cache should be updated based on character list changes
    /// </summary>
    private bool ShouldUpdateCache()
    {
        if (configsHandler == null) return false;
        
        var currentCharacters = configsHandler.GetCharacters();
        
        // Check if character count changed
        if (currentCharacters.Count != lastCharacterCount)
        {
            return true;
        }
        
        // Check if character list composition changed (more expensive check)
        string currentHash = GenerateCharacterListHash(currentCharacters);
        if (currentHash != lastCharacterListHash)
        {
            return true;
        }
        
        return false;
    }
    
    /// <summary>
    /// Updates the cached character lists
    /// </summary>
    private void UpdateCache()
    {
        if (configsHandler == null) return;
        
        float startTime = Time.realtimeSinceStartup;
        
        // Get current character list
        cachedAllCharacters = configsHandler.GetCharacters();
        
        // Filter non-enemy characters
        cachedNonEnemyCharacters = cachedAllCharacters.Where(c => !c.isEnemy).ToList();
        
        // Update tracking variables
        lastCharacterCount = cachedAllCharacters.Count;
        lastNonEnemyCount = cachedNonEnemyCharacters.Count;
        lastCharacterListHash = GenerateCharacterListHash(cachedAllCharacters);
        lastCacheUpdateTime = Time.realtimeSinceStartup;
        
        float updateTime = (Time.realtimeSinceStartup - startTime) * 1000f;
        
        if (enablePerformanceLogging)
        {
            Debug.Log($"[CACHED_FILTER] 🔄 Cache updated in {updateTime:F2}ms: {cachedAllCharacters.Count} total, {cachedNonEnemyCharacters.Count} non-enemy");
        }
        
        // Notify listeners
        OnCacheUpdated?.Invoke();
    }
    
    /// <summary>
    /// Generates a hash of the character list for change detection
    /// </summary>
    private string GenerateCharacterListHash(List<BattleCharacter> characters)
    {
        if (characters == null || characters.Count == 0) return "";
        
        // Simple hash based on character IDs and count
        // This is much faster than deep comparison but catches most changes
        var hashString = characters.Count.ToString();
        
        // Sample a few characters for hash (performance vs accuracy trade-off)
        int sampleSize = Mathf.Min(10, characters.Count);
        for (int i = 0; i < sampleSize; i++)
        {
            int index = i * (characters.Count / sampleSize);
            if (index < characters.Count && characters[index] != null)
            {
                hashString += characters[index].id;
            }
        }
        
        return hashString.GetHashCode().ToString();
    }
    
    /// <summary>
    /// Forces cache update regardless of change detection
    /// </summary>
    public void ForceUpdateCache()
    {
        UpdateCache();
        
        if (enablePerformanceLogging)
        {
            Debug.Log("[CACHED_FILTER] 🔄 Forced cache update");
        }
    }
    
    /// <summary>
    /// Checks if a character exists in the cached non-enemy list
    /// </summary>
    public bool IsNonEnemyCharacter(BattleCharacter character)
    {
        if (ShouldUpdateCache())
        {
            UpdateCache();
        }
        
        return cachedNonEnemyCharacters.Any(c => c.id == character.id);
    }
    
    /// <summary>
    /// Gets the index of a character in the cached non-enemy list
    /// </summary>
    public int GetNonEnemyCharacterIndex(BattleCharacter character)
    {
        if (ShouldUpdateCache())
        {
            UpdateCache();
        }
        
        return cachedNonEnemyCharacters.FindIndex(c => c.id == character.id);
    }
    
    /// <summary>
    /// Gets cache performance statistics
    /// </summary>
    public void LogCacheStats()
    {
        float hitRate = cacheHits + cacheMisses > 0 ? (float)cacheHits / (cacheHits + cacheMisses) * 100f : 0f;
        float timeSinceLastUpdate = Time.realtimeSinceStartup - lastCacheUpdateTime;
        
        Debug.Log($"[CACHED_FILTER] 📊 Cache Stats:");
        Debug.Log($"  Hit Rate: {hitRate:F1}% ({cacheHits} hits, {cacheMisses} misses)");
        Debug.Log($"  Cached Characters: {cachedAllCharacters.Count} total, {cachedNonEnemyCharacters.Count} non-enemy");
        Debug.Log($"  Time Since Last Update: {timeSinceLastUpdate:F2}s");
    }
    
    /// <summary>
    /// Resets cache statistics
    /// </summary>
    public void ResetCacheStats()
    {
        cacheHits = 0;
        cacheMisses = 0;
        
        if (enablePerformanceLogging)
        {
            Debug.Log("[CACHED_FILTER] 🔄 Cache statistics reset");
        }
    }
    
    /// <summary>
    /// Gets the current cache hit rate
    /// </summary>
    public float GetCacheHitRate()
    {
        return cacheHits + cacheMisses > 0 ? (float)cacheHits / (cacheHits + cacheMisses) : 0f;
    }
    
    /// <summary>
    /// Clears all cached data
    /// </summary>
    public void ClearCache()
    {
        cachedAllCharacters.Clear();
        cachedNonEnemyCharacters.Clear();
        lastCharacterCount = -1;
        lastNonEnemyCount = -1;
        lastCharacterListHash = "";
        
        if (enablePerformanceLogging)
        {
            Debug.Log("[CACHED_FILTER] 🗑️ Cache cleared");
        }
    }
    
    void OnDestroy()
    {
        if (enablePerformanceLogging)
        {
            LogCacheStats();
        }
    }
}
