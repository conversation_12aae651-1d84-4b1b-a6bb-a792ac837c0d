using System.Collections.Generic;
using System.Linq;
using UnityEngine;

/// <summary>
/// High-performance character filtering system with caching
/// Replaces per-frame LINQ operations with cached results that update only when data changes
/// </summary>
public class CachedCharacterFilter
{
    private readonly ConfigsHandler configsHandler;
    
    // Cached filter results
    private List<BattleCharacter> cachedAllCharacters = new List<BattleCharacter>();
    private List<BattleCharacter> cachedNonEnemyCharacters = new List<BattleCharacter>();
    private List<BattleCharacter> cachedEnemyCharacters = new List<BattleCharacter>();
    
    // Cache validation
    private int lastCharacterCount = -1;
    private int lastCharacterHashCode = -1;
    private bool cacheValid = false;
    
    // Filter settings
    private FilterType currentFilter = FilterType.NonEnemies;
    private string searchQuery = "";
    private bool caseSensitiveSearch = false;
    
    // Performance tracking
    private int cacheHits = 0;
    private int cacheMisses = 0;
    private float lastUpdateTime = 0f;
    
    /// <summary>
    /// Available filter types
    /// </summary>
    public enum FilterType
    {
        All,
        NonEnemies,
        Enemies,
        Search
    }
    
    /// <summary>
    /// Initialize the cached character filter
    /// </summary>
    /// <param name="configsHandler">Reference to ConfigsHandler for character data</param>
    public CachedCharacterFilter(ConfigsHandler configsHandler)
    {
        this.configsHandler = configsHandler;
        InvalidateCache();
    }
    
    /// <summary>
    /// Get filtered characters based on current filter settings
    /// </summary>
    /// <returns>Filtered list of characters</returns>
    public List<BattleCharacter> GetFilteredCharacters()
    {
        ValidateCache();
        
        switch (currentFilter)
        {
            case FilterType.All:
                cacheHits++;
                return new List<BattleCharacter>(cachedAllCharacters);
                
            case FilterType.NonEnemies:
                cacheHits++;
                return new List<BattleCharacter>(cachedNonEnemyCharacters);
                
            case FilterType.Enemies:
                cacheHits++;
                return new List<BattleCharacter>(cachedEnemyCharacters);
                
            case FilterType.Search:
                return ApplySearchFilter();
                
            default:
                return new List<BattleCharacter>(cachedNonEnemyCharacters);
        }
    }
    
    /// <summary>
    /// Set the current filter type
    /// </summary>
    /// <param name="filterType">Type of filter to apply</param>
    public void SetFilter(FilterType filterType)
    {
        if (currentFilter != filterType)
        {
            currentFilter = filterType;
            Debug.Log($"[CACHED_FILTER] 🔍 Filter changed to: {filterType}");
        }
    }
    
    /// <summary>
    /// Set search query for text-based filtering
    /// </summary>
    /// <param name="query">Search query string</param>
    /// <param name="caseSensitive">Whether search should be case sensitive</param>
    public void SetSearchQuery(string query, bool caseSensitive = false)
    {
        if (searchQuery != query || caseSensitiveSearch != caseSensitive)
        {
            searchQuery = query ?? "";
            caseSensitiveSearch = caseSensitive;
            
            // Auto-switch to search filter if query is provided
            if (!string.IsNullOrEmpty(searchQuery))
            {
                SetFilter(FilterType.Search);
            }
            
            Debug.Log($"[CACHED_FILTER] 🔍 Search query set: '{searchQuery}' (Case sensitive: {caseSensitive})");
        }
    }
    
    /// <summary>
    /// Validate and update cache if necessary
    /// </summary>
    private void ValidateCache()
    {
        if (configsHandler == null) return;
        
        var allCharacters = configsHandler.GetCharacters();
        if (allCharacters == null) return;
        
        // Check if cache needs updating
        int currentCount = allCharacters.Count;
        int currentHashCode = CalculateCharacterListHashCode(allCharacters);
        
        if (!cacheValid || 
            currentCount != lastCharacterCount || 
            currentHashCode != lastCharacterHashCode)
        {
            UpdateCache(allCharacters);
            lastCharacterCount = currentCount;
            lastCharacterHashCode = currentHashCode;
            cacheValid = true;
            cacheMisses++;
            lastUpdateTime = Time.time;
            
            Debug.Log($"[CACHED_FILTER] 🔄 Cache updated: {currentCount} characters");
        }
    }
    
    /// <summary>
    /// Update all cached filter results
    /// </summary>
    /// <param name="allCharacters">Complete list of characters</param>
    private void UpdateCache(List<BattleCharacter> allCharacters)
    {
        // Cache all characters
        cachedAllCharacters = new List<BattleCharacter>(allCharacters);
        
        // Cache non-enemy characters (most common filter)
        cachedNonEnemyCharacters = allCharacters.Where(c => !c.isEnemy).ToList();
        
        // Cache enemy characters
        cachedEnemyCharacters = allCharacters.Where(c => c.isEnemy).ToList();
    }
    
    /// <summary>
    /// Apply search filter to cached results
    /// </summary>
    /// <returns>Characters matching search criteria</returns>
    private List<BattleCharacter> ApplySearchFilter()
    {
        if (string.IsNullOrEmpty(searchQuery))
        {
            return GetFilteredCharacters(); // Return current filter if no search query
        }
        
        var baseList = currentFilter == FilterType.Enemies ? cachedEnemyCharacters : cachedNonEnemyCharacters;
        var comparison = caseSensitiveSearch ? System.StringComparison.Ordinal : System.StringComparison.OrdinalIgnoreCase;
        
        return baseList.Where(character => 
            character.name.IndexOf(searchQuery, comparison) >= 0 ||
            character.id.IndexOf(searchQuery, comparison) >= 0 ||
            character.level.ToString().Contains(searchQuery)
        ).ToList();
    }
    
    /// <summary>
    /// Calculate hash code for character list to detect changes
    /// </summary>
    /// <param name="characters">List of characters</param>
    /// <returns>Hash code representing the current state</returns>
    private int CalculateCharacterListHashCode(List<BattleCharacter> characters)
    {
        if (characters == null || characters.Count == 0) return 0;
        
        unchecked
        {
            int hash = 17;
            hash = hash * 23 + characters.Count;
            
            // Sample a few characters for hash calculation (performance optimization)
            int sampleSize = Mathf.Min(10, characters.Count);
            int step = Mathf.Max(1, characters.Count / sampleSize);
            
            for (int i = 0; i < characters.Count; i += step)
            {
                var character = characters[i];
                if (character != null)
                {
                    hash = hash * 23 + (character.id?.GetHashCode() ?? 0);
                    hash = hash * 23 + character.level;
                    hash = hash * 23 + character.isEnemy.GetHashCode();
                }
            }
            
            return hash;
        }
    }
    
    /// <summary>
    /// Force cache invalidation on next access
    /// </summary>
    public void InvalidateCache()
    {
        cacheValid = false;
        lastCharacterCount = -1;
        lastCharacterHashCode = -1;
        
        Debug.Log("[CACHED_FILTER] ♻️ Cache invalidated");
    }
    
    /// <summary>
    /// Get current filter statistics
    /// </summary>
    /// <returns>Filter performance statistics</returns>
    public FilterStatistics GetStatistics()
    {
        return new FilterStatistics
        {
            CacheHits = cacheHits,
            CacheMisses = cacheMisses,
            HitRatio = (cacheHits + cacheMisses) > 0 ? (float)cacheHits / (cacheHits + cacheMisses) : 0f,
            LastUpdateTime = lastUpdateTime,
            CacheValid = cacheValid,
            CurrentFilter = currentFilter,
            SearchQuery = searchQuery,
            TotalCharacters = cachedAllCharacters.Count,
            NonEnemyCount = cachedNonEnemyCharacters.Count,
            EnemyCount = cachedEnemyCharacters.Count
        };
    }
    
    /// <summary>
    /// Get character count for current filter
    /// </summary>
    /// <returns>Number of characters matching current filter</returns>
    public int GetFilteredCharacterCount()
    {
        return GetFilteredCharacters().Count;
    }
    
    /// <summary>
    /// Check if a character matches current filter criteria
    /// </summary>
    /// <param name="character">Character to check</param>
    /// <returns>True if character matches current filter</returns>
    public bool CharacterMatchesFilter(BattleCharacter character)
    {
        if (character == null) return false;
        
        switch (currentFilter)
        {
            case FilterType.All:
                return true;
                
            case FilterType.NonEnemies:
                return !character.isEnemy;
                
            case FilterType.Enemies:
                return character.isEnemy;
                
            case FilterType.Search:
                if (string.IsNullOrEmpty(searchQuery)) return true;
                var comparison = caseSensitiveSearch ? System.StringComparison.Ordinal : System.StringComparison.OrdinalIgnoreCase;
                return character.name.IndexOf(searchQuery, comparison) >= 0 ||
                       character.id.IndexOf(searchQuery, comparison) >= 0 ||
                       character.level.ToString().Contains(searchQuery);
                
            default:
                return false;
        }
    }
}

/// <summary>
/// Statistics for filter performance monitoring
/// </summary>
public struct FilterStatistics
{
    public int CacheHits;
    public int CacheMisses;
    public float HitRatio;
    public float LastUpdateTime;
    public bool CacheValid;
    public CachedCharacterFilter.FilterType CurrentFilter;
    public string SearchQuery;
    public int TotalCharacters;
    public int NonEnemyCount;
    public int EnemyCount;
    
    public override string ToString()
    {
        return $"Filter Stats - Hits: {CacheHits}, Misses: {CacheMisses}, " +
               $"Hit Ratio: {HitRatio:P1}, Filter: {CurrentFilter}, " +
               $"Characters: {TotalCharacters} ({NonEnemyCount} non-enemy, {EnemyCount} enemy)";
    }
}
