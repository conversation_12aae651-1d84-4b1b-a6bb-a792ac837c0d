using System;
using System.IO;
using System.Linq;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Unity.VisualScripting;
using UnityEngine;
using UnityEngine.UI;

public class ImportJsonFile : MonoBehaviour
{
    public Button importButton;

    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        importButton.onClick.AddListener(OpenFileExplorer);
    }

    // Update is called once per frame
    void Update()
    {

    }

    void OpenFileExplorer()
    {
        NativeFilePicker.PickFile((importPath) =>
        {
            if (!string.IsNullOrEmpty(importPath))
            {
                if (ImportFile(importPath, "characters.json"))
                {
                    new ReloadGame(); //After successful import
                }
            }
            else
            {
                Debug.LogWarning("No file selected.");
            }
        });
    }

    bool ImportFile(string importPath, string targetJsonFile)
    {
        try
        {
            // Only allow replacing characters.json
            if (!string.Equals(targetJsonFile, "characters.json", StringComparison.OrdinalIgnoreCase))
            {
                Debug.LogError("Only characters.json can be replaced by import.");
                return false;
            }

            // Get the save folder path
            string saveFolder = (Application.platform == RuntimePlatform.WindowsEditor || Application.platform == RuntimePlatform.WindowsPlayer)
                ? Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), @"DKGRPGPrototype_unity")
                : Application.persistentDataPath;

            string targetPath = Path.Combine(saveFolder, targetJsonFile);

            // Check if the import file exists
            if (!File.Exists(importPath))
            {
                Debug.LogError($"Import file not found: {importPath}");
                return false;
            }

        // Load current and imported JSON
        var currentJson = JObject.Parse(File.ReadAllText(targetPath));
        var importJson = JObject.Parse(File.ReadAllText(importPath));

        var importedCharList = importJson["characterPkg"]?["data"] as JArray;
        var importedBattleUpList = importJson["battleUpgradePkg"]?["data"] as JArray;
        var importedStatsList = importJson["statusInfoPkg"]?["data"] as JArray;
        var importedModsList = importJson["primalModifierPkg"]?["data"]?["primalModifier"] as JArray;
        var localList = currentJson["characters"] as JArray;

        if (importedCharList == null || localList == null)
        {
            Debug.LogError("Invalid JSON structure.");
            return false;
        }

        foreach (var importedChar in importedCharList)
        {
            var id = importedChar["id"]?.ToString();
            var name = importedChar["name"]?.ToString() ?? importedChar["character"]?.ToString();
            if (string.IsNullOrEmpty(id)) continue;

            // Find matching objects in imported arrays by "character" == id
            var battleUp = importedBattleUpList?.FirstOrDefault(x => x["character"]?.ToString() == id);
            var stats = importedStatsList?.Where(x => x["character"]?.ToString() == id).ToList();
            var mods = importedModsList?.Where(x => x["character"]?.ToString() == id).ToList();

            // Find or create local character
            var localChar = localList.FirstOrDefault(c =>
                c["id"] != null &&
                (
                    (c["id"].Type == JTokenType.Integer && c["id"].ToString() == id) ||
                    (c["id"].Type == JTokenType.String && c["id"].ToString() == id)
                )
            ) as JObject;

            if (localChar == null)
            {
                localChar = new JObject();
                localList.Add(localChar);
            }

            // Set id and name
            localChar["id"] = id;
            localChar["name"] = name;

            // Level (bl)
            localChar["level"] = battleUp?["bl"] ?? 1;

            // Skills
            var skillsByType = new JArray();
            if (stats != null)
            {
                foreach (var stat in stats)
                {
                    skillsByType.Add(new JObject
                    {
                        ["spDef"] = stat["spDef"] ?? "0",
                        ["spAtk"] = stat["spAtk"] ?? "0",
                        ["type"] = stat["type"] ?? "",
                        ["acronym"] = stat["acronym"] ?? ""
                    });
                }
            }
            localChar["skills"] = new JObject { ["skillsByType"] = skillsByType };

            // Stats
            localChar["stats"] = new JObject
            {
                ["apMin"] = battleUp?["apMin"] ?? new JArray(),
                ["hp"] = battleUp?["hp"] ?? new JArray(),
                ["atk"] = battleUp?["atk"] ?? new JArray(),
                ["def"] = battleUp?["def"] ?? new JArray(),
                ["atkLim"] = battleUp?["atkLim"] ?? new JArray(),
                ["bl"] = battleUp?["baseLevel"] ?? new JArray()
            };

            // Mods
            JObject modsObj = new JObject();
            if (mods != null)
            {
                modsObj["knowledge"] = mods.FirstOrDefault(m => m["fieldName"]?.ToString() == "Inteligência")?["fieldValue"] ?? 0;
                modsObj["luck"] = mods.FirstOrDefault(m => m["fieldName"]?.ToString() == "Sorte")?["fieldValue"] ?? 0;
                modsObj["speed"] = mods.FirstOrDefault(m => m["fieldName"]?.ToString() == "Velocidade")?["fieldValue"] ?? 0;
                modsObj["precision"] = mods.FirstOrDefault(m => m["fieldName"]?.ToString() == "Precisão")?["fieldValue"] ?? 0;
                modsObj["evasion"] = mods.FirstOrDefault(m => m["fieldName"]?.ToString() == "Evasão")?["fieldValue"] ?? 0;
                modsObj["criticalChance"] = mods.FirstOrDefault(m => m["fieldName"]?.ToString() == "Chance Critica")?["fieldValue"] ?? 0;
            }
            localChar["mods"] = modsObj;
        }

        // Save
        File.WriteAllText(targetPath, currentJson.ToString(Formatting.Indented));
        Debug.Log("Characters merged and updated successfully.");
        return true;
    }
        catch (Exception ex)
        {
            Debug.LogError($"Error importing JSON file: {ex.Message}");
            return false;
        }
    }

}
