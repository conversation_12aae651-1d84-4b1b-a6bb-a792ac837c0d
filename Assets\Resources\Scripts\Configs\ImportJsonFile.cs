using System;
using System.IO;
using System.Linq;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Unity.VisualScripting;
using UnityEngine;
using UnityEngine.UI;

public class ImportJsonFile : MonoBehaviour
{
    public Button importButton;

    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        importButton.onClick.AddListener(OpenFileExplorer);
    }

    // Update is called once per frame
    void Update()
    {

    }

    void OpenFileExplorer()
    {
        NativeFilePicker.PickFile((importPath) =>
        {
            if (!string.IsNullOrEmpty(importPath))
            {
                if (ImportFile(importPath, "characters.json"))
                {
                    new ReloadGame(); //After successful import
                }
            }
            else
            {
                Debug.LogWarning("No file selected.");
            }
        });
    }

    bool ImportFile(string importPath, string targetJsonFile)
    {
        try
        {
            // Only allow replacing characters.json
            if (!string.Equals(targetJsonFile, "characters.json", StringComparison.OrdinalIgnoreCase))
            {
                Debug.LogError("Only characters.json can be replaced by import.");
                return false;
            }

            // Get the save folder path
            string saveFolder = (Application.platform == RuntimePlatform.WindowsEditor || Application.platform == RuntimePlatform.WindowsPlayer)
                ? Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), @"DKGRPGPrototype_unity")
                : Application.persistentDataPath;

            string targetPath = Path.Combine(saveFolder, targetJsonFile);

            // Check if the import file exists
            if (!File.Exists(importPath))
            {
                Debug.LogError($"Import file not found: {importPath}");
                return false;
            }

            // Check if target file exists, create default structure if not
            JObject currentJson;
            if (!File.Exists(targetPath))
            {
                Debug.LogWarning($"Target file {targetJsonFile} not found. Creating default structure.");
                currentJson = new JObject
                {
                    ["characters"] = new JArray()
                };
            }
            else
            {
                currentJson = JObject.Parse(File.ReadAllText(targetPath));
            }

            var importJson = JObject.Parse(File.ReadAllText(importPath));

        var importedCharList = importJson["characterPkg"]?["data"] as JArray;
        var importedBattleUpList = importJson["battleUpgradePkg"]?["data"] as JArray;
        var importedStatsList = importJson["statusInfoPkg"]?["data"] as JArray;
        var importedModsList = importJson["primalModifierPkg"]?["data"] as JArray;
        var localList = currentJson["characters"] as JArray;

        if (importedCharList == null || localList == null)
        {
            Debug.LogError("Invalid JSON structure.");
            return false;
        }

        foreach (var importedChar in importedCharList)
        {
            var id = importedChar["id"]?.ToString();
            var name = importedChar["name"]?.ToString() ?? importedChar["character"]?.ToString();
            if (string.IsNullOrEmpty(id)) continue;

            // Find matching objects in imported arrays by "character" == id
            var battleUp = importedBattleUpList?.FirstOrDefault(x => x["character"]?.ToString() == id);
            var stats = importedStatsList?.Where(x => x["character"]?.ToString() == id).ToList();

            // Find the primal modifier entry for this character and extract the primalModifier array
            var modsEntry = importedModsList?.FirstOrDefault(x => x["character"]?.ToString() == id);
            var mods = modsEntry?["primalModifier"] as JArray;

            // Find or create local character
            var localChar = localList.FirstOrDefault(c =>
                c["id"] != null &&
                (
                    (c["id"].Type == JTokenType.Integer && c["id"].ToString() == id) ||
                    (c["id"].Type == JTokenType.String && c["id"].ToString() == id)
                )
            ) as JObject;

            if (localChar == null)
            {
                localChar = new JObject();
                localList.Add(localChar);
            }

            // Set id and name
            localChar["id"] = id;
            localChar["name"] = name;

            // Set isEnemy field (imported characters are player characters by default)
            localChar["isEnemy"] = false;

            // Level (bl)
            localChar["level"] = battleUp?["bl"] ?? 1;

            // Skills - Create fixed-size array indexed by Types enum values
            var skillsByType = new JArray();

            // Initialize all skill type slots with default values (8 types: Strength through Frost)
            int totalTypes = 8; // Types enum has 8 values: Strength, Magic, Fire, Venom, Possession, Electricity, Acid, Frost
            string[] typeNames = { "Strength", "Magic", "Fire", "Venom", "Possession", "Electricity", "Acid", "Frost" };
            string[] typeAcronyms = { "St", "Ma", "Fi", "Ve", "Po", "El", "Ac", "Fr" };

            for (int i = 0; i < totalTypes; i++)
            {
                skillsByType.Add(new JObject
                {
                    ["spDef"] = "0",
                    ["spAtk"] = "0",
                    ["type"] = typeNames[i],
                    ["acronym"] = typeAcronyms[i]
                });
            }

            // Map imported stats to correct skill type positions based on acronym
            if (stats != null)
            {
                foreach (var stat in stats)
                {
                    string acronym = stat["acronym"]?.ToString() ?? "";
                    int typeIndex = GetTypeIndexFromAcronym(acronym);

                    if (typeIndex >= 0 && typeIndex < skillsByType.Count)
                    {
                        var skillSlot = skillsByType[typeIndex] as JObject;
                        if (skillSlot != null)
                        {
                            skillSlot["spDef"] = stat["spDef"]?.ToString() ?? "0";
                            skillSlot["spAtk"] = stat["spAtk"]?.ToString() ?? "0";
                            skillSlot["type"] = stat["type"]?.ToString() ?? "";
                            skillSlot["acronym"] = acronym;
                        }
                    }
                }
            }

            localChar["skills"] = new JObject { ["skillsByType"] = skillsByType };

            // Stats - Create proper stat arrays or use imported data if available
            JArray apMinArray, hpArray, atkArray, defArray, atkLimArray, blArray;

            if (battleUp != null &&
                battleUp["apMin"] is JArray importedApMin && importedApMin.Count > 0 &&
                battleUp["hp"] is JArray importedHp && importedHp.Count > 0 &&
                battleUp["atk"] is JArray importedAtk && importedAtk.Count > 0 &&
                battleUp["def"] is JArray importedDef && importedDef.Count > 0 &&
                battleUp["atkLim"] is JArray importedAtkLim && importedAtkLim.Count > 0)
            {
                // Use imported data if all arrays are present and non-empty
                apMinArray = importedApMin;
                hpArray = importedHp;
                atkArray = importedAtk;
                defArray = importedDef;
                atkLimArray = importedAtkLim;
                blArray = battleUp["bl"] as JArray ?? GenerateDefaultBlArray(apMinArray.Count);
            }
            else
            {
                // Generate default stat arrays (101 levels: 0-100)
                apMinArray = GenerateDefaultApMinArray();
                hpArray = GenerateDefaultHpArray();
                atkArray = GenerateDefaultAtkArray();
                defArray = GenerateDefaultDefArray();
                atkLimArray = GenerateDefaultAtkLimArray(atkArray);
                blArray = GenerateDefaultBlArray(101);
            }

            localChar["stats"] = new JObject
            {
                ["apMin"] = apMinArray,
                ["hp"] = hpArray,
                ["atk"] = atkArray,
                ["def"] = defArray,
                ["atkLim"] = atkLimArray,
                ["bl"] = blArray
            };

            // Mods
            JObject modsObj = new JObject();
            if (mods != null)
            {
                modsObj["knowledge"] = mods.FirstOrDefault(m => m["fieldName"]?.ToString() == "Inteligência")?["fieldValue"] ?? 0;
                modsObj["luck"] = mods.FirstOrDefault(m => m["fieldName"]?.ToString() == "Sorte")?["fieldValue"] ?? 0;
                modsObj["speed"] = mods.FirstOrDefault(m => m["fieldName"]?.ToString() == "Velocidade")?["fieldValue"] ?? 0;
                modsObj["precision"] = mods.FirstOrDefault(m => m["fieldName"]?.ToString() == "Precisão")?["fieldValue"] ?? 0;
                modsObj["evasion"] = mods.FirstOrDefault(m => m["fieldName"]?.ToString() == "Evasão")?["fieldValue"] ?? 0;
                modsObj["criticalChance"] = mods.FirstOrDefault(m => m["fieldName"]?.ToString() == "Chance Critica")?["fieldValue"] ?? 0;
            }
            localChar["mods"] = modsObj;
        }

        // Save
        File.WriteAllText(targetPath, currentJson.ToString(Formatting.Indented));
        Debug.Log("Characters merged and updated successfully.");
        return true;
    }
        catch (Exception ex)
        {
            Debug.LogError($"Error importing JSON file: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Maps skill acronyms to their corresponding Types enum index
    /// </summary>
    private int GetTypeIndexFromAcronym(string acronym)
    {
        return acronym?.ToUpper() switch
        {
            "ST" => 0, // Strength
            "MA" => 1, // Magic
            "FI" => 2, // Fire
            "VE" => 3, // Venom
            "PO" => 4, // Possession
            "EL" => 5, // Electricity
            "AC" => 6, // Acid
            "FR" => 7, // Frost
            _ => -1    // Unknown/invalid acronym
        };
    }

    /// <summary>
    /// Generates default apMin array (101 levels: 0-100)
    /// </summary>
    private JArray GenerateDefaultApMinArray()
    {
        var array = new JArray();
        for (int i = 0; i <= 100; i++)
        {
            array.Add(i / 10); // apMin = level / 10
        }
        return array;
    }

    /// <summary>
    /// Generates default HP array (101 levels: 0-100)
    /// </summary>
    private JArray GenerateDefaultHpArray()
    {
        var array = new JArray();
        for (int i = 0; i <= 100; i++)
        {
            array.Add(UnityEngine.Random.Range(50, 101) * (i + 1)); // hp = random(50-100) * (level + 1)
        }
        return array;
    }

    /// <summary>
    /// Generates default attack array (101 levels: 0-100)
    /// </summary>
    private JArray GenerateDefaultAtkArray()
    {
        var array = new JArray();
        for (int i = 0; i <= 100; i++)
        {
            array.Add(UnityEngine.Random.Range(50, 101) * (i + 1)); // atk = random(50-100) * (level + 1)
        }
        return array;
    }

    /// <summary>
    /// Generates default defense array (101 levels: 0-100)
    /// </summary>
    private JArray GenerateDefaultDefArray()
    {
        var array = new JArray();
        for (int i = 0; i <= 100; i++)
        {
            array.Add(UnityEngine.Random.Range(50, 101) * (i + 1)); // def = random(50-100) * (level + 1)
        }
        return array;
    }

    /// <summary>
    /// Generates default attack limit array based on attack values
    /// </summary>
    private JArray GenerateDefaultAtkLimArray(JArray atkArray)
    {
        var array = new JArray();
        foreach (var atk in atkArray)
        {
            array.Add((int)atk * 2); // atkLim = atk * 2
        }
        return array;
    }

    /// <summary>
    /// Generates default base level array
    /// </summary>
    private JArray GenerateDefaultBlArray(int count)
    {
        var array = new JArray();
        for (int i = 0; i < count; i++)
        {
            array.Add(i); // bl = level index
        }
        return array;
    }
}
