using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Virtual scrolling system for character list UI
/// Only renders visible character UI elements to optimize performance with large datasets
/// </summary>
public class VirtualCharacterList : MonoBehaviour
{
    [Header("Virtual List Configuration")]
    [SerializeField] private ScrollRect scrollRect;
    [SerializeField] private RectTransform content;
    [SerializeField] private RectTransform viewport;
    [SerializeField] private GameObject characterUIPrefab;
    
    [Header("Layout Settings")]
    [SerializeField] private float itemHeight = 100f;
    [SerializeField] private float itemSpacing = 10f;
    [SerializeField] private int visibleItemCount = 12;
    [SerializeField] private int bufferItemCount = 2; // Extra items above/below visible area
    
    [Header("Performance Settings")]
    [SerializeField] private bool enableCachedFiltering = true;
    [SerializeField] private int maxPoolSize = 20;
    
    // Core data
    private List<BattleCharacter> allCharacters = new List<BattleCharacter>();
    private List<BattleCharacter> filteredCharacters = new List<BattleCharacter>();
    private ConfigsHandler configsHandler;
    
    // Virtual scrolling state
    private int firstVisibleIndex = 0;
    private int lastVisibleIndex = 0;
    private float totalContentHeight = 0f;
    
    // Object pooling
    private CharacterUIPool uiPool;
    private List<VirtualCharacterUIItem> activeItems = new List<VirtualCharacterUIItem>();
    
    // Cached filtering
    private Dictionary<string, List<BattleCharacter>> filterCache = new Dictionary<string, List<BattleCharacter>>();
    private string currentFilter = "";
    private bool isFilterDirty = true;
    
    // Performance tracking
    private int framesSinceLastUpdate = 0;
    private const int UPDATE_FREQUENCY = 3; // Update every 3 frames
    
    public System.Action<BattleCharacter> OnCharacterSelected;
    public System.Action<BattleCharacter> OnCharacterStatsClicked;
    public System.Action<BattleCharacter> OnCharacterSkillsClicked;
    public System.Action<BattleCharacter> OnCharacterModsClicked;
    
    private void Awake()
    {
        InitializeComponents();
        InitializePool();
        SetupScrollRect();
    }
    
    private void Start()
    {
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>();
        
        if (configsHandler != null)
        {
            LoadCharacters();
        }
        else
        {
            Debug.LogError("[VirtualCharacterList] ConfigsHandler not found!");
        }
    }
    
    private void Update()
    {
        // Throttle updates for performance
        framesSinceLastUpdate++;
        if (framesSinceLastUpdate >= UPDATE_FREQUENCY)
        {
            framesSinceLastUpdate = 0;
            UpdateVisibleItems();
        }
    }
    
    /// <summary>
    /// Initialize UI components and references
    /// </summary>
    private void InitializeComponents()
    {
        // Auto-find components if not assigned
        if (scrollRect == null)
            scrollRect = GetComponent<ScrollRect>();
        
        if (content == null && scrollRect != null)
            content = scrollRect.content;
        
        if (viewport == null && scrollRect != null)
            viewport = scrollRect.viewport;
        
        if (characterUIPrefab == null)
            characterUIPrefab = Resources.Load<GameObject>("Prefabs/CharacterValues");
        
        // Validate required components
        if (scrollRect == null || content == null || viewport == null)
        {
            Debug.LogError("[VirtualCharacterList] Missing required UI components!");
            enabled = false;
            return;
        }
        
        if (characterUIPrefab == null)
        {
            Debug.LogError("[VirtualCharacterList] CharacterValues prefab not found!");
            enabled = false;
            return;
        }
    }
    
    /// <summary>
    /// Initialize the object pool for character UI elements
    /// </summary>
    private void InitializePool()
    {
        uiPool = new CharacterUIPool(characterUIPrefab, content, maxPoolSize);
    }
    
    /// <summary>
    /// Setup scroll rect event handlers
    /// </summary>
    private void SetupScrollRect()
    {
        if (scrollRect != null)
        {
            scrollRect.onValueChanged.AddListener(OnScrollValueChanged);
            scrollRect.vertical = true;
            scrollRect.horizontal = false;
            scrollRect.movementType = ScrollRect.MovementType.Clamped;
        }
    }
    
    /// <summary>
    /// Load characters from ConfigsHandler and initialize virtual list
    /// </summary>
    public void LoadCharacters()
    {
        if (configsHandler == null) return;
        
        allCharacters = configsHandler.GetCharacters();
        
        Debug.Log($"[VirtualCharacterList] Loaded {allCharacters.Count} characters");
        
        // Clear filter cache and mark as dirty
        filterCache.Clear();
        isFilterDirty = true;
        
        // Apply current filter
        ApplyFilter(currentFilter);
        
        // Update content size and refresh display
        UpdateContentSize();
        RefreshVisibleItems();
    }
    
    /// <summary>
    /// Apply filter to character list with caching
    /// </summary>
    public void ApplyFilter(string filter = "")
    {
        currentFilter = filter.ToLower().Trim();
        
        // Use cached result if available
        if (enableCachedFiltering && filterCache.ContainsKey(currentFilter))
        {
            filteredCharacters = filterCache[currentFilter];
            Debug.Log($"[VirtualCharacterList] Used cached filter result: {filteredCharacters.Count} characters");
        }
        else
        {
            // Compute filtered list
            if (string.IsNullOrEmpty(currentFilter))
            {
                filteredCharacters = new List<BattleCharacter>(allCharacters);
            }
            else
            {
                filteredCharacters = allCharacters.Where(c => 
                    c.name.ToLower().Contains(currentFilter) ||
                    c.id.Contains(currentFilter) ||
                    c.level.ToString().Contains(currentFilter)
                ).ToList();
            }
            
            // Cache the result
            if (enableCachedFiltering)
            {
                filterCache[currentFilter] = new List<BattleCharacter>(filteredCharacters);
            }
            
            Debug.Log($"[VirtualCharacterList] Filtered {allCharacters.Count} -> {filteredCharacters.Count} characters");
        }
        
        isFilterDirty = false;
        UpdateContentSize();
        RefreshVisibleItems();
    }
    
    /// <summary>
    /// Update the content size based on filtered character count
    /// </summary>
    private void UpdateContentSize()
    {
        totalContentHeight = filteredCharacters.Count * (itemHeight + itemSpacing);
        content.sizeDelta = new Vector2(content.sizeDelta.x, totalContentHeight);
    }
    
    /// <summary>
    /// Handle scroll value changes
    /// </summary>
    private void OnScrollValueChanged(Vector2 scrollPosition)
    {
        // Update will handle the actual item refresh for performance
    }
    
    /// <summary>
    /// Update which items should be visible based on scroll position
    /// </summary>
    private void UpdateVisibleItems()
    {
        if (filteredCharacters.Count == 0) return;
        
        // Calculate visible range based on scroll position
        float scrollY = content.anchoredPosition.y;
        float viewportHeight = viewport.rect.height;
        
        // Calculate first and last visible indices with buffer
        int newFirstIndex = Mathf.Max(0, Mathf.FloorToInt(scrollY / (itemHeight + itemSpacing)) - bufferItemCount);
        int newLastIndex = Mathf.Min(filteredCharacters.Count - 1, 
            Mathf.CeilToInt((scrollY + viewportHeight) / (itemHeight + itemSpacing)) + bufferItemCount);
        
        // Only update if range changed
        if (newFirstIndex != firstVisibleIndex || newLastIndex != lastVisibleIndex)
        {
            firstVisibleIndex = newFirstIndex;
            lastVisibleIndex = newLastIndex;
            RefreshVisibleItems();
        }
    }
    
    /// <summary>
    /// Refresh the visible items display
    /// </summary>
    private void RefreshVisibleItems()
    {
        // Return all active items to pool
        foreach (var item in activeItems)
        {
            uiPool.ReturnItem(item);
        }
        activeItems.Clear();
        
        // Create items for visible range
        for (int i = firstVisibleIndex; i <= lastVisibleIndex && i < filteredCharacters.Count; i++)
        {
            var character = filteredCharacters[i];
            var uiItem = uiPool.GetItem();
            
            if (uiItem != null)
            {
                uiItem.SetCharacter(character, i);
                uiItem.SetPosition(i * (itemHeight + itemSpacing));
                uiItem.SetCallbacks(OnCharacterSelected, OnCharacterStatsClicked, OnCharacterSkillsClicked, OnCharacterModsClicked);
                activeItems.Add(uiItem);
            }
        }
        
        Debug.Log($"[VirtualCharacterList] Displaying {activeItems.Count} items (indices {firstVisibleIndex}-{lastVisibleIndex})");
    }
    
    /// <summary>
    /// Refresh a specific character's UI if it's currently visible
    /// </summary>
    public void RefreshCharacter(BattleCharacter character)
    {
        int index = filteredCharacters.IndexOf(character);
        if (index >= firstVisibleIndex && index <= lastVisibleIndex)
        {
            var activeItem = activeItems.Find(item => item.GetCharacter() == character);
            activeItem?.RefreshDisplay();
        }
    }
    
    /// <summary>
    /// Scroll to a specific character
    /// </summary>
    public void ScrollToCharacter(BattleCharacter character)
    {
        int index = filteredCharacters.IndexOf(character);
        if (index >= 0)
        {
            float targetY = index * (itemHeight + itemSpacing);
            content.anchoredPosition = new Vector2(content.anchoredPosition.x, targetY);
        }
    }
    
    /// <summary>
    /// Get performance statistics
    /// </summary>
    public VirtualListStats GetStats()
    {
        return new VirtualListStats
        {
            TotalCharacters = allCharacters.Count,
            FilteredCharacters = filteredCharacters.Count,
            VisibleItems = activeItems.Count,
            PooledItems = uiPool.GetPoolSize(),
            CacheEntries = filterCache.Count,
            MemoryReduction = (float)(allCharacters.Count - activeItems.Count) / allCharacters.Count
        };
    }
    
    private void OnDestroy()
    {
        if (scrollRect != null)
        {
            scrollRect.onValueChanged.RemoveListener(OnScrollValueChanged);
        }
        
        uiPool?.Dispose();
    }
}

/// <summary>
/// Performance statistics for the virtual list
/// </summary>
[System.Serializable]
public struct VirtualListStats
{
    public int TotalCharacters;
    public int FilteredCharacters;
    public int VisibleItems;
    public int PooledItems;
    public int CacheEntries;
    public float MemoryReduction;
}
