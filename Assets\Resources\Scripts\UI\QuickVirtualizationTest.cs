using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Quick test to verify virtualization components work without ScrollRect errors
/// </summary>
public class QuickVirtualizationTest : MonoBehaviour
{
    [Header("Quick Test Settings")]
    [SerializeField] private bool runOnStart = true;
    
    void Start()
    {
        if (runOnStart)
        {
            TestVirtualizationComponents();
        }
    }
    
    /// <summary>
    /// Tests virtualization components for basic functionality
    /// </summary>
    public void TestVirtualizationComponents()
    {
        Debug.Log("=== QUICK VIRTUALIZATION TEST ===");
        
        // Test 1: VirtualizedCharacterList without ScrollRect
        TestVirtualizedListWithoutScrollRect();
        
        // Test 2: VirtualizedAddCharacter basic functionality
        TestVirtualizedAddCharacter();
        
        // Test 3: Check existing components
        TestExistingComponents();
        
        Debug.Log("=== QUICK TEST COMPLETED ===");
    }
    
    /// <summary>
    /// Tests VirtualizedCharacterList without ScrollRect
    /// </summary>
    private void TestVirtualizedListWithoutScrollRect()
    {
        Debug.Log("[QUICK_TEST] 🧪 Testing VirtualizedCharacterList without ScrollRect...");
        
        try
        {
            GameObject testObj = new GameObject("TestVirtualizedList");
            testObj.AddComponent<RectTransform>(); // Add RectTransform for UI
            
            VirtualizedCharacterList testList = testObj.AddComponent<VirtualizedCharacterList>();
            testList.InitializeVirtualization();
            
            Debug.Log("[QUICK_TEST] ✅ VirtualizedCharacterList initialized successfully without ScrollRect");
            
            DestroyImmediate(testObj);
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[QUICK_TEST] ❌ VirtualizedCharacterList failed: {e.Message}");
        }
    }
    
    /// <summary>
    /// Tests VirtualizedAddCharacter basic functionality
    /// </summary>
    private void TestVirtualizedAddCharacter()
    {
        Debug.Log("[QUICK_TEST] 🧪 Testing VirtualizedAddCharacter...");
        
        try
        {
            // Create a test parent with RectTransform
            GameObject parentObj = new GameObject("TestCharactersInfo");
            parentObj.AddComponent<RectTransform>();
            
            GameObject testObj = new GameObject("TestVirtualizedAddChar");
            VirtualizedAddCharacter testAddChar = testObj.AddComponent<VirtualizedAddCharacter>();
            
            // Set the parent manually for testing
            testAddChar.GetType().GetField("charactersInfoParent", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)?.SetValue(testAddChar, parentObj.transform);
            
            // Find ConfigsHandler for testing
            ConfigsHandler configsHandler = FindFirstObjectByType<ConfigsHandler>();
            if (configsHandler != null)
            {
                testAddChar.GetType().GetField("configsHandler", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)?.SetValue(testAddChar, configsHandler);
            }
            
            testAddChar.InitializeVirtualizedSystem();
            
            Debug.Log("[QUICK_TEST] ✅ VirtualizedAddCharacter initialized successfully");
            
            DestroyImmediate(testObj);
            DestroyImmediate(parentObj);
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[QUICK_TEST] ❌ VirtualizedAddCharacter failed: {e.Message}");
        }
    }
    
    /// <summary>
    /// Tests existing components in the scene
    /// </summary>
    private void TestExistingComponents()
    {
        Debug.Log("[QUICK_TEST] 🧪 Testing existing components...");
        
        // Find existing VirtualizedCharacterList components
        VirtualizedCharacterList[] existingLists = FindObjectsByType<VirtualizedCharacterList>(FindObjectsSortMode.None);
        Debug.Log($"[QUICK_TEST] 📊 Found {existingLists.Length} VirtualizedCharacterList components");
        
        foreach (var list in existingLists)
        {
            try
            {
                list.InitializeWhenReady();
                Debug.Log($"[QUICK_TEST] ✅ {list.name} initialized successfully");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[QUICK_TEST] ❌ {list.name} failed: {e.Message}");
            }
        }
        
        // Find existing VirtualizedAddCharacter components
        VirtualizedAddCharacter[] existingAddChars = FindObjectsByType<VirtualizedAddCharacter>(FindObjectsSortMode.None);
        Debug.Log($"[QUICK_TEST] 📊 Found {existingAddChars.Length} VirtualizedAddCharacter components");
        
        // Check ConfigsHandler
        ConfigsHandler configsHandler = FindFirstObjectByType<ConfigsHandler>();
        if (configsHandler != null)
        {
            var characters = configsHandler.GetCharacters();
            if (characters != null)
            {
                Debug.Log($"[QUICK_TEST] 📊 ConfigsHandler has {characters.Count} characters");
            }
            else
            {
                Debug.LogWarning("[QUICK_TEST] ⚠️ ConfigsHandler.GetCharacters() returned null");
            }
        }
        else
        {
            Debug.LogWarning("[QUICK_TEST] ⚠️ ConfigsHandler not found");
        }
        
        // Check for ScrollRect components
        ScrollRect[] scrollRects = FindObjectsByType<ScrollRect>(FindObjectsSortMode.None);
        Debug.Log($"[QUICK_TEST] 📊 Found {scrollRects.Length} ScrollRect components in scene");
        
        foreach (var scrollRect in scrollRects)
        {
            Debug.Log($"[QUICK_TEST] 📜 ScrollRect: {scrollRect.name} (Content: {(scrollRect.content != null ? scrollRect.content.name : "null")})");
        }
    }
    
    /// <summary>
    /// Manual test trigger
    /// </summary>
    [ContextMenu("Run Quick Test")]
    public void RunQuickTest()
    {
        TestVirtualizationComponents();
    }
    
    /// <summary>
    /// Test only VirtualizedCharacterList
    /// </summary>
    [ContextMenu("Test VirtualizedCharacterList Only")]
    public void TestVirtualizedListOnly()
    {
        TestVirtualizedListWithoutScrollRect();
    }
    
    /// <summary>
    /// Test only VirtualizedAddCharacter
    /// </summary>
    [ContextMenu("Test VirtualizedAddCharacter Only")]
    public void TestVirtualizedAddCharOnly()
    {
        TestVirtualizedAddCharacter();
    }
    
    /// <summary>
    /// Test only existing components
    /// </summary>
    [ContextMenu("Test Existing Components Only")]
    public void TestExistingComponentsOnly()
    {
        TestExistingComponents();
    }
}
