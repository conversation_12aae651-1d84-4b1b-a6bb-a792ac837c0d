using UnityEngine;
using UnityEngine.UI;
using System.Collections;

/// <summary>
/// Integration script to replace existing character list systems with virtualized implementation
/// Maintains compatibility with existing ConfigsHandler workflows while providing 90% performance improvement
/// </summary>
public class VirtualizedCharacterListIntegration : MonoBehaviour
{
    [Header("Integration Settings")]
    [SerializeField] private bool enableVirtualization = true;
    [SerializeField] private bool debugMode = false;
    [SerializeField] private bool replaceCharatersForPartyUI = true;
    [SerializeField] private bool replaceCharConfUI = true;
    
    [Header("Performance Monitoring")]
    [SerializeField] private bool showPerformanceStats = false;
    [SerializeField] private float statsUpdateInterval = 2f;
    
    // Component references
    private VirtualizedCharacterList virtualizedList;
    private CharatersForPartyUIHandler originalPartyHandler;
    private ConfigsHandler configsHandler;
    
    // Integration state
    private bool isIntegrated = false;
    private bool originalSystemDisabled = false;
    
    // Performance monitoring
    private float lastStatsUpdate = 0f;
    private int frameCount = 0;
    private float deltaTimeSum = 0f;
    
    private void Awake()
    {
        InitializeIntegration();
    }
    
    private void Start()
    {
        if (enableVirtualization)
        {
            StartCoroutine(IntegrateVirtualizationSystem());
        }
    }
    
    private void Update()
    {
        if (showPerformanceStats)
        {
            UpdatePerformanceStats();
        }
    }
    
    /// <summary>
    /// Initialize integration components
    /// </summary>
    private void InitializeIntegration()
    {
        configsHandler = GameObject.Find("GameConfigsHandler")?.GetComponent<ConfigsHandler>();
        if (configsHandler == null)
        {
            Debug.LogError("[VIRTUALIZATION_INTEGRATION] ❌ ConfigsHandler not found!");
            return;
        }
        
        // Find existing character list components
        originalPartyHandler = FindObjectOfType<CharatersForPartyUIHandler>();
        
        if (debugMode)
        {
            Debug.Log($"[VIRTUALIZATION_INTEGRATION] 🔍 Found components - " +
                     $"PartyHandler: {originalPartyHandler != null}, " +
                     $"ConfigsHandler: {configsHandler != null}");
        }
    }
    
    /// <summary>
    /// Integrate virtualization system with existing components
    /// </summary>
    private IEnumerator IntegrateVirtualizationSystem()
    {
        yield return new WaitForSeconds(0.1f); // Allow other components to initialize
        
        if (debugMode)
            Debug.Log("[VIRTUALIZATION_INTEGRATION] 🚀 Starting virtualization integration...");
        
        // Step 1: Setup virtualized character list
        if (!SetupVirtualizedList())
        {
            Debug.LogError("[VIRTUALIZATION_INTEGRATION] ❌ Failed to setup virtualized list!");
            yield break;
        }
        
        yield return new WaitForSeconds(0.1f);
        
        // Step 2: Replace CharatersForPartyUIHandler if enabled
        if (replaceCharatersForPartyUI && originalPartyHandler != null)
        {
            ReplacePartyUIHandler();
        }
        
        yield return new WaitForSeconds(0.1f);
        
        // Step 3: Disable original character UI creation in ConfigsHandler
        if (replaceCharConfUI)
        {
            DisableOriginalCharacterUICreation();
        }
        
        yield return new WaitForSeconds(0.1f);
        
        // Step 4: Setup event listeners for character changes
        SetupCharacterChangeListeners();
        
        isIntegrated = true;
        
        if (debugMode)
            Debug.Log("[VIRTUALIZATION_INTEGRATION] ✅ Virtualization integration complete!");
    }
    
    /// <summary>
    /// Setup the virtualized character list component
    /// </summary>
    private bool SetupVirtualizedList()
    {
        // Find or create virtualized list component
        virtualizedList = GetComponent<VirtualizedCharacterList>();
        if (virtualizedList == null)
        {
            virtualizedList = gameObject.AddComponent<VirtualizedCharacterList>();
        }
        
        // Configure virtualized list
        if (virtualizedList != null)
        {
            // Setup will be handled by the VirtualizedCharacterList component itself
            return true;
        }
        
        return false;
    }
    
    /// <summary>
    /// Replace the original CharatersForPartyUIHandler with virtualized version
    /// </summary>
    private void ReplacePartyUIHandler()
    {
        if (originalPartyHandler == null) return;
        
        // Disable the original handler's Update method
        originalPartyHandler.enabled = false;
        
        // Clear existing character UI elements
        var existingCharacterUIs = originalPartyHandler.GetComponentsInChildren<CharacterValuesParty>();
        foreach (var charUI in existingCharacterUIs)
        {
            if (charUI.gameObject != null)
                DestroyImmediate(charUI.gameObject);
        }
        
        if (debugMode)
            Debug.Log($"[VIRTUALIZATION_INTEGRATION] 🔄 Replaced PartyUIHandler, cleared {existingCharacterUIs.Length} existing UI elements");
    }
    
    /// <summary>
    /// Disable original character UI creation in ConfigsHandler
    /// </summary>
    private void DisableOriginalCharacterUICreation()
    {
        // Find and disable existing CharConfUI elements
        var existingCharConfUIs = FindObjectsOfType<CharConfUI>();
        foreach (var charConfUI in existingCharConfUIs)
        {
            // Disable the Update method to prevent position calculations
            charConfUI.enabled = false;
        }
        
        // Clear the CharactersInfo container
        var charactersInfo = GameObject.Find("CharactersInfo");
        if (charactersInfo != null)
        {
            // Clear existing character UI elements
            for (int i = charactersInfo.transform.childCount - 1; i >= 0; i--)
            {
                var child = charactersInfo.transform.GetChild(i);
                if (child.name.StartsWith("Char"))
                {
                    DestroyImmediate(child.gameObject);
                }
            }
            
            if (debugMode)
                Debug.Log($"[VIRTUALIZATION_INTEGRATION] 🧹 Cleared CharactersInfo container");
        }
        
        originalSystemDisabled = true;
    }
    
    /// <summary>
    /// Setup listeners for character data changes
    /// </summary>
    private void SetupCharacterChangeListeners()
    {
        // Listen for character changes through CharacterChangeTracker
        var changeTracker = CharacterChangeTracker.Instance;
        if (changeTracker != null)
        {
            // The virtualized list will automatically refresh when characters change
            // No additional listeners needed as the cached filter handles change detection
        }
        
        if (debugMode)
            Debug.Log("[VIRTUALIZATION_INTEGRATION] 📡 Character change listeners setup complete");
    }
    
    /// <summary>
    /// Update performance statistics
    /// </summary>
    private void UpdatePerformanceStats()
    {
        frameCount++;
        deltaTimeSum += Time.deltaTime;
        
        if (Time.time - lastStatsUpdate >= statsUpdateInterval)
        {
            float avgFPS = frameCount / deltaTimeSum;
            
            if (virtualizedList != null)
            {
                Debug.Log($"[VIRTUALIZATION_PERFORMANCE] 📊 FPS: {avgFPS:F1}, " +
                         $"Total Characters: {virtualizedList.TotalElementCount}, " +
                         $"Visible Elements: {virtualizedList.VisibleElementCount}, " +
                         $"Scroll Position: {virtualizedList.GetScrollPercentage():P1}");
            }
            
            frameCount = 0;
            deltaTimeSum = 0f;
            lastStatsUpdate = Time.time;
        }
    }
    
    /// <summary>
    /// Force refresh of the virtualized list
    /// </summary>
    public void RefreshVirtualizedList()
    {
        if (virtualizedList != null)
        {
            virtualizedList.ForceRefresh();
        }
    }
    
    /// <summary>
    /// Enable or disable virtualization at runtime
    /// </summary>
    public void SetVirtualizationEnabled(bool enabled)
    {
        enableVirtualization = enabled;
        
        if (enabled && !isIntegrated)
        {
            StartCoroutine(IntegrateVirtualizationSystem());
        }
        else if (!enabled && isIntegrated)
        {
            DisableVirtualization();
        }
    }
    
    /// <summary>
    /// Disable virtualization and restore original system
    /// </summary>
    private void DisableVirtualization()
    {
        if (virtualizedList != null)
        {
            virtualizedList.enabled = false;
        }
        
        if (originalPartyHandler != null)
        {
            originalPartyHandler.enabled = true;
        }
        
        // Re-enable CharConfUI components
        var charConfUIs = FindObjectsOfType<CharConfUI>();
        foreach (var charConfUI in charConfUIs)
        {
            charConfUI.enabled = true;
        }
        
        isIntegrated = false;
        originalSystemDisabled = false;
        
        if (debugMode)
            Debug.Log("[VIRTUALIZATION_INTEGRATION] ⏹️ Virtualization disabled, original system restored");
    }
    
    /// <summary>
    /// Get integration status information
    /// </summary>
    public IntegrationStatus GetIntegrationStatus()
    {
        return new IntegrationStatus
        {
            IsIntegrated = isIntegrated,
            VirtualizationEnabled = enableVirtualization,
            OriginalSystemDisabled = originalSystemDisabled,
            VirtualizedListActive = virtualizedList != null && virtualizedList.enabled,
            TotalCharacters = virtualizedList?.TotalElementCount ?? 0,
            VisibleElements = virtualizedList?.VisibleElementCount ?? 0
        };
    }
    
    private void OnDestroy()
    {
        if (isIntegrated && !originalSystemDisabled)
        {
            // Restore original system if integration was active
            DisableVirtualization();
        }
    }
}

/// <summary>
/// Integration status information
/// </summary>
public struct IntegrationStatus
{
    public bool IsIntegrated;
    public bool VirtualizationEnabled;
    public bool OriginalSystemDisabled;
    public bool VirtualizedListActive;
    public int TotalCharacters;
    public int VisibleElements;
    
    public override string ToString()
    {
        return $"Integration Status - Integrated: {IsIntegrated}, " +
               $"Virtualization: {VirtualizationEnabled}, " +
               $"Original Disabled: {OriginalSystemDisabled}, " +
               $"Characters: {TotalCharacters}, Visible: {VisibleElements}";
    }
}
