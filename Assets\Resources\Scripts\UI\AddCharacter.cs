using UnityEngine;

public class Add<PERSON>haracter
{
    readonly GameObject valuesPrefab;
    readonly Configs<PERSON>andler configs<PERSON>andler;
    readonly Transform valuesParentTransform;

    public AddCharacter(int index, ConfigsHandler cH)
    {
        configsHandler = cH;

        // Check if we should use the virtualized system
        VirtualizedCharacterIntegration integration = Object.FindObjectOfType<VirtualizedCharacterIntegration>();

        if (integration != null && integration.IsUsingVirtualizedSystem)
        {
            // Virtualized system handles UI creation automatically
            // Just notify the integration that a character was added
            BattleCharacter character = configsHandler.GetCharacter(index);
            if (character != null)
            {
                integration.OnCharacterAdded(character);
            }
            return;
        }

        // Legacy system: Create individual UI elements
        CreateLegacyCharacterUI(index);
    }

    void CreateLegacyCharacterUI(int index)
    {
        // Get the parent transform
        valuesParentTransform = GameObject.Find("CharactersInfo").transform;

        if (valuesParentTransform == null)
        {
            Debug.LogError("AddCharacter: CharactersInfo parent not found!");
            return;
        }

        // Load the prefab
        valuesPrefab = Resources.Load<GameObject>("Prefabs/CharacterValues");

        if (valuesPrefab == null)
        {
            Debug.LogError("AddCharacter: CharacterValues prefab not found!");
            return;
        }

        // Instantiate the prefab
        GameObject values = Object.Instantiate(valuesPrefab, valuesParentTransform);

        // Set the name
        values.name = "Char" + index;

        // Get and configure the CharConfUI component
        CharConfUI charConfUI = values.GetComponent<CharConfUI>();
        if (charConfUI != null)
        {
            charConfUI.character = configsHandler.GetCharacter(index);
            charConfUI.useVirtualizedPositioning = false; // Ensure legacy positioning
        }
        else
        {
            Debug.LogError("AddCharacter: CharConfUI component not found on instantiated prefab!");
        }
    }
}
