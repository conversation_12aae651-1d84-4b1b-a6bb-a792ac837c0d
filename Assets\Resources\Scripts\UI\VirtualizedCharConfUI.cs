using UnityEngine;
using UnityEngine.UI;
using TMPro;

/// <summary>
/// Virtualized version of CharConfUI optimized for object pooling and performance
/// Disables expensive Update() operations and provides manual refresh methods
/// </summary>
public class VirtualizedCharConfUI : MonoBehaviour
{
    [Header("Virtualization Settings")]
    [SerializeField] private bool enableAutoUpdate = false; // Disabled for virtualization
    [SerializeField] private bool debugMode = false;
    
    public BattleCharacter character; // the character that this UI represents
    private BattleCharacter tempCharacter; // the temporary character to store the values
    
    readonly float ySpacing = 0.5f; // the spacing between the characters in the UI
    
    ConfigsHandler configsHandler; // the ConfigsHandler script
    GameObject fatherObject; // the father object of the UI
    
    GameObject enemy, _name, stats, skills, mods, level; // the game objects of the UI
    GameObject Stats, Skills, Mods; // the game objects of the Stats, Skills and Mods UI
    
    Button enemyB, statsB, skillsB, modsB; // the buttons of the UI
    TMP_InputField nameI, levelI; // the input fields of the UI
    
    // Virtualization state
    private bool isInitialized = false;
    private bool isDirty = false;
    private int lastCharacterIndex = -1;
    
    void Start()
    {
        InitializeComponents();
    }
    
    void Update()
    {
        // Only update if auto-update is enabled (disabled for virtualization)
        if (enableAutoUpdate)
        {
            UpdateCharacterDisplay();
        }
    }
    
    /// <summary>
    /// Initialize UI components (called once)
    /// </summary>
    private void InitializeComponents()
    {
        if (isInitialized) return;
        
        // gets the ConfigsHandler script
        configsHandler = GameObject.Find("GameConfigsHandler").GetComponent<ConfigsHandler>();
        
        // gets the father object
        fatherObject = GameObject.Find("CharactersInfo");
        
        // gets the game objects of the UI
        enemy = transform.GetChild(0).gameObject;
        _name = transform.GetChild(1).gameObject;
        stats = transform.GetChild(2).gameObject;
        skills = transform.GetChild(3).gameObject;
        mods = transform.GetChild(4).gameObject;
        level = transform.GetChild(5).gameObject;
        
        // gets the game objects of the Stats, Skills and Mods UI
        Stats = GameObject.Find("StatsValues");
        Skills = GameObject.Find("SkillsValues");
        Mods = GameObject.Find("ModsValues");
        
        // gets the input of the UI
        enemyB = enemy.GetComponent<Button>();
        nameI = _name.GetComponent<TMP_InputField>();
        statsB = stats.GetComponent<Button>();
        skillsB = skills.GetComponent<Button>();
        modsB = mods.GetComponent<Button>();
        levelI = level.GetComponent<TMP_InputField>();
        
        // adds the listeners to the buttons
        AddButtonListener(enemyB, ChangeCharacterType);
        AddButtonListener(statsB, ValueButtonClick);
        AddButtonListener(skillsB, ValueButtonClick);
        AddButtonListener(modsB, ValueButtonClick);
        
        // adds the listeners to the input fields
        AddTextEditFinishedListener(nameI, ChangeCharacterName);
        AddTextEditFinishedListener(levelI, ChangeCharacterlevel);
        
        isInitialized = true;
        
        if (debugMode)
            Debug.Log("[VIRTUALIZED_CHAR_CONF] ✅ Components initialized");
    }
    
    /// <summary>
    /// Set character data and refresh display (called by virtualization system)
    /// </summary>
    /// <param name="newCharacter">Character to display</param>
    /// <param name="characterIndex">Index in the list for positioning</param>
    public void SetCharacter(BattleCharacter newCharacter, int characterIndex)
    {
        if (!isInitialized)
            InitializeComponents();
            
        character = newCharacter;
        lastCharacterIndex = characterIndex;
        isDirty = true;
        
        RefreshDisplay();
    }
    
    /// <summary>
    /// Refresh the character display manually (replaces Update() for virtualization)
    /// </summary>
    public void RefreshDisplay()
    {
        if (!isInitialized || character == null) return;
        
        // Update character data
        if (configsHandler != null)
        {
            // Get most updated value of the character
            var updatedCharacter = configsHandler.GetCharacterByID(character.id);
            if (updatedCharacter != null)
                character = updatedCharacter;
        }
        
        // Update UI elements
        UpdateCharacterDisplay();
        isDirty = false;
    }
    
    /// <summary>
    /// Update character display elements
    /// </summary>
    private void UpdateCharacterDisplay()
    {
        if (character == null) return;
        
        // Update input fields
        if (nameI != null && nameI.text != character.name)
            nameI.text = character.name;
            
        if (levelI != null && levelI.text != character.level.ToString())
            levelI.text = character.level.ToString();
        
        // Update enemy button state
        if (enemy != null)
            enemy.transform.GetChild(0).gameObject.SetActive(character.isEnemy);
    }
    
    /// <summary>
    /// Set position manually (called by virtualization system)
    /// </summary>
    /// <param name="position">World position to set</param>
    public void SetPosition(Vector3 position)
    {
        transform.position = position;
    }
    
    /// <summary>
    /// Set local position manually (called by virtualization system)
    /// </summary>
    /// <param name="localPosition">Local position to set</param>
    public void SetLocalPosition(Vector3 localPosition)
    {
        transform.localPosition = localPosition;
    }
    
    /// <summary>
    /// Reset UI element for object pooling
    /// </summary>
    public void ResetForPooling()
    {
        character = null;
        tempCharacter = null;
        lastCharacterIndex = -1;
        isDirty = false;
        
        // Reset UI elements
        if (nameI != null) nameI.text = "";
        if (levelI != null) levelI.text = "";
        if (enemy != null) enemy.transform.GetChild(0).gameObject.SetActive(false);
        
        // Hide value panels
        if (Stats != null) Stats.SetActive(false);
        if (Skills != null) Skills.SetActive(false);
        if (Mods != null) Mods.SetActive(false);
    }
    
    /// <summary>
    /// Check if character data needs refreshing
    /// </summary>
    public bool NeedsRefresh()
    {
        return isDirty || (character != null && configsHandler != null && 
               configsHandler.GetCharacterByID(character.id) != character);
    }
    
    // Original CharConfUI methods (preserved for compatibility)
    
    void AddButtonListener(Button button, System.Action action)
    {
        if (button != null)
            button.onClick.AddListener(() => action());
    }
    
    void AddTextEditFinishedListener(TMP_InputField inputField, System.Action action)
    {
        if (inputField != null)
            inputField.onEndEdit.AddListener((value) => action());
    }
    
    void ValueButtonClick()
    {
        Button self = UnityEngine.EventSystems.EventSystem.current.currentSelectedGameObject.GetComponent<Button>();
        
        // Disable all other value panels
        if (Stats != null) Stats.SetActive(false);
        if (Skills != null) Skills.SetActive(false);
        if (Mods != null) Mods.SetActive(false);
        
        // Disable all button highlights
        if (statsB != null) statsB.transform.GetChild(0).gameObject.SetActive(false);
        if (skillsB != null) skillsB.transform.GetChild(0).gameObject.SetActive(false);
        if (modsB != null) modsB.transform.GetChild(0).gameObject.SetActive(false);
        
        // Enable the selected panel and highlight
        if (self != null)
        {
            self.transform.GetChild(0).gameObject.SetActive(true);
            
            switch (self.gameObject.name)
            {
                case "Stats":
                    if (Stats != null)
                    {
                        Stats.SetActive(true);
                        var statsDisplay = Stats.GetComponent<StatsValueDisplay>();
                        if (statsDisplay != null)
                            statsDisplay.SetValues(character, this);
                    }
                    break;
                case "Skills":
                    if (Skills != null)
                    {
                        Skills.SetActive(true);
                        var skillsDisplay = Skills.GetComponent<SkillsValueDisplay>();
                        if (skillsDisplay != null)
                            skillsDisplay.SetValues(character, this);
                    }
                    break;
                case "Mods":
                    if (Mods != null)
                    {
                        Mods.SetActive(true);
                        var modsDisplay = Mods.GetComponent<ModsValueDisplay>();
                        if (modsDisplay != null)
                            modsDisplay.SetValues(character, this);
                    }
                    break;
            }
        }
    }
    
    void ChangeCharacterType()
    {
        if (character != null && configsHandler != null)
        {
            character.isEnemy = !character.isEnemy;
            int characterIndex = configsHandler.GetCharacter(character);
            if (characterIndex != -1)
            {
                configsHandler.SetCharacter(character, characterIndex);
            }
            RefreshDisplay();
        }
    }

    void ChangeCharacterName()
    {
        if (character != null && configsHandler != null && nameI != null)
        {
            character.name = nameI.text;
            int characterIndex = configsHandler.GetCharacter(character);
            if (characterIndex != -1)
            {
                configsHandler.SetCharacter(character, characterIndex);
            }
        }
    }

    void ChangeCharacterlevel()
    {
        if (character != null && configsHandler != null && levelI != null)
        {
            if (int.TryParse(levelI.text, out int newLevel))
            {
                character.SetLevel(newLevel);
                int characterIndex = configsHandler.GetCharacter(character);
                if (characterIndex != -1)
                {
                    configsHandler.SetCharacter(character, characterIndex);
                }
            }
        }
    }
    
    // Additional methods from original CharConfUI can be added here as needed
    // (Truncated for brevity - add remaining methods if required)
}
